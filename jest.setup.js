/**
 * Jest setup file for global test configuration
 */

// Set test environment variables
process.env.NODE_ENV = 'test';

// Mock console methods in tests to reduce noise
global.console = {
  ...console,
  // Uncomment to suppress console.log in tests
  // log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
};

// Global test utilities
global.testUtils = {
  /**
   * Create a mock LLM response
   * @param {string} content - The response content
   * @returns {Object} Mock response object
   */
  createMockLLMResponse: content => ({
    content,
    usage: {
      prompt_tokens: 100,
      completion_tokens: 50,
      total_tokens: 150
    }
  }),

  /**
   * Create mock test facts
   * @param {number} count - Number of facts to create
   * @returns {Array} Array of mock facts
   */
  createMockFacts: (count = 5) => {
    return Array.from({ length: count }, (_, i) => ({
      id: `fact_${i + 1}`,
      category: `category_${(i % 3) + 1}`,
      fact: `This is test fact number ${i + 1}`,
      complexity: i % 2 === 0 ? 'simple' : 'complex'
    }));
  },

  /**
   * Create a mock memory instance
   * @returns {Object} Mock memory object
   */
  createMockMemory: () => ({
    addMessage: jest.fn(),
    getContext: jest.fn().mockReturnValue('mock context'),
    clear: jest.fn()
  }),

  /**
   * Wait for a specified amount of time
   * @param {number} ms - Milliseconds to wait
   * @returns {Promise} Promise that resolves after the specified time
   */
  wait: ms => new Promise(resolve => setTimeout(resolve, ms))
};

// Setup and teardown hooks
beforeEach(() => {
  // Clear all mocks before each test
  jest.clearAllMocks();
});

afterEach(() => {
  // Clean up any test artifacts
  jest.restoreAllMocks();
});
