# LLM Memory Test Results

## Memory Retention Visualization

```
```
## Test Configuration

- **Session ID**: session-123
- **Memory Type**: undefined
- **Test Scenario**: casual
- **Facts Tested**: undefined
- **Messages Between Facts**: undefined

## Models Used

- **User Model**: undefined
- **Assistant Model**: undefined
- **Evaluator Model**: undefined
- **Summary Model**: undefined
- **Knowledge Extraction Model**: undefined

## Test Results

- **Overall Score**: 75.00%

## Performance Metrics

### Test Execution Performance

- **Total Duration**: 10.00s
- **Operations Completed**: 8/10
- **Operation Success Rate**: 80%
- **Average Operation Duration**: 1250ms
- **Longest Operation**: fact-evaluation (2000ms)
- **Shortest Operation**: memory-context (100ms)

### API Performance

- **Total API Calls**: 20
- **API Success Rate**: 95%
- **Total Tokens Used**: 5,000
- **Average Response Time**: 800ms
- **Total Response Time**: 16.00s

#### Per-Model API Statistics

**gpt-4**:
- Calls: 10 (100% success)
- Tokens: 3,000 (avg: 300)
- Response Time: 900ms avg (500-1200ms range)


## Evaluation Results

## Conversation Log

