# Configuration Documentation

This document describes all available configuration options for the LLM Memory Test Application.

## API Configuration

Configure API access for LLM providers.

| Environment Variable | Default | Description |
|---------------------|---------|-------------|
| `OPENROUTER_API_KEY` | - | API key for OpenRouter (preferred) |
| `OPENAI_API_KEY` | - | API key for OpenAI (alternative) |

**Note:** At least one API key must be provided.

## Model Configuration

Configure LLM models for different roles in the application.

| Environment Variable | Default | Range | Description |
|---------------------|---------|-------|-------------|
| `USER_MODEL` | anthropic/claude-3-haiku:beta | - | Model name for user role |
| `USER_TEMPERATURE` | 0.8 | 0.0-2.0 | Temperature for user model |
| `USER_MAX_TOKENS` | 150 | 1-4000 | Max tokens for user model |
| `ASSISTANT_MODEL` | anthropic/claude-3-haiku:beta | - | Model name for assistant role |
| `ASSISTANT_TEMPERATURE` | 0.7 | 0.0-2.0 | Temperature for assistant model |
| `ASSISTANT_MAX_TOKENS` | 200 | 1-4000 | Max tokens for assistant model |
| `EVALUATOR_MODEL` | anthropic/claude-3-haiku:beta | - | Model name for evaluator role |
| `EVALUATOR_TEMPERATURE` | 0.2 | 0.0-2.0 | Temperature for evaluator model |
| `EVALUATOR_MAX_TOKENS` | 100 | 1-4000 | Max tokens for evaluator model |
| `SUMMARY_MODEL` | anthropic/claude-3-haiku:beta | - | Model name for summary role |
| `SUMMARY_TEMPERATURE` | 0.3 | 0.0-2.0 | Temperature for summary model |
| `SUMMARY_MAX_TOKENS` | 300 | 1-4000 | Max tokens for summary model |
| `KNOWLEDGE_EXTRACTION_MODEL` | anthropic/claude-3-haiku:beta | - | Model name for knowledge_extraction role |
| `KNOWLEDGE_EXTRACTION_TEMPERATURE` | 0.3 | 0.0-2.0 | Temperature for knowledge_extraction model |
| `KNOWLEDGE_EXTRACTION_MAX_TOKENS` | 200 | 1-4000 | Max tokens for knowledge_extraction model |

## Memory Configuration

Configure memory behavior and thresholds.

| Environment Variable | Default | Range/Options | Description |
|---------------------|---------|---------------|-------------|
| `MEMORY_TYPE` | simple | simple, summary, summary_with_knowledge | Type of memory implementation |
| `MEMORY_CONTEXT_WINDOW` | 10 | 1-50 | Number of messages to keep in context |
| `SUMMARY_THRESHOLD` | 20 | 5-200 | Messages before summarization |
| `ENABLE_KNOWLEDGE_EXTRACTION` | false | true/false | Enable knowledge extraction in summary memory |

## Test Configuration

Configure test execution parameters.

| Environment Variable | Default | Range | Description |
|---------------------|---------|-------|-------------|
| `TEST_FACTS_FILE` | casual | - | Test facts file name (without .json) |
| `TEST_FACTS_COUNT` | 10 | 1-100 | Number of facts to test |
| `MESSAGES_BETWEEN_FACTS` | 5 | 1-20 | Messages between fact introductions |

## Output Configuration

Configure application output and logging.

| Environment Variable | Default | Options | Description |
|---------------------|---------|---------|-------------|
| `SAVE_RESULTS` | true | true/false | Save test results to file |
| `VERBOSE_LOGGING` | false | true/false | Enable detailed logging |
| `NODE_ENV` | development | development, test, production | Application environment |

## Memory Type Guide

- **simple**: Basic memory that keeps recent messages in context
- **summary**: Summarizes old messages when threshold is reached
- **summary_with_knowledge**: Summary memory with knowledge extraction

## Best Practices

- Use higher temperatures (0.7-1.0) for user models to encourage creativity
- Use lower temperatures (0.1-0.3) for evaluator models for consistency
- Set summary threshold to 2-3x the context window size
- Start with 5-15 facts for initial testing
- Use 3-10 messages between facts for natural conversation flow

## Configuration Examples

### Basic Setup (.env)

```bash
# API Configuration (choose one)
OPENROUTER_API_KEY=your_openrouter_key_here
# OR
OPENAI_API_KEY=your_openai_key_here

# Basic test configuration
MEMORY_TYPE=simple
TEST_FACTS_COUNT=10
MESSAGES_BETWEEN_FACTS=5
SAVE_RESULTS=true
```

### Advanced Memory Testing (.env)

```bash
# API Configuration
OPENROUTER_API_KEY=your_openrouter_key_here

# Advanced memory configuration
MEMORY_TYPE=summary_with_knowledge
MEMORY_CONTEXT_WINDOW=15
SUMMARY_THRESHOLD=30
ENABLE_KNOWLEDGE_EXTRACTION=true

# Test configuration for comprehensive testing
TEST_FACTS_COUNT=25
MESSAGES_BETWEEN_FACTS=8
TEST_FACTS_FILE=customer

# Enhanced logging
VERBOSE_LOGGING=true
SAVE_RESULTS=true
```

### Performance Optimized (.env)

```bash
# API Configuration
OPENROUTER_API_KEY=your_openrouter_key_here

# Optimized model configuration
USER_MODEL=anthropic/claude-3-haiku:beta
USER_TEMPERATURE=0.8
USER_MAX_TOKENS=100

ASSISTANT_MODEL=anthropic/claude-3-haiku:beta
ASSISTANT_TEMPERATURE=0.7
ASSISTANT_MAX_TOKENS=150

EVALUATOR_MODEL=anthropic/claude-3-haiku:beta
EVALUATOR_TEMPERATURE=0.1
EVALUATOR_MAX_TOKENS=50

# Memory configuration for speed
MEMORY_TYPE=simple
MEMORY_CONTEXT_WINDOW=8
TEST_FACTS_COUNT=15
MESSAGES_BETWEEN_FACTS=4
```

### Development Setup (.env)

```bash
# API Configuration
OPENROUTER_API_KEY=your_openrouter_key_here

# Development environment
NODE_ENV=development
VERBOSE_LOGGING=true
SAVE_RESULTS=true

# Quick testing configuration
MEMORY_TYPE=simple
TEST_FACTS_COUNT=5
MESSAGES_BETWEEN_FACTS=3
TEST_FACTS_FILE=simple
```

## Configuration Validation

The application performs comprehensive configuration validation on startup:

- **Required fields**: Validates that all required configuration is present
- **Type validation**: Ensures numeric values are within valid ranges
- **Business rules**: Checks logical constraints (e.g., summary threshold > context window)
- **API keys**: Verifies at least one API key is provided
- **Model names**: Ensures model names are not empty

### Validation Feedback

The application provides three types of feedback:

1. **Errors**: Configuration issues that prevent startup
2. **Warnings**: Suboptimal settings that may affect performance
3. **Suggestions**: Recommendations for improvement

## Troubleshooting

### Common Configuration Issues

1. **Missing API Key**
   ```
   Error: No API key configured. Please set OPENROUTER_API_KEY or OPENAI_API_KEY
   ```
   Solution: Add at least one API key to your .env file

2. **Invalid Temperature**
   ```
   Error: /models/evaluator/temperature: must be <= 2
   ```
   Solution: Set temperature values between 0.0 and 2.0

3. **Summary Threshold Too Low**
   ```
   Error: SUMMARY_THRESHOLD must be greater than MEMORY_CONTEXT_WINDOW
   ```
   Solution: Increase SUMMARY_THRESHOLD or decrease MEMORY_CONTEXT_WINDOW

4. **Invalid Memory Type**
   ```
   Error: /memory/type: must be equal to one of the allowed values
   ```
   Solution: Use one of: simple, summary, summary_with_knowledge

### Performance Issues

- **Slow API responses**: Try using faster models or reducing max_tokens
- **High memory usage**: Reduce MEMORY_CONTEXT_WINDOW or TEST_FACTS_COUNT
- **Frequent summarization**: Increase SUMMARY_THRESHOLD relative to MEMORY_CONTEXT_WINDOW

### Getting Help

1. Check the validation output on startup for specific guidance
2. Review the configuration examples above
3. Use VERBOSE_LOGGING=true to see detailed execution information
4. Start with smaller TEST_FACTS_COUNT values for initial testing