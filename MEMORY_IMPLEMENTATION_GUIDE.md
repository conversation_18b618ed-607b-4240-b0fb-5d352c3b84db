# Memory Implementation Guide

This guide provides step-by-step instructions for creating custom memory implementations in the LLM Memory Test Framework.

## 🧠 Understanding Memory Implementations

Memory implementations are the core of the framework, responsible for:
- Storing conversation messages
- Managing context windows
- Providing relevant context for LLM responses
- Implementing different memory strategies (FIFO, summarization, retrieval, etc.)

## 📋 Prerequisites

Before creating a custom memory implementation, ensure you understand:
- JavaScript ES6+ features (classes, async/await, modules)
- The MemoryInterface contract
- Configuration management in the framework
- Logging and error handling patterns

## 🚀 Step-by-Step Implementation

### Step 1: Create the Memory Class

Create a new file in `src/memory/` that extends `MemoryInterface`:

```javascript
// src/memory/vectorMemory.js
const MemoryInterface = require('./memoryInterface');
const { Logger } = require('../utils/logger');
const { PerformanceTracker } = require('../utils/performanceTracker');

/**
 * Vector Memory Implementation
 * 
 * Uses vector embeddings to store and retrieve semantically similar messages.
 * Provides context based on semantic similarity rather than recency.
 */
class VectorMemory extends MemoryInterface {
  /**
   * Initialize vector memory with configuration
   * 
   * @param {string} sessionId - Unique session identifier
   * @param {ConfigManager} config - Configuration manager instance
   */
  constructor(sessionId, config) {
    super(sessionId);
    
    this.config = config;
    this.logger = new Logger('VectorMemory', sessionId);
    this.performanceTracker = new PerformanceTracker(sessionId);
    
    // Vector storage
    this.messages = new Map();
    this.embeddings = new Map();
    this.messageIndex = 0;
    
    // Configuration
    this.maxMessages = config.get('memory.contextWindow') || 100;
    this.similarityThreshold = config.get('memory.similarityThreshold') || 0.7;
    this.embeddingDimension = config.get('memory.embeddingDimension') || 384;
    
    this.logger.info('VectorMemory initialized', {
      maxMessages: this.maxMessages,
      similarityThreshold: this.similarityThreshold,
      embeddingDimension: this.embeddingDimension
    });
  }

  /**
   * Add a message to vector memory
   * 
   * @param {Object} message - Message object with role and content
   * @param {string} message.role - Message role (user/assistant)
   * @param {string} message.content - Message content
   */
  async addMessage(message) {
    const operationId = this.performanceTracker.startOperation('add-message', {
      type: 'memory-operation',
      component: 'VectorMemory'
    });

    try {
      // Validate message
      await this.validateMessage(message);
      
      // Generate unique message ID
      const messageId = `msg_${this.messageIndex++}_${Date.now()}`;
      
      // Generate embedding for the message
      this.logger.debug('Generating embedding for message', { messageId });
      const embedding = await this.generateEmbedding(message.content);
      
      // Store message and embedding
      this.messages.set(messageId, {
        ...message,
        id: messageId,
        timestamp: new Date().toISOString()
      });
      
      this.embeddings.set(messageId, embedding);
      
      // Manage memory size
      await this.manageMemorySize();
      
      this.logger.debug('Message added to vector memory', {
        messageId,
        totalMessages: this.messages.size
      });

      this.performanceTracker.endOperation(operationId, {
        success: true,
        metadata: { messageId, totalMessages: this.messages.size }
      });

    } catch (error) {
      this.logger.error('Failed to add message to vector memory', {
        error: error.message,
        message
      });
      
      this.performanceTracker.endOperation(operationId, {
        success: false,
        error: error.message
      });
      
      throw error;
    }
  }

  /**
   * Get memory context based on semantic similarity
   * 
   * @param {Object} currentMessage - Current message for context retrieval
   * @returns {Promise<string>} Formatted context string
   */
  async getMemoryContext(currentMessage = null) {
    const operationId = this.performanceTracker.startOperation('get-context', {
      type: 'memory-operation',
      component: 'VectorMemory'
    });

    try {
      if (this.messages.size === 0) {
        this.performanceTracker.endOperation(operationId, {
          success: true,
          metadata: { contextLength: 0 }
        });
        return '';
      }

      let relevantMessages;

      if (currentMessage && currentMessage.content) {
        // Find semantically similar messages
        relevantMessages = await this.findSimilarMessages(
          currentMessage.content,
          this.config.get('memory.contextWindow') || 10
        );
      } else {
        // Fallback to most recent messages
        relevantMessages = Array.from(this.messages.values())
          .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
          .slice(0, this.config.get('memory.contextWindow') || 10);
      }

      const context = this.formatContext(relevantMessages);
      
      this.logger.debug('Generated memory context', {
        messagesUsed: relevantMessages.length,
        contextLength: context.length
      });

      this.performanceTracker.endOperation(operationId, {
        success: true,
        metadata: {
          messagesUsed: relevantMessages.length,
          contextLength: context.length
        }
      });

      return context;

    } catch (error) {
      this.logger.error('Failed to get memory context', {
        error: error.message
      });
      
      this.performanceTracker.endOperation(operationId, {
        success: false,
        error: error.message
      });
      
      throw error;
    }
  }

  /**
   * Clear all stored messages and embeddings
   */
  async clearMemory() {
    this.logger.info('Clearing vector memory');
    
    this.messages.clear();
    this.embeddings.clear();
    this.messageIndex = 0;
    
    this.logger.info('Vector memory cleared');
  }

  /**
   * Get memory statistics
   * 
   * @returns {Promise<Object>} Memory statistics
   */
  async getStats() {
    const baseStats = await super.getStats();
    
    return {
      ...baseStats,
      type: 'VectorMemory',
      messageCount: this.messages.size,
      embeddingCount: this.embeddings.size,
      averageEmbeddingDimension: this.embeddingDimension,
      memoryUtilization: (this.messages.size / this.maxMessages * 100).toFixed(1) + '%'
    };
  }

  // Private helper methods

  /**
   * Generate embedding for text content
   * 
   * @param {string} text - Text to generate embedding for
   * @returns {Promise<number[]>} Embedding vector
   */
  async generateEmbedding(text) {
    // This is a placeholder implementation
    // In a real implementation, you would use:
    // - OpenAI embeddings API
    // - Sentence transformers
    // - Local embedding models
    // - etc.
    
    // Simulate embedding generation with random vector
    const embedding = Array(this.embeddingDimension)
      .fill(0)
      .map(() => Math.random() * 2 - 1); // Random values between -1 and 1
    
    // Normalize the vector
    const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
    return embedding.map(val => val / magnitude);
  }

  /**
   * Find messages similar to the given text
   * 
   * @param {string} queryText - Text to find similar messages for
   * @param {number} topK - Number of similar messages to return
   * @returns {Promise<Object[]>} Array of similar messages
   */
  async findSimilarMessages(queryText, topK = 5) {
    if (this.messages.size === 0) {
      return [];
    }

    // Generate embedding for query text
    const queryEmbedding = await this.generateEmbedding(queryText);
    
    // Calculate similarities
    const similarities = [];
    
    for (const [messageId, message] of this.messages) {
      const messageEmbedding = this.embeddings.get(messageId);
      const similarity = this.calculateCosineSimilarity(queryEmbedding, messageEmbedding);
      
      if (similarity >= this.similarityThreshold) {
        similarities.push({
          message,
          similarity
        });
      }
    }
    
    // Sort by similarity and return top K
    return similarities
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, topK)
      .map(item => item.message);
  }

  /**
   * Calculate cosine similarity between two vectors
   * 
   * @param {number[]} vectorA - First vector
   * @param {number[]} vectorB - Second vector
   * @returns {number} Cosine similarity (-1 to 1)
   */
  calculateCosineSimilarity(vectorA, vectorB) {
    if (vectorA.length !== vectorB.length) {
      throw new Error('Vectors must have the same dimension');
    }

    let dotProduct = 0;
    let magnitudeA = 0;
    let magnitudeB = 0;

    for (let i = 0; i < vectorA.length; i++) {
      dotProduct += vectorA[i] * vectorB[i];
      magnitudeA += vectorA[i] * vectorA[i];
      magnitudeB += vectorB[i] * vectorB[i];
    }

    magnitudeA = Math.sqrt(magnitudeA);
    magnitudeB = Math.sqrt(magnitudeB);

    if (magnitudeA === 0 || magnitudeB === 0) {
      return 0;
    }

    return dotProduct / (magnitudeA * magnitudeB);
  }

  /**
   * Manage memory size by removing oldest messages if needed
   */
  async manageMemorySize() {
    if (this.messages.size <= this.maxMessages) {
      return;
    }

    // Remove oldest messages
    const messagesToRemove = this.messages.size - this.maxMessages;
    const sortedMessages = Array.from(this.messages.entries())
      .sort(([, a], [, b]) => new Date(a.timestamp) - new Date(b.timestamp));

    for (let i = 0; i < messagesToRemove; i++) {
      const [messageId] = sortedMessages[i];
      this.messages.delete(messageId);
      this.embeddings.delete(messageId);
    }

    this.logger.debug('Memory size managed', {
      removedMessages: messagesToRemove,
      currentSize: this.messages.size
    });
  }

  /**
   * Format messages into context string
   * 
   * @param {Object[]} messages - Array of message objects
   * @returns {string} Formatted context string
   */
  formatContext(messages) {
    if (!messages || messages.length === 0) {
      return '';
    }

    return messages
      .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))
      .map(msg => `${msg.role}: ${msg.content}`)
      .join('\n');
  }
}

module.exports = VectorMemory;
```

This implementation demonstrates:
- Proper inheritance from MemoryInterface
- Comprehensive logging and performance tracking
- Error handling and validation
- Configuration management
- Vector-based similarity search
- Memory management and cleanup

### Step 2: Register in Factory

Add your memory type to `src/memory/memoryFactory.js`:

```javascript
// Add import
const VectorMemory = require('./vectorMemory');

// Add case in createMemory method
case 'vector':
  return new VectorMemory(sessionId, config);

// Update getSupportedTypes
static getSupportedTypes() {
  return ['simple', 'summary', 'summary_with_knowledge', 'vector'];
}
```

### Step 3: Add Configuration Support

Update configuration schema and defaults to support your new memory type.

### Step 4: Create Tests

Write comprehensive tests for your implementation in `src/__tests__/memory/vectorMemory.test.js`.

### Step 5: Update Documentation

Update README.md and other documentation to include your new memory type.

## 🎯 Implementation Checklist

- [ ] Extends MemoryInterface properly
- [ ] Implements all required methods
- [ ] Includes comprehensive error handling
- [ ] Uses structured logging
- [ ] Includes performance tracking
- [ ] Handles configuration properly
- [ ] Includes input validation
- [ ] Manages memory efficiently
- [ ] Includes comprehensive tests
- [ ] Updates documentation

## 💡 Advanced Patterns

### Async Initialization
```javascript
class AsyncMemory extends MemoryInterface {
  constructor(sessionId, config) {
    super(sessionId);
    this.initialized = false;
    this.initPromise = this.initialize();
  }

  async initialize() {
    // Async initialization logic
    this.initialized = true;
  }

  async addMessage(message) {
    await this.initPromise;
    // Implementation
  }
}
```

### Plugin Architecture
```javascript
class PluginMemory extends MemoryInterface {
  constructor(sessionId, config) {
    super(sessionId);
    this.plugins = [];
    this.loadPlugins(config.get('memory.plugins') || []);
  }

  loadPlugins(pluginConfigs) {
    for (const pluginConfig of pluginConfigs) {
      const plugin = this.createPlugin(pluginConfig);
      this.plugins.push(plugin);
    }
  }
}
```

This guide provides a comprehensive foundation for creating custom memory implementations that integrate seamlessly with the framework.
