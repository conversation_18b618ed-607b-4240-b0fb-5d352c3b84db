#!/usr/bin/env node

/**
 * Test script for data integrity and format checking functionality
 * 
 * This script tests the newly implemented data integrity features:
 * - Test fact file format validation on load
 * - Conversation log integrity checking during simulation
 * - Result output format validation before saving
 * - Data migration utilities for schema evolution
 */

const { DataIntegrityChecker } = require('./src/validation/dataIntegrityChecker');
const { DataMigrationManager } = require('./src/validation/dataMigration');
const path = require('path');

async function testDataIntegrity() {
  console.log('🔍 Testing Data Integrity and Format Checking...\n');

  const integrityChecker = new DataIntegrityChecker();
  const migrationManager = new DataMigrationManager();

  // Test 1: Test fact file format validation
  console.log('1. Testing test fact file format validation...');
  try {
    const testFactsPath = path.join(__dirname, 'data', 'test_facts_simple.json');
    const result = await integrityChecker.validateTestFactsFileIntegrity(testFactsPath);
    
    console.log(`   ✅ File validation: ${result.isValid ? 'PASSED' : 'FAILED'}`);
    console.log(`   📊 Statistics: ${result.statistics.factCount} facts, ${result.statistics.validationTime}ms`);
    
    if (result.issues.length > 0) {
      console.log(`   ⚠️  Issues: ${result.issues.length}`);
      result.issues.slice(0, 3).forEach(issue => console.log(`      - ${issue}`));
    }
    
    if (result.warnings.length > 0) {
      console.log(`   ⚠️  Warnings: ${result.warnings.length}`);
      result.warnings.slice(0, 2).forEach(warning => console.log(`      - ${warning}`));
    }
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
  }

  console.log();

  // Test 2: Conversation log integrity checking
  console.log('2. Testing conversation log integrity checking...');
  try {
    const baseTime = Date.now();
    const sampleConversationLog = [
      { 
        role: 'user', 
        content: 'Hello, my name is John.', 
        timestamp: baseTime,
        cycle: 1
      },
      { 
        role: 'assistant', 
        content: 'Nice to meet you, John! How can I help you today?', 
        timestamp: baseTime + 1000,
        cycle: 2
      },
      { 
        role: 'user', 
        content: 'I work as a software engineer.', 
        timestamp: baseTime + 2000,
        cycle: 3
      },
      { 
        role: 'assistant', 
        content: 'That\'s interesting! What kind of software do you work on?', 
        timestamp: baseTime + 3000,
        cycle: 4
      }
    ];

    const result = await integrityChecker.validateConversationLogIntegrity(sampleConversationLog);
    
    console.log(`   ✅ Conversation log validation: ${result.isValid ? 'PASSED' : 'FAILED'}`);
    console.log(`   📊 Statistics: ${result.statistics.messageCount} messages, ${result.statistics.validationTime}ms`);
    
    if (result.issues.length > 0) {
      console.log(`   ⚠️  Issues: ${result.issues.length}`);
      result.issues.slice(0, 3).forEach(issue => console.log(`      - ${issue}`));
    }
    
    if (result.warnings.length > 0) {
      console.log(`   ⚠️  Warnings: ${result.warnings.length}`);
      result.warnings.slice(0, 2).forEach(warning => console.log(`      - ${warning}`));
    }
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
  }

  console.log();

  // Test 3: Result output format validation
  console.log('3. Testing result output format validation...');
  try {
    const sampleResultData = {
      sessionId: 'test-session-123',
      memoryType: 'simple',
      testFactsFile: 'simple',
      testFactsCount: 5,
      messagesBetweenFacts: 3,
      userModel: 'gpt-3.5-turbo',
      assistantModel: 'claude-3-sonnet',
      evaluatorModel: 'gpt-4',
      overallScore: 85.5,
      evaluationResults: [
        {
          factId: 1,
          fact: 'My name is John',
          question: 'What is my name?',
          expectedAnswer: 'John',
          actualResponse: 'Your name is John',
          score: 9,
          complexity: 'simple'
        },
        {
          factId: 2,
          fact: 'I work as a software engineer',
          question: 'What is my profession?',
          expectedAnswer: 'Software engineer',
          actualResponse: 'You work as a software engineer',
          score: 8,
          complexity: 'simple'
        }
      ],
      conversationLog: [
        { role: 'user', content: 'Hello, my name is John.' },
        { role: 'assistant', content: 'Nice to meet you, John!' },
        { role: 'user', content: 'I work as a software engineer.' },
        { role: 'assistant', content: 'That\'s interesting!' }
      ]
    };

    const result = integrityChecker.validateResultOutputFormat(sampleResultData, 'test_results/test_output.md');
    
    console.log(`   ✅ Result format validation: ${result.isValid ? 'PASSED' : 'FAILED'}`);
    console.log(`   📊 Statistics: ${result.statistics.requiredFieldsPresent}/5 required fields, ${result.statistics.optionalFieldsPresent} optional fields`);
    
    if (result.issues.length > 0) {
      console.log(`   ⚠️  Issues: ${result.issues.length}`);
      result.issues.slice(0, 3).forEach(issue => console.log(`      - ${issue}`));
    }
    
    if (result.warnings.length > 0) {
      console.log(`   ⚠️  Warnings: ${result.warnings.length}`);
      result.warnings.slice(0, 2).forEach(warning => console.log(`      - ${warning}`));
    }

    if (result.recommendations.length > 0) {
      console.log(`   💡 Recommendations: ${result.recommendations.length}`);
      result.recommendations.slice(0, 2).forEach(rec => console.log(`      - ${rec}`));
    }
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
  }

  console.log();

  // Test 4: Data migration utilities
  console.log('4. Testing data migration utilities...');
  try {
    // Test schema version detection
    const sampleTestFacts = [
      { id: 1, fact: 'Test fact', question: 'Test?', answer: 'Test', complexity: 'simple' }
    ];
    
    const detectedVersion = migrationManager.detectSchemaVersion('testFacts', sampleTestFacts);
    console.log(`   ✅ Schema version detection: ${detectedVersion}`);
    
    const latestVersion = migrationManager.getLatestVersion('testFacts');
    console.log(`   📋 Latest version available: ${latestVersion}`);
    
    // Test migration (if needed)
    if (detectedVersion !== latestVersion) {
      const migrationResult = migrationManager.migrateToLatest('testFacts', sampleTestFacts);
      console.log(`   ✅ Migration: ${migrationResult.success ? 'PASSED' : 'FAILED'}`);
      console.log(`   📈 Migrated from ${migrationResult.fromVersion} to ${migrationResult.toVersion}`);
      console.log(`   🔧 Migrations applied: ${migrationResult.migrationsApplied.length}`);
    } else {
      console.log(`   ✅ No migration needed - already at latest version`);
    }
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
  }

  console.log();

  // Test 5: Integration test with invalid data
  console.log('5. Testing with invalid data (should fail gracefully)...');
  try {
    // Test with invalid result data
    const invalidResultData = {
      sessionId: '', // Invalid: empty string
      memoryType: 'invalid_type', // Invalid: not in allowed list
      overallScore: 150, // Invalid: out of range
      evaluationResults: 'not_an_array', // Invalid: should be array
      conversationLog: null // Invalid: should be array
    };

    const result = integrityChecker.validateResultOutputFormat(invalidResultData, 'test.md');
    
    console.log(`   ✅ Invalid data handling: ${!result.isValid ? 'PASSED' : 'FAILED'} (should fail)`);
    console.log(`   📊 Issues detected: ${result.issues.length}`);
    console.log(`   📊 Warnings: ${result.warnings.length}`);
    
    if (result.issues.length > 0) {
      console.log(`   🔍 Sample issues:`);
      result.issues.slice(0, 3).forEach(issue => console.log(`      - ${issue}`));
    }
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
  }

  console.log('\n🎉 Data integrity testing completed!');
  console.log('\n📋 Summary:');
  console.log('   ✅ Test fact file format validation - Implemented');
  console.log('   ✅ Conversation log integrity checking - Implemented');
  console.log('   ✅ Result output format validation - Implemented');
  console.log('   ✅ Data migration utilities - Available');
  console.log('   ✅ Error handling and validation - Working');
}

// Run the tests
if (require.main === module) {
  testDataIntegrity().catch(console.error);
}

module.exports = { testDataIntegrity };