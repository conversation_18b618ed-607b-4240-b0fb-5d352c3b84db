# Evaluation Framework Guide

This guide explains the evaluation system in the LLM Memory Test Framework and how to customize or extend it for your specific needs.

## 🎯 Overview

The evaluation framework is responsible for:
- Testing memory retention by asking questions about injected facts
- Scoring LLM responses against expected answers
- Providing detailed analysis and reasoning for scores
- Generating comprehensive evaluation reports

## 🏗️ Architecture

### Core Components

1. **Evaluator Class** (`src/utils/evaluator.js`)
   - Main evaluation orchestrator
   - Handles question generation and response scoring
   - Manages evaluation workflow

2. **Scoring System**
   - Configurable scoring algorithms
   - Support for different evaluation criteria
   - Confidence level assessment

3. **Report Generation**
   - Detailed evaluation results
   - Statistical analysis
   - Performance metrics

## 📊 Evaluation Process

### 1. Fact Injection and Testing Flow

```mermaid
graph TD
    A[Load Test Facts] --> B[Inject Fact into Conversation]
    B --> C[Continue Conversation]
    C --> D[Ask Memory Question]
    D --> E[Get LLM Response]
    E --> F[Evaluate Response]
    F --> G[Calculate Score]
    G --> H[Generate Reasoning]
    H --> I[Store Result]
    I --> J{More Facts?}
    J -->|Yes| B
    J -->|No| K[Generate Final Report]
```

### 2. Scoring Methodology

The default evaluator uses a multi-criteria approach:

```javascript
// Default scoring criteria
const evaluationCriteria = {
  accuracy: 0.4,      // 40% - Factual correctness
  completeness: 0.3,  // 30% - Information completeness
  relevance: 0.2,     // 20% - Response relevance
  clarity: 0.1        // 10% - Response clarity
};
```

### 3. Confidence Levels

- **High Confidence (90-100%)**: Clear, accurate, complete response
- **Medium Confidence (70-89%)**: Mostly accurate with minor gaps
- **Low Confidence (50-69%)**: Partially correct or unclear
- **Very Low Confidence (0-49%)**: Incorrect or irrelevant response

## 🔧 Customizing Evaluation

### Creating Custom Evaluators

Extend the base `Evaluator` class to implement custom evaluation logic:

```javascript
// src/utils/semanticEvaluator.js
const Evaluator = require('./evaluator');
const { Logger } = require('./logger');

class SemanticEvaluator extends Evaluator {
  constructor(config) {
    super(config);
    this.logger = new Logger('SemanticEvaluator');
    this.semanticThreshold = config.get('evaluation.semanticThreshold') || 0.8;
  }

  /**
   * Evaluate response using semantic similarity
   */
  async evaluateResponse(fact, question, response, context) {
    try {
      this.logger.debug('Starting semantic evaluation', {
        fact: fact.fact,
        question,
        response
      });

      // Calculate semantic similarity
      const semanticScore = await this.calculateSemanticSimilarity(
        fact.answer,
        response
      );

      // Calculate keyword overlap
      const keywordScore = this.calculateKeywordOverlap(
        fact.answer,
        response
      );

      // Calculate structural similarity
      const structuralScore = this.calculateStructuralSimilarity(
        fact.answer,
        response
      );

      // Weighted final score
      const finalScore = Math.round(
        (semanticScore * 0.5) +
        (keywordScore * 0.3) +
        (structuralScore * 0.2)
      );

      // Determine confidence level
      const confidence = this.determineConfidence(finalScore, {
        semantic: semanticScore,
        keyword: keywordScore,
        structural: structuralScore
      });

      // Generate detailed reasoning
      const reasoning = this.generateReasoning(finalScore, {
        semanticScore,
        keywordScore,
        structuralScore,
        threshold: this.semanticThreshold
      });

      return {
        score: finalScore,
        confidence,
        reasoning,
        details: {
          expectedAnswer: fact.answer,
          actualResponse: response,
          semanticScore,
          keywordScore,
          structuralScore,
          evaluationMethod: 'semantic'
        }
      };

    } catch (error) {
      this.logger.error('Semantic evaluation failed', {
        error: error.message,
        fact: fact.fact
      });
      
      // Fallback to default evaluation
      return await super.evaluateResponse(fact, question, response, context);
    }
  }

  /**
   * Calculate semantic similarity between expected and actual responses
   */
  async calculateSemanticSimilarity(expected, actual) {
    // Implement your semantic similarity calculation
    // This could use:
    // - Sentence embeddings (OpenAI, sentence-transformers)
    // - BERT similarity scores
    // - Custom NLP models
    
    // Placeholder implementation
    const expectedWords = this.tokenize(expected.toLowerCase());
    const actualWords = this.tokenize(actual.toLowerCase());
    
    const intersection = expectedWords.filter(word => 
      actualWords.includes(word)
    );
    
    const union = [...new Set([...expectedWords, ...actualWords])];
    
    return intersection.length / union.length * 100;
  }

  /**
   * Calculate keyword overlap score
   */
  calculateKeywordOverlap(expected, actual) {
    const expectedKeywords = this.extractKeywords(expected);
    const actualKeywords = this.extractKeywords(actual);
    
    const matchedKeywords = expectedKeywords.filter(keyword =>
      actualKeywords.some(actual => 
        actual.toLowerCase().includes(keyword.toLowerCase())
      )
    );
    
    return expectedKeywords.length > 0 
      ? (matchedKeywords.length / expectedKeywords.length) * 100
      : 0;
  }

  /**
   * Calculate structural similarity (sentence structure, length, etc.)
   */
  calculateStructuralSimilarity(expected, actual) {
    const expectedSentences = expected.split(/[.!?]+/).filter(s => s.trim());
    const actualSentences = actual.split(/[.!?]+/).filter(s => s.trim());
    
    const lengthSimilarity = 1 - Math.abs(
      expectedSentences.length - actualSentences.length
    ) / Math.max(expectedSentences.length, actualSentences.length);
    
    const avgLengthExpected = expectedSentences.reduce((sum, s) => 
      sum + s.length, 0) / expectedSentences.length;
    const avgLengthActual = actualSentences.reduce((sum, s) => 
      sum + s.length, 0) / actualSentences.length;
    
    const avgLengthSimilarity = 1 - Math.abs(
      avgLengthExpected - avgLengthActual
    ) / Math.max(avgLengthExpected, avgLengthActual);
    
    return ((lengthSimilarity + avgLengthSimilarity) / 2) * 100;
  }

  /**
   * Determine confidence level based on scores
   */
  determineConfidence(finalScore, subscores) {
    if (finalScore >= 90 && subscores.semantic >= 85) {
      return 'high';
    } else if (finalScore >= 70 && subscores.semantic >= 65) {
      return 'medium';
    } else if (finalScore >= 50) {
      return 'low';
    } else {
      return 'very_low';
    }
  }

  /**
   * Generate detailed reasoning for the evaluation
   */
  generateReasoning(finalScore, scores) {
    const reasons = [];
    
    if (scores.semanticScore >= 80) {
      reasons.push(`High semantic similarity (${scores.semanticScore.toFixed(1)}%)`);
    } else if (scores.semanticScore >= 60) {
      reasons.push(`Moderate semantic similarity (${scores.semanticScore.toFixed(1)}%)`);
    } else {
      reasons.push(`Low semantic similarity (${scores.semanticScore.toFixed(1)}%)`);
    }
    
    if (scores.keywordScore >= 80) {
      reasons.push(`Strong keyword overlap (${scores.keywordScore.toFixed(1)}%)`);
    } else if (scores.keywordScore >= 50) {
      reasons.push(`Partial keyword overlap (${scores.keywordScore.toFixed(1)}%)`);
    } else {
      reasons.push(`Limited keyword overlap (${scores.keywordScore.toFixed(1)}%)`);
    }
    
    if (scores.structuralScore >= 70) {
      reasons.push(`Similar response structure (${scores.structuralScore.toFixed(1)}%)`);
    }
    
    return `Final score: ${finalScore}%. ${reasons.join('. ')}.`;
  }

  /**
   * Tokenize text into words
   */
  tokenize(text) {
    return text
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2);
  }

  /**
   * Extract important keywords from text
   */
  extractKeywords(text) {
    // Simple keyword extraction
    // In a real implementation, you might use:
    // - TF-IDF
    // - Named entity recognition
    // - Part-of-speech tagging
    
    const stopWords = new Set([
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
      'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
      'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
      'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'
    ]);
    
    return this.tokenize(text.toLowerCase())
      .filter(word => !stopWords.has(word) && word.length > 3);
  }
}

module.exports = SemanticEvaluator;
```

### Using Custom Evaluators

To use your custom evaluator, modify the main application:

```javascript
// In src/app.js
const SemanticEvaluator = require('./utils/semanticEvaluator');

// Replace the default evaluator
const evaluator = new SemanticEvaluator(config);
```

## 📈 Evaluation Metrics

### Built-in Metrics

1. **Overall Score**: Weighted average of all evaluation criteria
2. **Simple vs Complex**: Separate scoring for different fact types
3. **Confidence Distribution**: Breakdown of confidence levels
4. **Response Quality**: Analysis of response characteristics

### Custom Metrics

Add custom metrics by extending the evaluation results:

```javascript
class CustomEvaluator extends Evaluator {
  async evaluateMemory(memory, facts, conversationLog) {
    const baseResults = await super.evaluateMemory(memory, facts, conversationLog);
    
    // Add custom metrics
    const customMetrics = {
      domainSpecificAccuracy: this.calculateDomainAccuracy(baseResults),
      responseConsistency: this.calculateConsistency(baseResults),
      memoryEfficiency: this.calculateMemoryEfficiency(memory, baseResults)
    };
    
    return {
      ...baseResults,
      customMetrics
    };
  }
}
```

## 🔍 Advanced Evaluation Techniques

### Multi-Model Evaluation

Use multiple LLM models for evaluation consensus:

```javascript
class ConsensusEvaluator extends Evaluator {
  constructor(config) {
    super(config);
    this.evaluatorModels = [
      'gpt-4',
      'claude-3-sonnet',
      'gemini-pro'
    ];
  }

  async evaluateResponse(fact, question, response, context) {
    const evaluations = [];
    
    for (const model of this.evaluatorModels) {
      const evaluation = await this.evaluateWithModel(
        fact, question, response, context, model
      );
      evaluations.push(evaluation);
    }
    
    return this.calculateConsensus(evaluations);
  }
}
```

### Temporal Evaluation

Track how memory performance changes over time:

```javascript
class TemporalEvaluator extends Evaluator {
  async evaluateMemory(memory, facts, conversationLog) {
    const results = await super.evaluateMemory(memory, facts, conversationLog);
    
    // Add temporal analysis
    const temporalAnalysis = this.analyzeTemporalPerformance(
      facts, 
      conversationLog,
      results
    );
    
    return {
      ...results,
      temporalAnalysis
    };
  }
}
```

## 🎯 Best Practices

1. **Consistent Scoring**: Use standardized scoring criteria
2. **Detailed Reasoning**: Provide clear explanations for scores
3. **Error Handling**: Gracefully handle evaluation failures
4. **Performance Monitoring**: Track evaluation performance
5. **Validation**: Validate evaluation results for consistency

## 📊 Evaluation Reports

The framework generates comprehensive evaluation reports including:
- Individual fact evaluation results
- Statistical summaries
- Performance trends
- Detailed reasoning for each score
- Confidence level distributions

This evaluation framework provides a robust foundation for assessing memory performance while remaining flexible enough to accommodate custom evaluation strategies.
