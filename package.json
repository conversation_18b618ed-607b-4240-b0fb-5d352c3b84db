{"name": "llm-memory-test", "version": "1.0.0", "description": "Application to test different LLM memory implementations", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "node --watch src/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/ --ext .js || echo 'No files to lint'", "lint:fix": "eslint src/ --ext .js --fix || echo 'No files to lint'", "format": "prettier --write \"src/**/*.js\" \"*.{js,json,md}\"", "format:check": "prettier --check \"src/**/*.js\" \"*.{js,json,md}\"", "precommit": "npm run lint && npm run format:check && npm run test", "prepare": "npm run lint && npm run test"}, "keywords": ["llm", "memory", "test", "ai", "conversation", "evaluation"], "author": "", "license": "ISC", "dependencies": {"@langchain/openai": "^0.5.5", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "dotenv": "^16.4.7", "langchain": "^0.3.21", "uuid": "^11.1.0"}, "devDependencies": {"eslint": "^8.57.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-n": "^16.6.2", "eslint-plugin-promise": "^6.1.1", "jest": "^29.7.0", "prettier": "^3.2.5"}, "engines": {"node": ">=18.0.0"}}