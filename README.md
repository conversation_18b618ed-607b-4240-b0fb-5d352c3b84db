# LLM Memory Test Framework

A comprehensive framework for testing and evaluating different Large Language Model (LLM) memory implementations through simulated conversations and systematic evaluation.

## 🎯 Overview

The LLM Memory Test Framework is a sophisticated testing platform designed to evaluate how well different memory implementations can retain and recall information during extended conversations. The framework simulates realistic conversations between user and assistant LLMs, strategically injects facts at specified intervals, and then evaluates memory retention through targeted questions.

This framework is particularly valuable for:
- **Researchers** studying memory mechanisms in conversational AI
- **Developers** building chatbots and virtual assistants
- **Organizations** evaluating memory solutions for production systems
- **AI Engineers** comparing different memory architectures

## ✨ Key Features

### 🧠 Multiple Memory Implementations
- **Simple Memory**: Maintains the most recent N messages with configurable context window
- **Summary Memory**: Automatically summarizes older messages when thresholds are reached
- **Summary with Knowledge Extraction**: Advanced summarization with explicit fact extraction and preservation

### 🎭 Realistic Conversation Simulation
- Dual-LLM conversation simulation (user and assistant roles)
- Configurable conversation patterns and message generation
- Strategic fact injection at customizable intervals
- Natural conversation flow with contextual responses

### 📊 Comprehensive Evaluation System
- Automated memory retention scoring
- Separate evaluation of simple vs. complex facts
- Statistical analysis and performance metrics
- Detailed evaluation reports with confidence scoring

### 🔧 Enterprise-Ready Infrastructure
- Robust error handling with retry logic and circuit breakers
- Comprehensive logging and performance monitoring
- Configuration management with validation
- Support for multiple LLM providers (OpenRouter, OpenAI)
- Extensible architecture for custom implementations

## 🚀 Quick Start

### Prerequisites

Before installing the LLM Memory Test Framework, ensure you have:

- **Node.js** version 18.0.0 or higher
- **npm** (comes with Node.js) or **yarn** package manager
- An API key from either:
  - [OpenRouter](https://openrouter.ai/) (recommended for access to multiple models)
  - [OpenAI](https://platform.openai.com/) (for OpenAI models only)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd llm-memory-test
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up configuration**
   ```bash
   # Copy the example environment file
   cp .env.example .env

   # Edit the configuration file with your settings
   nano .env  # or use your preferred editor
   ```

4. **Configure your API keys**

   Edit the `.env` file and add at least one API key:
   ```bash
   # For OpenRouter (recommended)
   OPENROUTER_API_KEY=your_openrouter_key_here

   # OR for OpenAI
   OPENAI_API_KEY=your_openai_key_here
   ```

5. **Verify installation**
   ```bash
   # Run a quick test to verify everything is working
   npm test

   # Start the application with default settings
   npm start
   ```

### First Run

For your first test run, we recommend starting with these settings in your `.env` file:

```bash
# Basic configuration for first run
MEMORY_TYPE=simple
TEST_FACTS_COUNT=5
MESSAGES_BETWEEN_FACTS=3
TEST_FACTS_FILE=simple
VERBOSE_LOGGING=true
SAVE_RESULTS=true
```

This will run a quick test with simple memory and generate detailed logs to help you understand the framework.

## ⚙️ Configuration Guide

The framework uses environment variables for configuration, providing flexibility and security. All configuration is managed through the `.env` file with comprehensive validation and helpful error messages.

### Essential Configuration

#### API Credentials
```bash
# Choose one or both (OpenRouter is recommended for model variety)
OPENROUTER_API_KEY=your_openrouter_key_here
OPENAI_API_KEY=your_openai_key_here
```

#### Memory Settings
```bash
# Memory implementation to test
MEMORY_TYPE=simple                    # Options: simple, summary, summary_with_knowledge

# Memory behavior
MEMORY_CONTEXT_WINDOW=10             # Messages to keep in context (1-50)
SUMMARY_THRESHOLD=20                 # Messages before summarization (5-200)
ENABLE_KNOWLEDGE_EXTRACTION=false    # Extract facts during summarization
```

#### Test Parameters
```bash
# Test data and execution
TEST_FACTS_FILE=simple               # Test scenario: simple, customer, casual, etc.
TEST_FACTS_COUNT=10                  # Number of facts to test (1-100)
MESSAGES_BETWEEN_FACTS=5             # Conversation messages between facts (1-20)

# Output and logging
SAVE_RESULTS=true                    # Save detailed results to files
VERBOSE_LOGGING=false                # Enable detailed execution logs
```

### Advanced Configuration

#### Model Customization
```bash
# Customize models for different roles
USER_MODEL=anthropic/claude-3-haiku:beta
USER_TEMPERATURE=0.8                 # Creativity level (0.0-2.0)
USER_MAX_TOKENS=150                  # Response length limit

ASSISTANT_MODEL=anthropic/claude-3-haiku:beta
ASSISTANT_TEMPERATURE=0.7
ASSISTANT_MAX_TOKENS=200

EVALUATOR_MODEL=anthropic/claude-3-haiku:beta
EVALUATOR_TEMPERATURE=0.2            # Lower for consistency
EVALUATOR_MAX_TOKENS=100
```

#### Performance Tuning
```bash
# Environment and performance
NODE_ENV=development                 # development, test, production
```

For complete configuration documentation, see [CONFIGURATION.md](CONFIGURATION.md).

## 🎮 Usage

### Basic Usage

Run the framework with default settings:
```bash
npm start
```

Or run directly with Node.js:
```bash
node src/app.js
```

### Development Mode

For development with automatic restarts on file changes:
```bash
npm run dev
```

### Testing and Quality Assurance

```bash
# Run the test suite
npm test

# Run tests with coverage reporting
npm run test:coverage

# Run tests in watch mode during development
npm run test:watch

# Check code quality
npm run lint

# Fix linting issues automatically
npm run lint:fix

# Format code
npm run format
```

### Common Usage Patterns

#### Quick Memory Comparison
```bash
# Test simple memory
MEMORY_TYPE=simple npm start

# Test summary memory
MEMORY_TYPE=summary npm start

# Test advanced memory with knowledge extraction
MEMORY_TYPE=summary_with_knowledge npm start
```

#### Different Test Scenarios
```bash
# Test with customer service scenarios
TEST_FACTS_FILE=customer npm start

# Test with technical documentation
TEST_FACTS_FILE=technical npm start

# Test with casual conversation
TEST_FACTS_FILE=casual npm start
```

#### Performance Testing
```bash
# Large-scale testing
TEST_FACTS_COUNT=50 MESSAGES_BETWEEN_FACTS=10 npm start

# Quick validation
TEST_FACTS_COUNT=3 MESSAGES_BETWEEN_FACTS=2 npm start
```

## 📁 Project Structure

The framework follows a modular architecture designed for extensibility and maintainability:

```
llm-memory-test/
├── src/                              # Source code
│   ├── app.js                        # Main application entry point
│   ├── config/                       # Configuration management
│   │   ├── configManager.js          # Centralized configuration with validation
│   │   ├── defaults.js               # Default configuration values
│   │   └── validator.js              # Configuration validation logic
│   ├── memory/                       # Memory implementations
│   │   ├── memoryInterface.js        # Abstract base class for all memory types
│   │   ├── memoryFactory.js          # Factory pattern for memory creation
│   │   ├── simpleMemory.js           # Simple FIFO memory implementation
│   │   └── inMemorySummaryMemory.js  # Advanced memory with summarization
│   ├── models/                       # LLM integration
│   │   └── llmModel.js               # LLM API abstraction with retry logic
│   ├── utils/                        # Utility modules
│   │   ├── conversationSimulator.js # Conversation simulation engine
│   │   ├── evaluator.js              # Memory evaluation and scoring
│   │   ├── dataLoader.js             # Test data loading and validation
│   │   ├── prompts.js                # System prompts for different roles
│   │   ├── logger.js                 # Structured logging system
│   │   ├── performanceTracker.js     # Performance monitoring
│   │   └── retryUtils.js             # Retry logic and error handling
│   └── validation/                   # Data validation
│       ├── schemas/                  # JSON schemas for validation
│       └── validator.js              # Data validation utilities
├── data/                             # Test data files
│   ├── test_facts_simple.json        # Basic test scenarios
│   ├── test_facts_customer.json      # Customer service scenarios
│   ├── test_facts_casual.json        # Casual conversation scenarios
│   └── test_facts_*.json             # Additional test scenarios
├── test_results/                     # Generated test results
├── coverage/                         # Test coverage reports
├── .env.example                      # Example configuration file
├── CONFIGURATION.md                  # Detailed configuration guide
├── package.json                      # Project dependencies and scripts
└── README.md                         # This file
```

### Key Components

- **Configuration System**: Centralized, validated configuration management
- **Memory Implementations**: Pluggable memory architectures with common interface
- **LLM Integration**: Robust API client with error handling and retry logic
- **Evaluation Framework**: Comprehensive scoring and analysis system
- **Performance Monitoring**: Built-in performance tracking and metrics
- **Logging System**: Structured logging with configurable levels

## 📊 Understanding Results

The framework generates comprehensive test results that provide detailed insights into memory performance.

### Result Files

Test results are automatically saved to the `test_results/` directory with descriptive filenames:
```
results_[timestamp]_[memoryType]_[scenario]_[sessionId].md
```

Example: `results_1746726531_summary_customer_3f59bfcb-1fb4-41ee-8344-571bd7f2f8de.md`

### Result Contents

Each result file contains:

#### 📋 Test Configuration
- Memory type and parameters used
- Model configurations for each role
- Test scenario and fact count
- Session metadata and timestamps

#### 🎯 Performance Metrics
- **Overall Score**: Aggregate memory retention percentage
- **Simple Facts Score**: Performance on straightforward information
- **Complex Facts Score**: Performance on detailed, multi-part information
- **Individual Fact Evaluation**: Detailed scoring for each tested fact

#### 📈 Statistical Analysis
- Performance trends and patterns
- API call statistics and timing
- Memory usage and efficiency metrics
- Error rates and retry statistics

#### 💬 Conversation Log
- Complete conversation transcript
- Fact injection points and timing
- Memory context at each evaluation point
- LLM responses and evaluation reasoning

#### 🔍 Detailed Evaluation
For each fact tested:
- Original fact content
- Question asked to test retention
- Expected vs. actual response
- Confidence score and reasoning
- Memory context used for the response

### Interpreting Scores

- **90-100%**: Excellent memory retention
- **70-89%**: Good memory performance with minor gaps
- **50-69%**: Moderate performance, some information loss
- **30-49%**: Poor retention, significant memory issues
- **0-29%**: Very poor performance, major memory failures

### Performance Analysis

The framework tracks several key performance indicators:

- **Response Time**: Average time for LLM responses
- **API Efficiency**: Successful API calls vs. retries
- **Memory Efficiency**: Context window utilization
- **Evaluation Accuracy**: Consistency of evaluation scoring

## 🔧 Troubleshooting

### Common Issues and Solutions

#### Installation Problems

**Issue**: `npm install` fails with permission errors
```bash
# Solution: Use npm with proper permissions or use a Node version manager
npm install --unsafe-perm
# OR
nvm use 18  # Use Node Version Manager
```

**Issue**: Node.js version compatibility
```bash
# Check your Node.js version
node --version

# Should be 18.0.0 or higher
# Update Node.js if needed
```

#### Configuration Issues

**Issue**: "No API key configured" error
```bash
# Solution: Ensure at least one API key is set in .env
OPENROUTER_API_KEY=your_key_here
# OR
OPENAI_API_KEY=your_key_here
```

**Issue**: "Invalid temperature" or other validation errors
```bash
# Solution: Check CONFIGURATION.md for valid ranges
# Temperature should be between 0.0 and 2.0
USER_TEMPERATURE=0.8
```

**Issue**: "SUMMARY_THRESHOLD must be greater than MEMORY_CONTEXT_WINDOW"
```bash
# Solution: Adjust the values in .env
MEMORY_CONTEXT_WINDOW=10
SUMMARY_THRESHOLD=25  # Must be > context window
```

#### Runtime Issues

**Issue**: API rate limiting or timeout errors
```bash
# Solution: The framework has built-in retry logic, but you can:
# 1. Use a different model with higher rate limits
# 2. Reduce TEST_FACTS_COUNT for smaller tests
# 3. Increase delays between API calls (check model configuration)
```

**Issue**: Out of memory errors during large tests
```bash
# Solution: Reduce test size or adjust memory settings
TEST_FACTS_COUNT=10        # Reduce from larger numbers
MEMORY_CONTEXT_WINDOW=8    # Reduce context window
```

**Issue**: Inconsistent evaluation results
```bash
# Solution: Use lower temperature for evaluator model
EVALUATOR_TEMPERATURE=0.1  # More consistent evaluation
```

### Getting Help

1. **Check the logs**: Enable verbose logging to see detailed execution information
   ```bash
   VERBOSE_LOGGING=true npm start
   ```

2. **Validate configuration**: The framework provides detailed validation feedback on startup

3. **Start small**: Begin with simple tests and gradually increase complexity
   ```bash
   TEST_FACTS_COUNT=3
   MESSAGES_BETWEEN_FACTS=2
   MEMORY_TYPE=simple
   ```

4. **Review examples**: Check the configuration examples in [CONFIGURATION.md](CONFIGURATION.md)

### Performance Optimization

- **Use faster models** for development and testing (e.g., `gpt-3.5-turbo` vs `gpt-4`)
- **Reduce token limits** for faster responses
- **Optimize memory settings** based on your use case
- **Use appropriate test sizes** for your hardware and API limits

## 🔌 Extending the Framework

The LLM Memory Test Framework is designed with extensibility as a core principle. The modular architecture makes it easy to add new memory implementations, evaluation methods, and data sources.

### 🧠 Adding New Memory Implementations

The framework uses a plugin-style architecture for memory implementations. Here's how to add your own:

#### Step 1: Create Your Memory Class

Create a new file in `src/memory/` that extends the `MemoryInterface`:

```javascript
// src/memory/vectorMemory.js
const MemoryInterface = require('./memoryInterface');
const { Logger } = require('../utils/logger');

class VectorMemory extends MemoryInterface {
  constructor(sessionId, config) {
    super(sessionId);
    this.config = config;
    this.logger = new Logger('VectorMemory', sessionId);
    this.vectors = new Map(); // Your vector storage
    this.embeddings = []; // Your embedding storage

    this.logger.info('VectorMemory initialized', {
      sessionId,
      config: this.config.getMemoryConfig()
    });
  }

  /**
   * Add a message to vector memory with embedding
   */
  async addMessage(message) {
    try {
      this.logger.debug('Adding message to vector memory', { message });

      // Validate message format
      if (!message || !message.content) {
        throw new Error('Invalid message format');
      }

      // Generate embedding (implement your embedding logic)
      const embedding = await this.generateEmbedding(message.content);

      // Store message with embedding
      const messageId = `${Date.now()}-${Math.random()}`;
      this.vectors.set(messageId, {
        ...message,
        embedding,
        timestamp: new Date().toISOString()
      });

      this.logger.debug('Message added to vector memory', { messageId });
    } catch (error) {
      this.logger.error('Failed to add message to vector memory', { error: error.message });
      throw error;
    }
  }

  /**
   * Get relevant context using vector similarity
   */
  async getMemoryContext(currentMessage = null) {
    try {
      if (this.vectors.size === 0) {
        return '';
      }

      let relevantMessages;

      if (currentMessage) {
        // Find most relevant messages using vector similarity
        relevantMessages = await this.findSimilarMessages(currentMessage);
      } else {
        // Return most recent messages as fallback
        relevantMessages = Array.from(this.vectors.values())
          .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
          .slice(0, this.config.getMemoryContextWindow());
      }

      return this.formatContext(relevantMessages);
    } catch (error) {
      this.logger.error('Failed to get memory context', { error: error.message });
      throw error;
    }
  }

  /**
   * Clear all stored vectors and messages
   */
  async clearMemory() {
    this.vectors.clear();
    this.embeddings = [];
    this.logger.info('Vector memory cleared');
  }

  /**
   * Get memory statistics
   */
  async getStats() {
    return {
      ...await super.getStats(),
      type: 'VectorMemory',
      messageCount: this.vectors.size,
      embeddingCount: this.embeddings.length
    };
  }

  // Private helper methods
  async generateEmbedding(text) {
    // Implement your embedding generation logic
    // This could use OpenAI embeddings, sentence transformers, etc.
    return Array(384).fill(0).map(() => Math.random()); // Placeholder
  }

  async findSimilarMessages(currentMessage, topK = 5) {
    // Implement vector similarity search
    // Return most similar messages based on embedding similarity
    return Array.from(this.vectors.values()).slice(0, topK);
  }

  formatContext(messages) {
    return messages
      .map(msg => `${msg.role}: ${msg.content}`)
      .join('\n');
  }
}

module.exports = VectorMemory;
```

#### Step 2: Register in Factory

Add your memory type to the factory in `src/memory/memoryFactory.js`:

```javascript
// Add import at the top
const VectorMemory = require('./vectorMemory');

// Add case in createMemory method
case 'vector':
  return new VectorMemory(sessionId, config);

// Add to getSupportedTypes method
static getSupportedTypes() {
  return ['simple', 'summary', 'summary_with_knowledge', 'vector'];
}
```

#### Step 3: Add Configuration Support

Update your `.env` file to support the new memory type:

```bash
MEMORY_TYPE=vector
VECTOR_DIMENSION=384
SIMILARITY_THRESHOLD=0.7
```

#### Step 4: Test Your Implementation

Create tests for your new memory implementation:

```javascript
// src/__tests__/memory/vectorMemory.test.js
const VectorMemory = require('../../memory/vectorMemory');
const { ConfigManager } = require('../../config/configManager');

describe('VectorMemory', () => {
  let memory;
  let config;

  beforeEach(() => {
    config = new ConfigManager();
    memory = new VectorMemory('test-session', config);
  });

  test('should add and retrieve messages', async () => {
    const message = { role: 'user', content: 'Hello world' };
    await memory.addMessage(message);

    const context = await memory.getMemoryContext();
    expect(context).toContain('Hello world');
  });

  test('should find similar messages', async () => {
    await memory.addMessage({ role: 'user', content: 'I love pizza' });
    await memory.addMessage({ role: 'user', content: 'Pizza is great' });
    await memory.addMessage({ role: 'user', content: 'I hate vegetables' });

    const context = await memory.getMemoryContext({
      role: 'user',
      content: 'Tell me about pizza'
    });

    expect(context).toContain('pizza');
  });
});
```

### 📝 Adding New Test Scenarios

The framework supports custom test scenarios through JSON files. Here's how to create your own:

#### Step 1: Create Test Facts File

Create a new JSON file in the `data/` directory:

```json
// data/test_facts_medical.json
{
  "simple": [
    {
      "fact": "Aspirin is commonly used to reduce fever and relieve pain.",
      "question": "What is aspirin commonly used for?",
      "answer": "Aspirin is commonly used to reduce fever and relieve pain."
    },
    {
      "fact": "The normal human body temperature is approximately 98.6°F (37°C).",
      "question": "What is the normal human body temperature?",
      "answer": "The normal human body temperature is approximately 98.6°F or 37°C."
    }
  ],
  "complex": [
    {
      "fact": "Hypertension, also known as high blood pressure, is defined as systolic pressure ≥140 mmHg or diastolic pressure ≥90 mmHg. It affects approximately 45% of adults and is a major risk factor for cardiovascular disease, stroke, and kidney disease.",
      "question": "What defines hypertension and what are its health implications?",
      "answer": "Hypertension is defined as systolic pressure ≥140 mmHg or diastolic pressure ≥90 mmHg. It affects about 45% of adults and is a major risk factor for cardiovascular disease, stroke, and kidney disease."
    },
    {
      "fact": "The human immune system consists of two main components: innate immunity (first line of defense including skin, mucous membranes, and white blood cells) and adaptive immunity (specific responses involving T-cells and B-cells that create immunological memory).",
      "question": "Describe the two main components of the human immune system.",
      "answer": "The immune system has innate immunity (first line defense with skin, mucous membranes, and white blood cells) and adaptive immunity (specific T-cell and B-cell responses that create immunological memory)."
    }
  ]
}
```

#### Step 2: Validate Your Test Facts

The framework includes validation to ensure your test facts are properly formatted:

```bash
# Test your new facts file
TEST_FACTS_FILE=medical npm start
```

#### Step 3: Best Practices for Test Facts

**Simple Facts Guidelines:**
- Single, clear piece of information
- Easy to verify with a direct question
- 1-2 sentences maximum
- Factual and unambiguous

**Complex Facts Guidelines:**
- Multiple related pieces of information
- Requires understanding relationships
- 2-4 sentences with interconnected details
- Tests deeper comprehension

**Question Design:**
- Questions should be specific and unambiguous
- Avoid yes/no questions when possible
- Test actual recall, not inference
- Match the complexity level of the fact

#### Step 4: Domain-Specific Scenarios

Create specialized test scenarios for different domains:

```bash
# Business/Corporate
data/test_facts_business.json

# Scientific/Technical
data/test_facts_science.json

# Historical
data/test_facts_history.json

# Legal
data/test_facts_legal.json
```

Each domain can test memory performance in specific contexts and vocabularies.

### 🎯 Customizing Evaluation Logic

The evaluation system is designed to be extensible. Here's how to customize it:

#### Custom Scoring Algorithms

Create a custom evaluator by extending the base evaluator:

```javascript
// src/utils/customEvaluator.js
const Evaluator = require('./evaluator');

class SemanticEvaluator extends Evaluator {
  constructor(config) {
    super(config);
    this.semanticThreshold = 0.8; // Similarity threshold
  }

  /**
   * Custom evaluation using semantic similarity
   */
  async evaluateResponse(fact, question, response, context) {
    // Use your custom evaluation logic
    const semanticScore = await this.calculateSemanticSimilarity(
      fact.answer,
      response
    );

    const confidence = semanticScore > this.semanticThreshold ? 'high' : 'low';

    return {
      score: Math.round(semanticScore * 100),
      confidence,
      reasoning: `Semantic similarity: ${semanticScore.toFixed(3)}`,
      details: {
        expectedAnswer: fact.answer,
        actualResponse: response,
        semanticScore
      }
    };
  }

  async calculateSemanticSimilarity(text1, text2) {
    // Implement your semantic similarity calculation
    // This could use embeddings, BERT similarity, etc.
    return 0.85; // Placeholder
  }
}

module.exports = SemanticEvaluator;
```

#### Custom Evaluation Metrics

Add new evaluation dimensions:

```javascript
// Custom evaluation with multiple metrics
async evaluateMemoryComprehensive(memory, facts, conversationLog) {
  const results = [];

  for (const fact of facts) {
    const evaluation = await this.evaluateMultiDimensional(fact, memory);
    results.push(evaluation);
  }

  return {
    overall: this.calculateOverallScore(results),
    accuracy: this.calculateAccuracy(results),
    completeness: this.calculateCompleteness(results),
    consistency: this.calculateConsistency(results),
    results
  };
}
```

### 🔌 Adding New LLM Providers

The framework supports multiple LLM providers through a unified interface:

#### Step 1: Extend the LLM Model

```javascript
// src/models/anthropicModel.js
const LLMModel = require('./llmModel');

class AnthropicModel extends LLMModel {
  constructor(config) {
    super(config);
    this.baseURL = 'https://api.anthropic.com/v1';
    this.apiKey = config.getAnthropicApiKey();
  }

  async generateResponse(messages, options = {}) {
    // Implement Anthropic-specific API calls
    const response = await this.makeApiCall('/messages', {
      model: options.model || 'claude-3-sonnet',
      messages: this.formatMessages(messages),
      max_tokens: options.maxTokens || 150,
      temperature: options.temperature || 0.7
    });

    return this.parseResponse(response);
  }

  formatMessages(messages) {
    // Convert to Anthropic message format
    return messages.map(msg => ({
      role: msg.role === 'assistant' ? 'assistant' : 'user',
      content: msg.content
    }));
  }
}

module.exports = AnthropicModel;
```

#### Step 2: Update Configuration

Add provider configuration:

```bash
# .env
ANTHROPIC_API_KEY=your_anthropic_key
ANTHROPIC_BASE_URL=https://api.anthropic.com/v1
```

#### Step 3: Register Provider

Update the model factory to support the new provider:

```javascript
// src/models/modelFactory.js
const AnthropicModel = require('./anthropicModel');

class ModelFactory {
  static createModel(provider, config) {
    switch (provider) {
      case 'openai':
        return new OpenAIModel(config);
      case 'openrouter':
        return new OpenRouterModel(config);
      case 'anthropic':
        return new AnthropicModel(config);
      default:
        throw new Error(`Unsupported provider: ${provider}`);
    }
  }
}
```

### 🔧 Advanced Customizations

#### Custom Conversation Patterns

Create specialized conversation simulators:

```javascript
// src/utils/technicalConversationSimulator.js
const ConversationSimulator = require('./conversationSimulator');

class TechnicalConversationSimulator extends ConversationSimulator {
  constructor(memory, config) {
    super(memory, config);
    this.technicalTerms = ['API', 'database', 'algorithm', 'framework'];
  }

  async generateUserMessage(context, factToInject = null) {
    // Generate technical-focused user messages
    const baseMessage = await super.generateUserMessage(context, factToInject);
    return this.addTechnicalContext(baseMessage);
  }

  addTechnicalContext(message) {
    // Add technical terminology and context
    return message; // Implement your logic
  }
}
```

#### Custom Performance Metrics

Add domain-specific performance tracking:

```javascript
// src/utils/domainPerformanceTracker.js
const PerformanceTracker = require('./performanceTracker');

class DomainPerformanceTracker extends PerformanceTracker {
  constructor(sessionId, domain) {
    super(sessionId);
    this.domain = domain;
    this.domainMetrics = new Map();
  }

  trackDomainSpecificMetric(metricName, value) {
    if (!this.domainMetrics.has(metricName)) {
      this.domainMetrics.set(metricName, []);
    }
    this.domainMetrics.get(metricName).push({
      value,
      timestamp: Date.now()
    });
  }

  getDomainReport() {
    return {
      domain: this.domain,
      metrics: Object.fromEntries(this.domainMetrics),
      summary: this.calculateDomainSummary()
    };
  }
}
```

## 🤝 Contributing

We welcome contributions to the LLM Memory Test Framework! Here's how you can help:

### Types of Contributions

- **Bug Reports**: Found an issue? Please report it with detailed steps to reproduce
- **Feature Requests**: Have an idea for improvement? We'd love to hear it
- **Code Contributions**: Submit pull requests for bug fixes or new features
- **Documentation**: Help improve documentation and examples
- **Test Scenarios**: Contribute new test fact files for different domains

### Development Setup

1. Fork the repository
2. Clone your fork: `git clone <your-fork-url>`
3. Install dependencies: `npm install`
4. Create a feature branch: `git checkout -b feature/your-feature-name`
5. Make your changes and add tests
6. Run the test suite: `npm test`
7. Submit a pull request

### Code Standards

- Follow the existing code style (ESLint configuration provided)
- Add JSDoc comments for new functions and classes
- Include unit tests for new functionality
- Update documentation as needed

## 📚 Additional Resources

### Related Projects

- **LangChain**: Framework for developing applications with LLMs
- **OpenAI API**: Direct access to OpenAI models
- **Anthropic Claude**: Alternative LLM provider
- **Hugging Face Transformers**: Open-source transformer models

### Research Papers

- "Attention Is All You Need" - Transformer architecture
- "Memory-Augmented Neural Networks" - Memory mechanisms in neural networks
- "Retrieval-Augmented Generation" - Combining retrieval with generation

### Community

- Join discussions about memory architectures and evaluation methodologies
- Share your custom memory implementations and test results
- Collaborate on research and development

## 📄 License

This project is licensed under the ISC License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- OpenAI and Anthropic for providing powerful LLM APIs
- The open-source community for inspiration and tools
- Contributors who help improve this framework

---

**Built with ❤️ for the AI research and development community**

For questions, issues, or contributions, please visit our [GitHub repository](https://github.com/your-repo/llm-memory-test).
