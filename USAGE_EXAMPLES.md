# Usage Examples and Common Scenarios

This document provides practical examples of how to use the LLM Memory Test Framework for different testing scenarios and use cases.

## 🚀 Quick Start Examples

### Basic Memory Testing

Test simple memory with default settings:

```bash
# Set up basic configuration
echo "OPENROUTER_API_KEY=your_key_here" > .env
echo "MEMORY_TYPE=simple" >> .env
echo "TEST_FACTS_COUNT=5" >> .env
echo "SAVE_RESULTS=true" >> .env

# Run the test
npm start
```

### Comparing Memory Types

Test different memory implementations:

```bash
# Test simple memory
MEMORY_TYPE=simple npm start

# Test summary memory
MEMORY_TYPE=summary npm start

# Test advanced memory with knowledge extraction
MEMORY_TYPE=summary_with_knowledge npm start
```

## 🎯 Domain-Specific Testing

### Customer Service Scenarios

Test memory performance in customer service contexts:

```bash
# Customer service configuration
cat > .env << EOF
OPENROUTER_API_KEY=your_key_here
MEMORY_TYPE=summary_with_knowledge
TEST_FACTS_FILE=customer
TEST_FACTS_COUNT=15
MESSAGES_BETWEEN_FACTS=8
VERBOSE_LOGGING=true

# Optimize for customer service
USER_MODEL=anthropic/claude-3-haiku:beta
USER_TEMPERATURE=0.9
ASSISTANT_MODEL=anthropic/claude-3-haiku:beta
ASSISTANT_TEMPERATURE=0.6
EVALUATOR_TEMPERATURE=0.2
EOF

npm start
```

### Technical Documentation Testing

Test memory with technical information:

```bash
# Technical documentation configuration
cat > .env << EOF
OPENROUTER_API_KEY=your_key_here
MEMORY_TYPE=summary
TEST_FACTS_FILE=technical
TEST_FACTS_COUNT=20
MESSAGES_BETWEEN_FACTS=6

# Technical-focused models
USER_MODEL=openai/gpt-4
ASSISTANT_MODEL=openai/gpt-4
EVALUATOR_MODEL=openai/gpt-4
EVALUATOR_TEMPERATURE=0.1
EOF

npm start
```

### Medical Information Testing

Test memory with medical facts:

```bash
# Medical information configuration
cat > .env << EOF
OPENROUTER_API_KEY=your_key_here
MEMORY_TYPE=summary_with_knowledge
TEST_FACTS_FILE=medical
TEST_FACTS_COUNT=12
MESSAGES_BETWEEN_FACTS=10

# Conservative settings for medical accuracy
ASSISTANT_TEMPERATURE=0.3
EVALUATOR_TEMPERATURE=0.1
SUMMARY_TEMPERATURE=0.2
KNOWLEDGE_EXTRACTION_TEMPERATURE=0.2
EOF

npm start
```

## 📊 Performance Testing Scenarios

### High-Volume Testing

Test memory performance with large conversation volumes:

```bash
# High-volume configuration
cat > .env << EOF
OPENROUTER_API_KEY=your_key_here
MEMORY_TYPE=summary
TEST_FACTS_COUNT=50
MESSAGES_BETWEEN_FACTS=15
MEMORY_CONTEXT_WINDOW=20
SUMMARY_THRESHOLD=40

# Performance optimizations
USER_MAX_TOKENS=100
ASSISTANT_MAX_TOKENS=120
EVALUATOR_MAX_TOKENS=80
EOF

npm start
```

### Memory Efficiency Testing

Test how efficiently different memory types use context:

```bash
# Memory efficiency test
for memory_type in simple summary summary_with_knowledge; do
  echo "Testing $memory_type memory..."
  MEMORY_TYPE=$memory_type \
  TEST_FACTS_COUNT=25 \
  MESSAGES_BETWEEN_FACTS=12 \
  VERBOSE_LOGGING=true \
  npm start
done
```

### Stress Testing

Test framework limits and error handling:

```bash
# Stress test configuration
cat > .env << EOF
OPENROUTER_API_KEY=your_key_here
MEMORY_TYPE=summary_with_knowledge
TEST_FACTS_COUNT=100
MESSAGES_BETWEEN_FACTS=20
MEMORY_CONTEXT_WINDOW=50
SUMMARY_THRESHOLD=100

# Enable detailed monitoring
VERBOSE_LOGGING=true
NODE_ENV=development
EOF

npm start
```

## 🔧 Development and Testing

### Development Setup

Set up the framework for development:

```bash
# Development environment
cat > .env << EOF
OPENROUTER_API_KEY=your_key_here
NODE_ENV=development
VERBOSE_LOGGING=true
SAVE_RESULTS=true

# Quick testing settings
MEMORY_TYPE=simple
TEST_FACTS_COUNT=3
MESSAGES_BETWEEN_FACTS=2
TEST_FACTS_FILE=simple
EOF

# Run in development mode with auto-restart
npm run dev
```

### Testing Custom Memory Implementation

Test a custom memory implementation:

```bash
# Custom memory testing
cat > .env << EOF
OPENROUTER_API_KEY=your_key_here
MEMORY_TYPE=vector  # Your custom memory type
TEST_FACTS_COUNT=10
MESSAGES_BETWEEN_FACTS=5

# Custom memory configuration
VECTOR_DIMENSION=384
SIMILARITY_THRESHOLD=0.7
VERBOSE_LOGGING=true
EOF

npm start
```

### A/B Testing Different Configurations

Compare different configurations systematically:

```bash
#!/bin/bash
# A/B testing script

configurations=(
  "simple,10,5"
  "summary,15,8"
  "summary_with_knowledge,12,6"
)

for config in "${configurations[@]}"; do
  IFS=',' read -r memory_type facts_count messages_between <<< "$config"
  
  echo "Testing: $memory_type with $facts_count facts, $messages_between messages between"
  
  MEMORY_TYPE=$memory_type \
  TEST_FACTS_COUNT=$facts_count \
  MESSAGES_BETWEEN_FACTS=$messages_between \
  SAVE_RESULTS=true \
  npm start
  
  echo "Completed: $memory_type"
  echo "---"
done
```

## 🎨 Custom Scenarios

### Creating Custom Test Facts

Create domain-specific test scenarios:

```json
// data/test_facts_legal.json
{
  "simple": [
    {
      "fact": "A contract requires offer, acceptance, and consideration to be legally binding.",
      "question": "What three elements are required for a legally binding contract?",
      "answer": "A legally binding contract requires offer, acceptance, and consideration."
    }
  ],
  "complex": [
    {
      "fact": "Under the doctrine of promissory estoppel, a promise may be enforceable even without consideration if the promisee reasonably relied on the promise to their detriment, the promisor should have expected such reliance, and injustice can only be avoided by enforcing the promise.",
      "question": "Explain the doctrine of promissory estoppel and its requirements.",
      "answer": "Promissory estoppel allows enforcement of promises without consideration when there is reasonable detrimental reliance by the promisee, expected reliance by the promisor, and enforcement is necessary to avoid injustice."
    }
  ]
}
```

### Custom Evaluation Criteria

Implement domain-specific evaluation:

```javascript
// Custom evaluator for legal accuracy
class LegalEvaluator extends Evaluator {
  async evaluateResponse(fact, question, response, context) {
    const baseEvaluation = await super.evaluateResponse(fact, question, response, context);
    
    // Add legal-specific criteria
    const legalAccuracy = this.assessLegalAccuracy(fact, response);
    const citationQuality = this.assessCitations(response);
    const riskAssessment = this.assessLegalRisk(response);
    
    return {
      ...baseEvaluation,
      legalMetrics: {
        accuracy: legalAccuracy,
        citations: citationQuality,
        risk: riskAssessment
      }
    };
  }
}
```

## 📈 Analysis and Reporting

### Automated Report Generation

Generate comprehensive analysis reports:

```bash
# Run multiple tests and generate comparison report
#!/bin/bash

echo "Running comprehensive memory analysis..."

# Test different memory types
for memory_type in simple summary summary_with_knowledge; do
  echo "Testing $memory_type..."
  MEMORY_TYPE=$memory_type \
  TEST_FACTS_COUNT=20 \
  MESSAGES_BETWEEN_FACTS=8 \
  SAVE_RESULTS=true \
  npm start
done

echo "Analysis complete. Check test_results/ directory for detailed reports."
```

### Performance Monitoring

Monitor framework performance over time:

```bash
# Performance monitoring script
cat > monitor_performance.sh << 'EOF'
#!/bin/bash

while true; do
  echo "$(date): Starting performance test..."
  
  MEMORY_TYPE=summary \
  TEST_FACTS_COUNT=15 \
  MESSAGES_BETWEEN_FACTS=6 \
  VERBOSE_LOGGING=true \
  npm start
  
  echo "$(date): Test completed. Waiting 1 hour..."
  sleep 3600
done
EOF

chmod +x monitor_performance.sh
./monitor_performance.sh
```

## 🔍 Debugging and Troubleshooting

### Debug Mode

Run with maximum debugging information:

```bash
# Debug configuration
cat > .env << EOF
OPENROUTER_API_KEY=your_key_here
NODE_ENV=development
VERBOSE_LOGGING=true

# Small test for debugging
MEMORY_TYPE=simple
TEST_FACTS_COUNT=2
MESSAGES_BETWEEN_FACTS=1
TEST_FACTS_FILE=simple

# Detailed model logging
USER_TEMPERATURE=0.8
ASSISTANT_TEMPERATURE=0.7
EVALUATOR_TEMPERATURE=0.2
EOF

# Run with Node.js debugging
node --inspect src/app.js
```

### Error Recovery Testing

Test error handling and recovery:

```bash
# Test with invalid API key to test error handling
OPENROUTER_API_KEY=invalid_key \
MEMORY_TYPE=simple \
TEST_FACTS_COUNT=2 \
npm start

# Test with extreme configurations
MEMORY_TYPE=summary \
SUMMARY_THRESHOLD=5 \
MEMORY_CONTEXT_WINDOW=10 \
npm start
```

## 🎯 Production Scenarios

### Production Monitoring

Set up production-ready monitoring:

```bash
# Production configuration
cat > .env << EOF
OPENROUTER_API_KEY=your_production_key
NODE_ENV=production
VERBOSE_LOGGING=false
SAVE_RESULTS=true

# Production-optimized settings
MEMORY_TYPE=summary_with_knowledge
TEST_FACTS_COUNT=30
MESSAGES_BETWEEN_FACTS=10
MEMORY_CONTEXT_WINDOW=15
SUMMARY_THRESHOLD=30

# Conservative model settings
USER_TEMPERATURE=0.7
ASSISTANT_TEMPERATURE=0.6
EVALUATOR_TEMPERATURE=0.1
EOF

npm start
```

### Batch Processing

Process multiple test scenarios in batch:

```bash
# Batch processing script
scenarios=("customer" "technical" "casual" "medical")

for scenario in "${scenarios[@]}"; do
  echo "Processing $scenario scenario..."
  
  TEST_FACTS_FILE=$scenario \
  TEST_FACTS_COUNT=25 \
  MESSAGES_BETWEEN_FACTS=8 \
  SAVE_RESULTS=true \
  npm start
  
  echo "$scenario completed."
done

echo "All scenarios processed. Results saved to test_results/"
```

These examples demonstrate the flexibility and power of the LLM Memory Test Framework across various use cases and scenarios.
