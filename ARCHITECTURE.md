# LLM Memory Test Framework - Architecture Guide

This document provides a comprehensive overview of the framework's architecture, design patterns, and extension points for developers who want to understand or extend the system.

## 🏗️ System Architecture

### High-Level Overview

The LLM Memory Test Framework follows a modular, plugin-based architecture designed for extensibility and maintainability. The system is organized into distinct layers with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────────┐
│                    Application Layer                        │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │   app.js    │  │ CLI Tools   │  │  Test Runners       │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    Service Layer                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │Conversation │  │  Evaluator  │  │ Performance Tracker │  │
│  │ Simulator   │  │             │  │                     │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    Memory Layer                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │   Simple    │  │   Summary   │  │    Custom Memory    │  │
│  │   Memory    │  │   Memory    │  │  Implementations    │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    Infrastructure Layer                     │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │   Config    │  │   Logger    │  │    LLM Models       │  │
│  │  Manager    │  │             │  │                     │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

### Core Components

#### 1. Configuration Management (`src/config/`)
- **ConfigManager**: Centralized configuration with validation
- **Validator**: JSON Schema-based validation
- **Defaults**: Default configuration values

#### 2. Memory Implementations (`src/memory/`)
- **MemoryInterface**: Abstract base class defining the memory contract
- **MemoryFactory**: Factory pattern for creating memory instances
- **SimpleMemory**: FIFO memory implementation
- **InMemorySummaryMemory**: Advanced memory with summarization

#### 3. LLM Integration (`src/models/`)
- **LLMModel**: Unified interface for different LLM providers
- **Retry Logic**: Robust error handling and retry mechanisms
- **Rate Limiting**: Built-in rate limiting and backoff strategies

#### 4. Utilities (`src/utils/`)
- **ConversationSimulator**: Orchestrates conversation simulation
- **Evaluator**: Memory evaluation and scoring system
- **DataLoader**: Test data loading and validation
- **Logger**: Structured logging system
- **PerformanceTracker**: Performance monitoring and metrics

## 🔧 Design Patterns

### 1. Factory Pattern
Used extensively for creating instances with proper configuration:

```javascript
// Memory Factory
const memory = MemoryFactory.createMemory('summary', sessionId, config);

// Model Factory (conceptual)
const model = ModelFactory.createModel('openai', config);
```

### 2. Strategy Pattern
Memory implementations use the strategy pattern:

```javascript
class MemoryInterface {
  // Common interface
  async addMessage(message) { throw new Error('Not implemented'); }
  async getMemoryContext() { throw new Error('Not implemented'); }
}

class SimpleMemory extends MemoryInterface {
  // Simple strategy implementation
}

class SummaryMemory extends MemoryInterface {
  // Summary strategy implementation
}
```

### 3. Observer Pattern
Performance tracking and logging use observer-like patterns:

```javascript
performanceTracker.startOperation('memory-operation');
// ... operation code ...
performanceTracker.endOperation(operationId, metadata);
```

### 4. Template Method Pattern
Base classes provide template methods with extension points:

```javascript
class MemoryInterface {
  async addMessage(message) {
    await this.validateMessage(message);  // Template step
    await this.storeMessage(message);     // Abstract step
    await this.postProcessMessage(message); // Template step
  }
}
```

## 🔌 Extension Points

### 1. Memory Implementation Extension

The memory system is designed for easy extension. Key extension points:

#### Abstract Methods (Must Implement)
```javascript
class CustomMemory extends MemoryInterface {
  async addMessage(message) {
    // Store message in your custom format
  }

  async getMemoryContext(currentMessage) {
    // Return formatted context string
  }

  async clearMemory() {
    // Clear stored data
  }
}
```

#### Optional Methods (Can Override)
```javascript
class CustomMemory extends MemoryInterface {
  async getStats() {
    // Return custom statistics
    return {
      ...await super.getStats(),
      customMetric: this.calculateCustomMetric()
    };
  }

  async validateMessage(message) {
    // Custom validation logic
    await super.validateMessage(message);
    // Additional validation
  }
}
```

### 2. Evaluation System Extension

The evaluation system supports custom scoring algorithms:

```javascript
class CustomEvaluator extends Evaluator {
  async evaluateResponse(fact, question, response, context) {
    // Custom evaluation logic
    const score = await this.customScoringAlgorithm(fact, response);
    
    return {
      score,
      confidence: score > 80 ? 'high' : 'low',
      reasoning: 'Custom evaluation reasoning',
      details: { /* custom details */ }
    };
  }
}
```

### 3. LLM Provider Extension

Add new LLM providers by extending the base model:

```javascript
class CustomLLMProvider extends LLMModel {
  constructor(config) {
    super(config);
    this.apiKey = config.getCustomProviderApiKey();
    this.baseURL = 'https://api.customprovider.com/v1';
  }

  async generateResponse(messages, options) {
    // Implement provider-specific API calls
    const response = await this.makeApiCall('/chat', {
      messages: this.formatMessages(messages),
      ...options
    });

    return this.parseResponse(response);
  }
}
```

### 4. Data Source Extension

Add custom test data sources:

```javascript
class CustomDataLoader extends DataLoader {
  async loadTestFacts(scenario) {
    if (scenario === 'custom_source') {
      return await this.loadFromCustomSource();
    }
    return await super.loadTestFacts(scenario);
  }

  async loadFromCustomSource() {
    // Load from database, API, etc.
    return {
      simple: [...],
      complex: [...]
    };
  }
}
```

## 📊 Data Flow

### 1. Test Execution Flow

```mermaid
graph TD
    A[Start Test] --> B[Load Configuration]
    B --> C[Validate Configuration]
    C --> D[Create Memory Instance]
    D --> E[Load Test Facts]
    E --> F[Initialize Conversation]
    F --> G[Simulate Conversation]
    G --> H[Inject Facts]
    H --> I[Continue Conversation]
    I --> J[Evaluate Memory]
    J --> K[Calculate Scores]
    K --> L[Generate Report]
    L --> M[Save Results]
```

### 2. Memory Operation Flow

```mermaid
graph TD
    A[Add Message] --> B[Validate Message]
    B --> C[Store Message]
    C --> D[Check Thresholds]
    D --> E{Threshold Exceeded?}
    E -->|Yes| F[Trigger Summarization]
    E -->|No| G[Update Context]
    F --> H[Generate Summary]
    H --> I[Extract Knowledge]
    I --> G
    G --> J[Return Context]
```

### 3. Evaluation Flow

```mermaid
graph TD
    A[Start Evaluation] --> B[Get Memory Context]
    B --> C[Ask Question]
    C --> D[Get LLM Response]
    D --> E[Compare with Expected]
    E --> F[Calculate Score]
    F --> G[Generate Reasoning]
    G --> H[Return Evaluation]
```

## 🔍 Key Interfaces

### Memory Interface
```javascript
class MemoryInterface {
  constructor(sessionId)
  async addMessage(message)
  async getMemoryContext(currentMessage)
  async clearMemory()
  async getStats()
}
```

### Configuration Interface
```javascript
class ConfigManager {
  loadConfiguration()
  validateConfiguration()
  get(path)
  set(path, value)
  getApiConfig()
  getMemoryConfig()
}
```

### Evaluation Interface
```javascript
class Evaluator {
  async evaluateMemory(memory, facts, conversationLog)
  async evaluateResponse(fact, question, response, context)
  calculateOverallScore(results)
}
```

## 🧪 Testing Architecture

### Test Structure

The framework includes comprehensive testing at multiple levels:

```
src/__tests__/
├── memory/                    # Memory implementation tests
│   ├── simpleMemory.test.js
│   ├── summaryMemory.test.js
│   └── memoryFactory.test.js
├── utils/                     # Utility tests
│   ├── evaluator.test.js
│   ├── conversationSimulator.test.js
│   └── dataLoader.test.js
├── config/                    # Configuration tests
│   └── configManager.test.js
└── integration/               # Integration tests
    └── fullWorkflow.test.js
```

### Testing Patterns

#### Unit Testing
```javascript
describe('SimpleMemory', () => {
  let memory;

  beforeEach(() => {
    memory = new SimpleMemory('test-session', mockConfig);
  });

  test('should add and retrieve messages', async () => {
    await memory.addMessage({ role: 'user', content: 'Hello' });
    const context = await memory.getMemoryContext();
    expect(context).toContain('Hello');
  });
});
```

#### Integration Testing
```javascript
describe('Full Workflow Integration', () => {
  test('should complete end-to-end test execution', async () => {
    const config = new ConfigManager();
    const memory = MemoryFactory.createMemory('simple', 'test-session', config);
    const simulator = new ConversationSimulator(memory, config);

    const result = await simulator.runConversation(testFacts, 3);
    expect(result.conversationLog).toBeDefined();
    expect(result.usedFacts).toHaveLength(testFacts.length);
  });
});
```

#### Mock Strategies
```javascript
// Mock LLM responses for consistent testing
const mockLLMModel = {
  generateResponse: jest.fn().mockResolvedValue({
    content: 'Mocked response',
    usage: { tokens: 50 }
  })
};
```

## 🔐 Security Considerations

### API Key Management
- Environment variable-based configuration
- No hardcoded credentials in source code
- Support for multiple API providers

### Input Validation
- JSON Schema validation for all configuration
- Message format validation
- Sanitization of user inputs

### Error Handling
- Comprehensive error catching and logging
- Graceful degradation on API failures
- Circuit breaker pattern for persistent failures

## 📈 Performance Considerations

### Memory Efficiency
- Configurable context windows to limit memory usage
- Automatic cleanup of old messages in simple memory
- Efficient summarization to reduce storage requirements

### API Efficiency
- Built-in retry logic with exponential backoff
- Rate limiting awareness
- Connection pooling and reuse

### Monitoring
- Performance tracking for all operations
- API call statistics and timing
- Memory usage monitoring

## 🔄 Lifecycle Management

### Session Management
```javascript
// Each test run gets a unique session ID
const sessionId = uuidv4();
const memory = MemoryFactory.createMemory(type, sessionId, config);

// Session cleanup
await memory.clearMemory();
```

### Resource Cleanup
```javascript
class MemoryInterface {
  async cleanup() {
    // Override in implementations for custom cleanup
    await this.clearMemory();
    this.logger.info('Memory cleaned up');
  }
}
```

### Error Recovery
```javascript
class ConversationSimulator {
  async runConversation(facts, messagesBetween) {
    try {
      return await this.executeConversation(facts, messagesBetween);
    } catch (error) {
      this.logger.error('Conversation failed', { error });
      await this.cleanup();
      throw error;
    }
  }
}
```

## 🎯 Best Practices for Extension

### 1. Follow Interface Contracts
Always implement all required methods from base interfaces:

```javascript
class CustomMemory extends MemoryInterface {
  // Must implement all abstract methods
  async addMessage(message) { /* implementation */ }
  async getMemoryContext(currentMessage) { /* implementation */ }
  async clearMemory() { /* implementation */ }
}
```

### 2. Use Dependency Injection
Accept configuration and dependencies through constructors:

```javascript
class CustomEvaluator {
  constructor(config, logger, performanceTracker) {
    this.config = config;
    this.logger = logger;
    this.performanceTracker = performanceTracker;
  }
}
```

### 3. Implement Proper Logging
Use structured logging with appropriate levels:

```javascript
this.logger.info('Operation started', { operationId, metadata });
this.logger.debug('Processing step', { step, data });
this.logger.error('Operation failed', { error: error.message, stack: error.stack });
```

### 4. Handle Errors Gracefully
Implement comprehensive error handling:

```javascript
async customOperation() {
  try {
    const result = await this.riskyOperation();
    return result;
  } catch (error) {
    this.logger.error('Custom operation failed', { error: error.message });

    // Attempt recovery
    if (this.canRecover(error)) {
      return await this.recoverFromError(error);
    }

    throw new CustomError(`Operation failed: ${error.message}`, error);
  }
}
```

### 5. Write Comprehensive Tests
Include unit tests for all custom implementations:

```javascript
describe('CustomMemory', () => {
  test('should handle edge cases', async () => {
    // Test edge cases specific to your implementation
  });

  test('should integrate with existing system', async () => {
    // Test integration with other components
  });
});
```

This architecture provides a solid foundation for extending the framework while maintaining clean separation of concerns and testability.
