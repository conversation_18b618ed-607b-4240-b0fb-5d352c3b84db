# Example environment variables for LLM Memory Test Application
# Copy this file to .env and update with your actual values

# API Configuration (at least one required)
OPENROUTER_API_KEY=your_openrouter_api_key_here
# OPENAI_API_KEY=your_openai_api_key_here

# Model Configuration (optional - defaults will be used if not specified)
# USER_MODEL=anthropic/claude-3-haiku:beta
# USER_TEMPERATURE=0.8
# USER_MAX_TOKENS=150

# ASSISTANT_MODEL=anthropic/claude-3-haiku:beta
# ASSISTANT_TEMPERATURE=0.7
# ASSISTANT_MAX_TOKENS=200

# EVALUATOR_MODEL=anthropic/claude-3-haiku:beta
# EVALUATOR_TEMPERATURE=0.2
# EVALUATOR_MAX_TOKENS=100

# SUMMARY_MODEL=anthropic/claude-3-haiku:beta
# SUMMARY_TEMPERATURE=0.3
# SUMMARY_MAX_TOKENS=300

# KNOWLEDGE_EXTRACTION_MODEL=anthropic/claude-3-haiku:beta
# KNOWLEDGE_EXTRACTION_TEMPERATURE=0.3
# KNOWLEDGE_EXTRACTION_MAX_TOKENS=200

# Memory Configuration
MEMORY_TYPE=simple # Options: simple, summary, summary_with_knowledge
MEMORY_CONTEXT_WINDOW=10 # Number of messages to keep in context (1-50)
SUMMARY_THRESHOLD=20 # Messages before summarization (5-200, must be > context window)
ENABLE_KNOWLEDGE_EXTRACTION=false # Enable knowledge extraction in summary memory

# Test Configuration
TEST_FACTS_FILE=casual # Test scenario file (without .json extension)
TEST_FACTS_COUNT=10 # Number of facts to test (1-100)
MESSAGES_BETWEEN_FACTS=5 # Messages between fact introductions (1-20)

# Output Configuration
SAVE_RESULTS=true # Save test results to file
VERBOSE_LOGGING=false # Enable detailed logging

# Environment
NODE_ENV=development # Options: development, test, production
