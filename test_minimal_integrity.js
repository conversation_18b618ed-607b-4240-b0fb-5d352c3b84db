#!/usr/bin/env node

console.log('Testing minimal DataIntegrityChecker...');

try {
  console.log('1. Importing fs...');
  const fs = require('fs').promises;
  console.log('✅ fs imported');

  console.log('2. Importing path...');
  const path = require('path');
  console.log('✅ path imported');

  console.log('3. Importing logger...');
  const { defaultLogger } = require('./src/utils/logger');
  console.log('✅ logger imported');

  console.log('4. Importing SchemaValidator...');
  const SchemaValidator = require('./src/validation/schemaValidator');
  console.log('✅ SchemaValidator imported, type:', typeof SchemaValidator);

  console.log('5. Creating SchemaValidator instance...');
  const validator = new SchemaValidator();
  console.log('✅ SchemaValidator instance created');

  console.log('6. Importing DataIntegrityChecker...');
  const { DataIntegrityChecker } = require('./src/validation/dataIntegrityChecker');
  console.log('✅ DataIntegrityChecker imported, type:', typeof DataIntegrityChecker);

  console.log('7. Creating DataIntegrityChecker instance...');
  const checker = new DataIntegrityChecker();
  console.log('✅ DataIntegrityChecker instance created');

  console.log('🎉 All tests passed!');
} catch (error) {
  console.error('❌ Error:', error.message);
  console.error('Stack:', error.stack);
}