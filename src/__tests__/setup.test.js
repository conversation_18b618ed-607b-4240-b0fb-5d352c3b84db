/**
 * Basic setup test to verify Jest configuration
 */

describe('Development Infrastructure Setup', () => {
  test('Jest is properly configured', () => {
    expect(true).toBe(true);
  });

  test('Global test utilities are available', () => {
    expect(global.testUtils).toBeDefined();
    expect(typeof global.testUtils.createMockLLMResponse).toBe('function');
    expect(typeof global.testUtils.createMockFacts).toBe('function');
    expect(typeof global.testUtils.createMockMemory).toBe('function');
    expect(typeof global.testUtils.wait).toBe('function');
  });

  test('Mock LLM response utility works', () => {
    const mockResponse = global.testUtils.createMockLLMResponse('test content');
    expect(mockResponse).toEqual({
      content: 'test content',
      usage: {
        prompt_tokens: 100,
        completion_tokens: 50,
        total_tokens: 150
      }
    });
  });

  test('Mock facts utility works', () => {
    const facts = global.testUtils.createMockFacts(3);
    expect(facts).toHaveLength(3);
    expect(facts[0]).toHaveProperty('id', 'fact_1');
    expect(facts[0]).toHaveProperty('category');
    expect(facts[0]).toHaveProperty('fact');
    expect(facts[0]).toHaveProperty('complexity');
  });

  test('Mock memory utility works', () => {
    const mockMemory = global.testUtils.createMockMemory();
    expect(mockMemory).toHaveProperty('addMessage');
    expect(mockMemory).toHaveProperty('getContext');
    expect(mockMemory).toHaveProperty('clear');
    expect(jest.isMockFunction(mockMemory.addMessage)).toBe(true);
  });
});