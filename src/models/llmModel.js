/**
 * LLM Model Utility - Advanced Language Model Client
 *
 * A comprehensive utility class for interacting with Large Language Models (LLMs)
 * via OpenRouter or OpenAI APIs. This class provides robust error handling,
 * automatic retry logic with exponential backoff, circuit breaker patterns,
 * comprehensive logging, and performance monitoring.
 *
 * Features:
 * - Multi-provider support (OpenRouter, OpenAI)
 * - Automatic retry with exponential backoff and jitter
 * - Circuit breaker pattern for fault tolerance
 * - Comprehensive error classification and handling
 * - Performance monitoring and statistics tracking
 * - Request/response logging with configurable verbosity
 * - Rate limiting awareness and backoff strategies
 * - Health checking and diagnostics
 * - Mock response support for testing environments
 *
 * @class LLMModel
 * 
 * @example
 * // Basic usage
 * const model = new LLMModel('gpt-3.5-turbo');
 * const response = await model.generateResponse('systemPrompt', [
 *   { role: 'user', content: 'Hello, how are you?' }
 * ]);
 * 
 * @example
 * // With custom configuration
 * const config = new ConfigManager();
 * const model = new LLMModel('claude-3-sonnet', config);
 * 
 * @example
 * // Health check
 * const healthResult = await model.performHealthCheck();
 * console.log('Model healthy:', healthResult.healthy);
 * 
 * @example
 * // Get performance statistics
 * const stats = model.getStats();
 * console.log('Success rate:', stats.requests.successRate);
 */

const { ChatOpenAI } = require('@langchain/openai');
const { prompts } = require('../utils/prompts');
const { ConfigManager } = require('../config');
const { createRetryUtility } = require('../utils/retry');
const { ErrorFactory, APIError, AuthenticationError, RateLimitError, TimeoutError } = require('../utils/errors');
const { defaultLogger } = require('../utils/logger');

class LLMModel {
  /**
   * Constructor
   * @param {string} modelName - Name of the model to use
   * @param {ConfigManager} [configManager] - Optional ConfigManager instance
   */
  constructor(modelName, configManager = null) {
    if (!modelName) {
      throw new Error('modelName is required for LLMModel');
    }
    
    this.modelName = modelName;
    this.config = configManager || new ConfigManager();
    
    // Get model-specific configuration based on model type
    const modelType = this.getModelType(modelName);
    const modelConfig = this.getModelConfigByType(modelType);
    
    this.isAssistantModel = modelName === this.config.get('models.assistant.name');

    // Initialize retry utility with model-specific configuration
    this.retryUtility = createRetryUtility({
      maxAttempts: this.config.get('api.maxRetries', 3),
      baseDelay: this.config.get('api.baseRetryDelay', 1000),
      maxDelay: this.config.get('api.maxRetryDelay', 30000),
      timeoutMs: this.config.get('api.requestTimeout', 60000),
      logger: defaultLogger
    });

    // Statistics tracking
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      totalTokensUsed: 0,
      averageResponseTime: 0,
      retryCount: 0
    };

    if (this.config.isVerboseLogging()) {
      defaultLogger.info(
        `Initializing ${modelType} model with temperature=${modelConfig.temperature}, maxTokens=${modelConfig.maxTokens}`,
        { modelName: this.modelName, modelType }
      );
    }

    // Get API configuration
    const apiConfig = this.config.getApiConfig();

    // Common configuration
    const clientConfig = {
      temperature: modelConfig.temperature,
      maxTokens: modelConfig.maxTokens,
      model: this.modelName,
      timeout: this.config.get('api.requestTimeout', 60000)
    };

    if (apiConfig.provider === 'openai') {
      // Configure for OpenAI
      clientConfig.apiKey = apiConfig.apiKey;
      defaultLogger.info(`Using OpenAI API with model: ${this.modelName}`);
    } else {
      // Configure for OpenRouter (default)
      clientConfig.configuration = {
        model: this.modelName,
        apiKey: apiConfig.apiKey,
        modelName: this.modelName,
        baseURL: apiConfig.baseURL,
        temperature: modelConfig.temperature,
        maxTokens: modelConfig.maxTokens,
        timeout: this.config.get('api.requestTimeout', 60000),
        headers: {
          'HTTP-Referer': 'http://localhost:3000', // Required for OpenRouter
          'X-Title': 'LLM Memory Test'
        }
      };
      defaultLogger.info(`Using OpenRouter API with model: ${this.modelName}`);
    }

    this.client = new ChatOpenAI(clientConfig);
    this.performanceTracker = null; // Will be set by components that use this model
  }

  /**
   * Set performance tracker for integrated monitoring
   * 
   * @param {PerformanceTracker} tracker - Performance tracker instance
   */
  setPerformanceTracker(tracker) {
    this.performanceTracker = tracker;
  }

  /**
   * Generate a response from the LLM with retry logic and robust error handling
   * @param {string|Array} promptKeyOrMessages - Either a key for the prompt in the prompts module or an array of messages
   * @param {Array} [additionalMessages] - Optional array of additional message objects with role and content
   * @returns {Promise<string>} Generated response
   */
  async generateResponse(promptKeyOrMessages, additionalMessages = []) {
    const startTime = Date.now();
    this.stats.totalRequests++;

    // Format messages first (outside retry loop to avoid repeated work)
    const formattedMessages = this._formatMessages(promptKeyOrMessages, additionalMessages);
    
    // Create operation ID for circuit breaker tracking
    const operationId = `llm-${this.modelName}-${this.getModelType(this.modelName)}`;

    try {
      // Execute API call with retry logic
      const result = await this.retryUtility.executeWithRetry(
        () => this._executeApiCall(formattedMessages),
        {
          operationId,
          logger: defaultLogger
        }
      );

      // Update statistics
      this.stats.successfulRequests++;
      this._updateResponseTimeStats(startTime);

      // Log successful request
      if (this.config.isVerboseLogging()) {
        this._logVerboseRequest(formattedMessages, result.content, result.usage);
      }

      return result.content;

    } catch (error) {
      // Update failure statistics
      this.stats.failedRequests++;
      
      // Log comprehensive error information
      this._logApiError(error, formattedMessages, startTime);

      // Check if we should return a mock response for testing
      if (this._shouldReturnMockResponse(error)) {
        const mockResponse = this._generateMockResponse();
        defaultLogger.warn('Returning mock response due to API failure', {
          modelName: this.modelName,
          error: error.message,
          mockResponse: mockResponse.substring(0, 100) + '...'
        });
        return mockResponse;
      }

      // Re-throw the structured error
      throw error;
    }
  }

  /**
   * Format messages for API call
   * @private
   * @param {string|Array} promptKeyOrMessages - Prompt key or messages array
   * @param {Array} additionalMessages - Additional messages
   * @returns {Array} Formatted messages array
   */
  _formatMessages(promptKeyOrMessages, additionalMessages) {
    let formattedMessages = [];

    // Check if the first argument is a string (prompt key) or an array (messages)
    if (typeof promptKeyOrMessages === 'string') {
      // Get the system prompt from the prompts module
      const systemPrompt = prompts[promptKeyOrMessages] || promptKeyOrMessages;

      formattedMessages = [
        { role: 'system', content: systemPrompt },
        ...additionalMessages.map(msg => ({
          role: msg.role,
          content: msg.content
        }))
      ];
    } else if (Array.isArray(promptKeyOrMessages)) {
      // Use the provided messages directly
      formattedMessages = promptKeyOrMessages.map(msg => ({
        role: msg.role,
        content: msg.content
      }));
    } else {
      throw ErrorFactory.createError(
        new Error('Invalid argument: promptKeyOrMessages must be a string or an array'),
        { 
          modelName: this.modelName,
          argumentType: typeof promptKeyOrMessages,
          additionalMessagesCount: additionalMessages.length
        }
      );
    }

    // Validate message format
    this._validateMessages(formattedMessages);
    
    return formattedMessages;
  }

  /**
   * Execute the actual API call
   * @private
   * @param {Array} formattedMessages - Formatted messages for the API
   * @returns {Promise<Object>} API response with content and usage info
   */
  async _executeApiCall(formattedMessages) {
    const requestStartTime = Date.now();
    
    try {
      // Add request context for logging
      const requestContext = {
        modelName: this.modelName,
        messageCount: formattedMessages.length,
        requestId: this._generateRequestId(),
        timestamp: new Date().toISOString()
      };

      defaultLogger.debug('Executing API call', requestContext);

      // Make the API call with timeout handling
      const response = await this._callWithTimeout(formattedMessages);
      
      // Validate response
      this._validateApiResponse(response);

      // Extract usage information if available
      const usage = this._extractUsageInfo(response);
      
      // Update token usage statistics
      if (usage.totalTokens) {
        this.stats.totalTokensUsed += usage.totalTokens;
      }

      const responseTime = Date.now() - requestStartTime;
      
      // Record API call in performance tracker if available
      if (this.performanceTracker) {
        this.performanceTracker.recordApiCall(
          this.modelName,
          responseTime,
          usage.totalTokens || 0,
          true, // success
          {
            promptTokens: usage.promptTokens || 0,
            completionTokens: usage.completionTokens || 0,
            operationType: 'chat',
            messageCount: formattedMessages.length
          }
        );
      }
      
      defaultLogger.debug('API call successful', {
        ...requestContext,
        responseTime,
        contentLength: response.content?.length || 0,
        tokensUsed: usage.totalTokens || 0
      });

      return {
        content: response.content,
        usage,
        responseTime,
        requestContext
      };

    } catch (error) {
      const responseTime = Date.now() - requestStartTime;
      
      // Create structured error with context
      const structuredError = ErrorFactory.createError(error, {
        modelName: this.modelName,
        messageCount: formattedMessages.length,
        responseTime,
        requestTimestamp: new Date(requestStartTime).toISOString()
      });

      // Record failed API call in performance tracker if available
      if (this.performanceTracker) {
        this.performanceTracker.recordApiCall(
          this.modelName,
          responseTime,
          0, // no tokens on failure
          false, // failure
          {
            error: structuredError.message,
            errorType: structuredError.constructor.name,
            operationType: 'chat',
            messageCount: formattedMessages.length
          }
        );
      }

      // Log the error with context
      defaultLogger.error('API call failed', {
        modelName: this.modelName,
        error: structuredError.message,
        responseTime,
        errorType: structuredError.constructor.name
      });

      throw structuredError;
    }
  }

  /**
   * Call API with timeout handling
   * @private
   * @param {Array} formattedMessages - Messages to send
   * @returns {Promise<Object>} API response
   */
  async _callWithTimeout(formattedMessages) {
    const timeoutMs = this.config.get('api.requestTimeout', 60000);
    
    return new Promise(async (resolve, reject) => {
      // Set up timeout
      const timeoutId = setTimeout(() => {
        reject(new TimeoutError(`Request timed out after ${timeoutMs}ms`, {
          modelName: this.modelName,
          timeout: timeoutMs
        }));
      }, timeoutMs);

      try {
        const response = await this.client.invoke(formattedMessages);
        clearTimeout(timeoutId);
        resolve(response);
      } catch (error) {
        clearTimeout(timeoutId);
        reject(error);
      }
    });
  }

  /**
   * Validate messages format
   * @private
   * @param {Array} messages - Messages to validate
   */
  _validateMessages(messages) {
    if (!Array.isArray(messages) || messages.length === 0) {
      throw ErrorFactory.createError(
        new Error('Messages must be a non-empty array'),
        { modelName: this.modelName, messagesType: typeof messages }
      );
    }

    for (let i = 0; i < messages.length; i++) {
      const message = messages[i];
      if (!message.role || !message.content) {
        throw ErrorFactory.createError(
          new Error(`Message at index ${i} must have 'role' and 'content' properties`),
          { 
            modelName: this.modelName, 
            messageIndex: i, 
            message: JSON.stringify(message) 
          }
        );
      }

      if (!['system', 'user', 'assistant'].includes(message.role)) {
        throw ErrorFactory.createError(
          new Error(`Invalid message role: ${message.role}`),
          { 
            modelName: this.modelName, 
            messageIndex: i, 
            role: message.role 
          }
        );
      }
    }
  }

  /**
   * Validate API response
   * @private
   * @param {Object} response - API response to validate
   */
  _validateApiResponse(response) {
    if (!response) {
      throw ErrorFactory.createError(
        new Error('API returned null or undefined response'),
        { modelName: this.modelName }
      );
    }

    if (!response.content) {
      throw ErrorFactory.createError(
        new Error('API response missing content'),
        { 
          modelName: this.modelName, 
          response: JSON.stringify(response) 
        }
      );
    }

    if (typeof response.content !== 'string') {
      throw ErrorFactory.createError(
        new Error('API response content must be a string'),
        { 
          modelName: this.modelName, 
          contentType: typeof response.content 
        }
      );
    }
  }

  /**
   * Extract usage information from API response
   * @private
   * @param {Object} response - API response
   * @returns {Object} Usage information
   */
  _extractUsageInfo(response) {
    const usage = {
      promptTokens: 0,
      completionTokens: 0,
      totalTokens: 0
    };

    // Try to extract usage info from different possible locations
    if (response.usage) {
      usage.promptTokens = response.usage.prompt_tokens || response.usage.promptTokens || 0;
      usage.completionTokens = response.usage.completion_tokens || response.usage.completionTokens || 0;
      usage.totalTokens = response.usage.total_tokens || response.usage.totalTokens || 0;
    } else if (response.response_metadata?.tokenUsage) {
      const tokenUsage = response.response_metadata.tokenUsage;
      usage.promptTokens = tokenUsage.promptTokens || 0;
      usage.completionTokens = tokenUsage.completionTokens || 0;
      usage.totalTokens = tokenUsage.totalTokens || 0;
    }

    return usage;
  }

  /**
   * Update response time statistics
   * @private
   * @param {number} startTime - Request start time
   */
  _updateResponseTimeStats(startTime) {
    const responseTime = Date.now() - startTime;
    
    // Update running average
    if (this.stats.successfulRequests === 1) {
      this.stats.averageResponseTime = responseTime;
    } else {
      this.stats.averageResponseTime = 
        (this.stats.averageResponseTime * (this.stats.successfulRequests - 1) + responseTime) / 
        this.stats.successfulRequests;
    }
  }

  /**
   * Log verbose request information
   * @private
   * @param {Array} messages - Request messages
   * @param {string} content - Response content
   * @param {Object} usage - Token usage info
   */
  _logVerboseRequest(messages, content, usage) {
    const context = {
      modelName: this.modelName,
      messageCount: messages.length,
      responseLength: content.length,
      usage: {
        totalTokens: usage.totalTokens,
        promptTokens: usage.promptTokens,
        completionTokens: usage.completionTokens
      }
    };

    // Only include full messages in debug mode to avoid performance impact
    if (defaultLogger.shouldLog('DEBUG')) {
      context.messages = messages;
      context.response = content.length > 500 ? content.substring(0, 500) + '...' : content;
    }

    defaultLogger.debug('LLM request completed', context);
    
    if (this.isAssistantModel && content.length > 1000) {
      defaultLogger.info('Large response generated', {
        modelName: this.modelName,
        responseLength: content.length,
        isAssistantModel: true
      });
    }
  }

  /**
   * Log comprehensive API error information
   * @private
   * @param {Error} error - The error that occurred
   * @param {Array} messages - Request messages
   * @param {number} startTime - Request start time
   */
  _logApiError(error, messages, startTime) {
    const responseTime = Date.now() - startTime;
    
    defaultLogger.error('LLM API call failed', {
      modelName: this.modelName,
      error: error.message,
      errorType: error.constructor.name,
      responseTime,
      messageCount: messages.length,
      isRetryable: error.isRetryable ? error.isRetryable() : false,
      context: error.context || {}
    });

    // Log additional error details in verbose mode
    if (this.config.isVerboseLogging()) {
      defaultLogger.error('Detailed LLM error information', {
        modelName: this.modelName,
        errorMessage: error.getFormattedMessage ? error.getFormattedMessage() : error.message,
        failedMessages: messages,
        responseTime,
        errorContext: error.context || {},
        stackTrace: error.stack
      });
    }
  }

  /**
   * Check if we should return a mock response for testing
   * @private
   * @param {Error} error - The error that occurred
   * @returns {boolean} True if should return mock response
   */
  _shouldReturnMockResponse(error) {
    // Return mock response in development/test environments for certain errors
    const isDevelopment = this.config.isDevelopment() || this.config.isTest();
    const isApiError = error instanceof APIError || error instanceof AuthenticationError;
    
    return isDevelopment && isApiError;
  }

  /**
   * Generate a mock response for testing
   * @private
   * @returns {string} Mock response
   */
  _generateMockResponse() {
    const modelType = this.getModelType(this.modelName);
    const responses = {
      USER: "This is a mock user response for testing purposes.",
      ASSISTANT: "This is a mock assistant response. In a real scenario, this would be a helpful response from the AI assistant.",
      EVALUATOR: "CORRECT: This is a mock evaluation response.",
      SUMMARY: "This is a mock summary of the conversation for testing.",
      KNOWLEDGE_EXTRACTION: "Mock extracted knowledge: The user discussed testing scenarios."
    };
    
    return responses[modelType] || `Mock response from ${this.modelName} model for testing.`;
  }

  /**
   * Generate a unique request ID for tracking
   * @private
   * @returns {string} Unique request ID
   */
  _generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get the model type based on the model name
   * @param {string} modelName - The model name
   * @returns {string} The model type (USER, ASSISTANT, EVALUATOR, SUMMARY, KNOWLEDGE_EXTRACTION)
   */
  getModelType(modelName) {
    if (modelName === this.config.get('models.user.name')) return 'USER';
    if (modelName === this.config.get('models.assistant.name')) return 'ASSISTANT';
    if (modelName === this.config.get('models.evaluator.name')) return 'EVALUATOR';
    if (modelName === this.config.get('models.summary.name')) return 'SUMMARY';
    if (modelName === this.config.get('models.knowledgeExtraction.name'))
      return 'KNOWLEDGE_EXTRACTION';
    return 'UNKNOWN';
  }

  /**
   * Get model configuration by type
   * @param {string} modelType - The model type
   * @returns {Object} Model configuration object
   */
  getModelConfigByType(modelType) {
    switch (modelType) {
      case 'USER':
        return this.config.getModelConfig('user');
      case 'ASSISTANT':
        return this.config.getModelConfig('assistant');
      case 'EVALUATOR':
        return this.config.getModelConfig('evaluator');
      case 'SUMMARY':
        return this.config.getModelConfig('summary');
      case 'KNOWLEDGE_EXTRACTION':
        return this.config.getModelConfig('knowledgeExtraction');
      default:
        return {
          name: this.modelName,
          temperature: 0.7,
          maxTokens: 150
        };
    }
  }

  /**
   * Get the temperature for a specific model type
   * @param {string} modelType - The model type
   * @returns {number} The temperature value
   */
  getModelTemperature(modelType) {
    const modelConfig = this.getModelConfigByType(modelType);
    return modelConfig.temperature;
  }

  /**
   * Get the max tokens for a specific model type
   * @param {string} modelType - The model type
   * @returns {number} The max tokens value
   */
  getModelMaxTokens(modelType) {
    const modelConfig = this.getModelConfigByType(modelType);
    return modelConfig.maxTokens;
  }

  /**
   * Get comprehensive statistics for this model instance
   * @returns {Object} Statistics object with performance metrics
   */
  getStats() {
    const circuitBreakerStats = this.retryUtility.getStats();
    const operationId = `llm-${this.modelName}-${this.getModelType(this.modelName)}`;
    
    return {
      modelName: this.modelName,
      modelType: this.getModelType(this.modelName),
      requests: {
        total: this.stats.totalRequests,
        successful: this.stats.successfulRequests,
        failed: this.stats.failedRequests,
        successRate: this.stats.totalRequests > 0 
          ? (this.stats.successfulRequests / this.stats.totalRequests * 100).toFixed(2) + '%'
          : '0%'
      },
      performance: {
        averageResponseTime: Math.round(this.stats.averageResponseTime),
        totalTokensUsed: this.stats.totalTokensUsed,
        averageTokensPerRequest: this.stats.successfulRequests > 0 
          ? Math.round(this.stats.totalTokensUsed / this.stats.successfulRequests)
          : 0
      },
      circuitBreaker: circuitBreakerStats[operationId] || {
        state: 'CLOSED',
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        successRate: 0
      }
    };
  }

  /**
   * Reset statistics for this model instance
   */
  resetStats() {
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      totalTokensUsed: 0,
      averageResponseTime: 0,
      retryCount: 0
    };
    
    // Reset circuit breaker for this model
    this.retryUtility.resetCircuitBreakers();
    
    defaultLogger.info('Statistics reset', { modelName: this.modelName });
  }

  /**
   * Check if the model is currently healthy (circuit breaker closed)
   * @returns {boolean} True if model is healthy
   */
  isHealthy() {
    const operationId = `llm-${this.modelName}-${this.getModelType(this.modelName)}`;
    const circuitBreaker = this.retryUtility.getCircuitBreaker(operationId);
    return circuitBreaker.canExecute();
  }

  /**
   * Get rate limiting information and backoff recommendations
   * @returns {Object} Rate limiting status and recommendations
   */
  getRateLimitStatus() {
    const stats = this.getStats();
    const recentFailureRate = stats.requests.total > 0 
      ? (stats.requests.failed / stats.requests.total) 
      : 0;
    
    // Estimate rate limiting based on failure patterns
    const isLikelyRateLimited = recentFailureRate > 0.3 && stats.circuitBreaker.state !== 'CLOSED';
    
    return {
      isLikelyRateLimited,
      recommendedBackoff: isLikelyRateLimited ? this._calculateRecommendedBackoff() : 0,
      circuitBreakerState: stats.circuitBreaker.state,
      failureRate: (recentFailureRate * 100).toFixed(2) + '%',
      suggestions: this._generateRateLimitSuggestions(isLikelyRateLimited, stats)
    };
  }

  /**
   * Calculate recommended backoff time based on current conditions
   * @private
   * @returns {number} Recommended backoff time in milliseconds
   */
  _calculateRecommendedBackoff() {
    const stats = this.getStats();
    const baseBackoff = 5000; // 5 seconds base
    
    // Increase backoff based on failure rate
    const failureMultiplier = Math.max(1, stats.requests.failed / 10);
    
    // Increase backoff if circuit breaker is open
    const circuitMultiplier = stats.circuitBreaker.state === 'OPEN' ? 3 : 1;
    
    return Math.min(baseBackoff * failureMultiplier * circuitMultiplier, 300000); // Max 5 minutes
  }

  /**
   * Generate suggestions for handling rate limiting
   * @private
   * @param {boolean} isRateLimited - Whether rate limiting is detected
   * @param {Object} stats - Current statistics
   * @returns {Array} Array of suggestion strings
   */
  _generateRateLimitSuggestions(isRateLimited, stats) {
    const suggestions = [];
    
    if (isRateLimited) {
      suggestions.push('Consider reducing request frequency');
      suggestions.push('Implement exponential backoff between requests');
      
      if (stats.performance.averageTokensPerRequest > 1000) {
        suggestions.push('Reduce token usage per request to stay within limits');
      }
    }
    
    if (stats.circuitBreaker.state === 'OPEN') {
      suggestions.push('Circuit breaker is open - wait for automatic recovery');
      suggestions.push('Check API key validity and account status');
    }
    
    if (stats.requests.successRate < 50) {
      suggestions.push('Low success rate detected - verify model configuration');
      suggestions.push('Check network connectivity and API endpoint status');
    }
    
    return suggestions;
  }

  /**
   * Perform a health check by making a simple API call
   * @returns {Promise<Object>} Health check result
   */
  async performHealthCheck() {
    const healthCheckStart = Date.now();
    
    try {
      // Simple health check message
      const testMessages = [
        { role: 'system', content: 'You are a helpful assistant.' },
        { role: 'user', content: 'Say "OK" if you can respond.' }
      ];
      
      const response = await this.generateResponse(testMessages);
      const responseTime = Date.now() - healthCheckStart;
      
      return {
        healthy: true,
        responseTime,
        response: response.substring(0, 100), // First 100 chars
        timestamp: new Date().toISOString(),
        modelName: this.modelName
      };
      
    } catch (error) {
      const responseTime = Date.now() - healthCheckStart;
      
      return {
        healthy: false,
        responseTime,
        error: error.message,
        errorType: error.constructor.name,
        timestamp: new Date().toISOString(),
        modelName: this.modelName
      };
    }
  }

  /**
   * Get detailed error context for the last failed request
   * @returns {Object|null} Error context or null if no recent errors
   */
  getLastErrorContext() {
    // This would typically be stored in instance state
    // For now, return circuit breaker stats as error context
    const stats = this.getStats();
    
    if (stats.requests.failed === 0) {
      return null;
    }
    
    return {
      modelName: this.modelName,
      totalFailures: stats.requests.failed,
      circuitBreakerState: stats.circuitBreaker.state,
      lastKnownIssues: this._identifyCommonIssues(stats),
      troubleshootingSteps: this._generateTroubleshootingSteps(stats)
    };
  }

  /**
   * Identify common issues based on statistics
   * @private
   * @param {Object} stats - Current statistics
   * @returns {Array} Array of identified issues
   */
  _identifyCommonIssues(stats) {
    const issues = [];
    
    if (stats.circuitBreaker.state === 'OPEN') {
      issues.push('Circuit breaker is open due to repeated failures');
    }
    
    if (stats.performance.averageResponseTime > 30000) {
      issues.push('High response times detected (>30s)');
    }
    
    if (stats.requests.successRate < 50) {
      issues.push('Low success rate (<50%)');
    }
    
    return issues;
  }

  /**
   * Generate troubleshooting steps based on current state
   * @private
   * @param {Object} stats - Current statistics
   * @returns {Array} Array of troubleshooting steps
   */
  _generateTroubleshootingSteps(stats) {
    const steps = [];
    
    steps.push('1. Check API key validity and permissions');
    steps.push('2. Verify network connectivity');
    steps.push('3. Check API service status');
    
    if (stats.circuitBreaker.state === 'OPEN') {
      steps.push('4. Wait for circuit breaker to reset automatically');
      steps.push('5. Consider resetting statistics if issues persist');
    }
    
    if (stats.performance.averageResponseTime > 10000) {
      steps.push('4. Reduce request complexity or token limits');
      steps.push('5. Check for network latency issues');
    }
    
    return steps;
  }
}

module.exports = LLMModel;
