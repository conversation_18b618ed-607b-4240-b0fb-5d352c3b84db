/**
 * Memory Interface - Abstract Base Class for Memory Implementations
 *
 * This abstract class defines the contract that all memory implementations must follow
 * in the LLM Memory Test Application. It provides a standardized interface for
 * managing conversational memory across different storage and processing strategies.
 *
 * @abstract
 * @class MemoryInterface
 * 
 * @example
 * // Memory implementations should extend this interface
 * class CustomMemory extends MemoryInterface {
 *   constructor(sessionId, config) {
 *     super(sessionId);
 *     this.config = config;
 *   }
 * 
 *   async addMessage(message) {
 *     // Implementation specific logic
 *   }
 * 
 *   async getMemoryContext(currentMessage) {
 *     // Return formatted context string
 *     return "Previous conversation context...";
 *   }
 * 
 *   async clearMemory() {
 *     // Clear stored messages
 *   }
 * }
 * 
 * @example
 * // Usage with factory
 * const memory = MemoryFactory.createMemory('simple', 'session-123');
 * await memory.addMessage({ role: 'user', content: 'Hello' });
 * const context = await memory.getMemoryContext();
 */
class MemoryInterface {
  /**
   * Creates a new memory interface instance
   * 
   * @param {string} sessionId - Unique session identifier for tracking conversations
   *                            Should be consistent across the entire conversation session
   * @throws {Error} Throws error if attempting to instantiate abstract class directly
   * 
   * @example
   * // This will throw an error
   * const memory = new MemoryInterface('session-123'); // Error!
   * 
   * @example
   * // Correct usage in subclass
   * class MyMemory extends MemoryInterface {
   *   constructor(sessionId) {
   *     super(sessionId); // Call parent constructor
   *   }
   * }
   */
  constructor(sessionId) {
    if (this.constructor === MemoryInterface) {
      throw new Error(
        "Abstract class 'MemoryInterface' cannot be instantiated directly"
      );
    }
    
    if (!sessionId || typeof sessionId !== 'string') {
      throw new Error('sessionId must be a non-empty string');
    }
    
    this.sessionId = sessionId;
  }

  /**
   * Add a message to the memory system
   * 
   * This method stores a new message in the memory implementation. The message
   * should contain at minimum a role and content. Implementations may perform
   * additional processing such as summarization or knowledge extraction.
   * 
   * @abstract
   * @param {Object} message - Message object to store
   * @param {string} message.role - Message role ('user', 'assistant', 'system')
   * @param {string} message.content - Message content/text
   * @param {string} [message.timestamp] - Optional timestamp (ISO string)
   * @param {Object} [message.metadata] - Optional additional metadata
   * @returns {Promise<void>} Promise that resolves when message is stored
   * @throws {Error} Throws error if method is not implemented by subclass
   * @throws {ValidationError} Throws if message format is invalid
   * 
   * @example
   * await memory.addMessage({
   *   role: 'user',
   *   content: 'What is the capital of France?',
   *   timestamp: new Date().toISOString()
   * });
   * 
   * @example
   * await memory.addMessage({
   *   role: 'assistant',
   *   content: 'The capital of France is Paris.',
   *   metadata: { confidence: 0.95, source: 'knowledge_base' }
   * });
   */
  async addMessage(message) {
    throw new Error("Method 'addMessage' must be implemented");
  }

  /**
   * Retrieve formatted memory context for the current conversation
   * 
   * This method returns a formatted string representation of the conversation
   * history that can be used as context for LLM prompts. The format and content
   * depend on the specific memory implementation strategy.
   * 
   * @abstract
   * @param {Object} [currentMessage] - Optional current message for context-aware formatting
   * @param {string} [currentMessage.role] - Current message role
   * @param {string} [currentMessage.content] - Current message content
   * @param {Object} [options] - Optional formatting options
   * @param {number} [options.maxLength] - Maximum context length in characters
   * @param {boolean} [options.includeMetadata] - Whether to include message metadata
   * @returns {Promise<string>} Promise that resolves to formatted memory context
   * @throws {Error} Throws error if method is not implemented by subclass
   * @throws {MemoryError} Throws if memory retrieval fails
   * 
   * @example
   * const context = await memory.getMemoryContext();
   * console.log(context);
   * // Output: "Previous messages:\nUser: Hello\nAssistant: Hi there!"
   * 
   * @example
   * const context = await memory.getMemoryContext(
   *   { role: 'user', content: 'Tell me more' },
   *   { maxLength: 500, includeMetadata: true }
   * );
   */
  async getMemoryContext(currentMessage) {
    throw new Error("Method 'getMemoryContext' must be implemented");
  }

  /**
   * Clear all stored memory for the current session
   * 
   * This method removes all stored messages and resets the memory state
   * for the current session. This operation is typically irreversible.
   * 
   * @abstract
   * @returns {Promise<void>} Promise that resolves when memory is cleared
   * @throws {Error} Throws error if method is not implemented by subclass
   * @throws {MemoryError} Throws if memory clearing fails
   * 
   * @example
   * await memory.clearMemory();
   * console.log('Memory cleared for session:', memory.sessionId);
   * 
   * @example
   * // Clear memory and verify
   * await memory.clearMemory();
   * const context = await memory.getMemoryContext();
   * console.log(context); // Should be empty or default state
   */
  async clearMemory() {
    throw new Error("Method 'clearMemory' must be implemented");
  }

  /**
   * Get the current session identifier
   * 
   * @returns {string} The session ID for this memory instance
   * 
   * @example
   * const sessionId = memory.getSessionId();
   * console.log('Current session:', sessionId);
   */
  getSessionId() {
    return this.sessionId;
  }

  /**
   * Get memory statistics and metadata
   * 
   * This method provides information about the current state of the memory
   * implementation, such as message count, memory usage, etc. Default
   * implementation returns basic information.
   * 
   * @returns {Promise<Object>} Promise that resolves to memory statistics
   * @returns {string} returns.sessionId - Session identifier
   * @returns {number} returns.messageCount - Number of stored messages (if available)
   * @returns {string} returns.type - Memory implementation type
   * 
   * @example
   * const stats = await memory.getStats();
   * console.log('Memory stats:', stats);
   * // { sessionId: 'session-123', messageCount: 5, type: 'SimpleMemory' }
   */
  async getStats() {
    return {
      sessionId: this.sessionId,
      messageCount: 0, // Subclasses should override
      type: this.constructor.name
    };
  }
}

module.exports = MemoryInterface;
