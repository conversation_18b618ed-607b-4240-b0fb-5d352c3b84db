/**
 * Comprehensive tests for SimpleMemory implementation
 * Tests cover various message scenarios, error handling, and performance tracking
 */

const SimpleMemory = require('../simpleMemory');

// Mock all dependencies
jest.mock('../../config', () => ({
  ConfigManager: jest.fn()
}));

jest.mock('../../utils/logger', () => ({
  defaultLogger: {
    child: jest.fn(() => ({
      info: jest.fn(),
      debug: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
      startTimer: jest.fn(() => 'timer-id'),
      endTimer: jest.fn()
    })),
    info: jest.fn(),
    error: jest.fn()
  }
}));

jest.mock('../../utils/errors', () => ({
  ValidationError: class ValidationError extends Error {
    constructor(message, context) {
      super(message);
      this.name = 'ValidationError';
      this.context = context;
    }
  },
  MemoryError: class MemoryError extends Error {
    constructor(message, context) {
      super(message);
      this.name = 'MemoryError';
      this.context = context;
    }
  },
  ErrorFactory: {
    createError: jest.fn((error, context) => {
      const wrappedError = new Error(error.message);
      wrappedError.context = context;
      return wrappedError;
    })
  }
}));

const { ConfigManager } = require('../../config');
const { ValidationError, MemoryError, ErrorFactory } = require('../../utils/errors');

describe('SimpleMemory', () => {
  let mockConfig;
  let memory;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Setup mock config with all required methods
    mockConfig = {
      get: jest.fn((key, defaultValue) => {
        const config = {
          'memory.contextWindow': 10,
          'memory.maxMessageLength': 1000,
          'memory.enableStrictValidation': true,
          'memory.enablePerformanceLogging': true
        };
        return config[key] !== undefined ? config[key] : defaultValue;
      })
    };

    // Mock ConfigManager constructor
    ConfigManager.mockImplementation(() => mockConfig);
  });

  describe('Constructor', () => {
    test('should create SimpleMemory with valid sessionId', () => {
      memory = new SimpleMemory('session-123');
      
      expect(memory.sessionId).toBe('session-123');
      expect(memory.messages).toEqual([]);
      expect(memory.contextWindow).toBe(10);
      expect(memory.maxMessageLength).toBe(10000);
    });

    test('should create SimpleMemory with custom config', () => {
      const customConfig = {
        get: jest.fn((key, defaultValue) => {
          const config = {
            'memory.contextWindow': 5,
            'memory.maxMessageLength': 500
          };
          return config[key] !== undefined ? config[key] : defaultValue;
        })
      };

      memory = new SimpleMemory('session-456', customConfig);
      
      expect(memory.contextWindow).toBe(5);
      expect(memory.maxMessageLength).toBe(500);
    });

    test('should throw error with invalid sessionId', () => {
      expect(() => {
        new SimpleMemory('');
      }).toThrow('sessionId must be a non-empty string');

      expect(() => {
        new SimpleMemory(null);
      }).toThrow('sessionId must be a non-empty string');

      expect(() => {
        new SimpleMemory(123);
      }).toThrow('sessionId must be a non-empty string');
    });

    test('should throw error with invalid context window config', () => {
      mockConfig.get.mockImplementation((key, defaultValue) => {
        if (key === 'memory.contextWindow') return 0;
        if (key === 'memory.maxMessageLength') return 1000;
        return defaultValue;
      });

      expect(() => {
        new SimpleMemory('session-123');
      }).toThrow('Context window must be a positive integer');
    });

    test('should handle missing optional configuration with defaults', () => {
      mockConfig.get.mockImplementation((key, defaultValue) => {
        const required = {
          'memory.contextWindow': 5
        };
        return required[key] !== undefined ? required[key] : defaultValue;
      });

      memory = new SimpleMemory('session-123');

      expect(memory.contextWindow).toBe(5);
      expect(memory.maxMessageLength).toBe(10000); // default value
      expect(memory.enableStrictValidation).toBe(true); // default value
    });
  });

  describe('addMessage', () => {
    beforeEach(() => {
      memory = new SimpleMemory('session-123');
    });

    test('should add valid message with all fields', async () => {
      const message = {
        role: 'user',
        content: 'Hello world',
        timestamp: new Date().toISOString(),
        metadata: { source: 'test' }
      };

      await memory.addMessage(message);

      expect(memory.messages).toHaveLength(1);
      expect(memory.messages[0]).toEqual(expect.objectContaining({
        role: 'user',
        content: 'Hello world',
        timestamp: expect.any(String),
        metadata: { source: 'test' }
      }));
    });

    test('should add message without optional fields', async () => {
      const message = {
        role: 'assistant',
        content: 'Hi there!'
      };

      await memory.addMessage(message);

      expect(memory.messages).toHaveLength(1);
      expect(memory.messages[0]).toEqual(expect.objectContaining({
        role: 'assistant',
        content: 'Hi there!',
        timestamp: expect.any(String)
      }));
    });

    test('should sanitize message role and content', async () => {
      // First disable strict validation to allow uppercase role
      mockConfig.get.mockImplementation((key, defaultValue) => {
        if (key === 'memory.enableStrictValidation') return false;
        if (key === 'memory.contextWindow') return 10;
        if (key === 'memory.maxMessageLength') return 10000;
        return defaultValue;
      });

      memory = new SimpleMemory('session-123');

      const message = {
        role: '  USER  ',
        content: '  Test content  '
      };

      await memory.addMessage(message);

      expect(memory.messages[0].role).toBe('user');
      expect(memory.messages[0].content).toBe('Test content');
    });

    test('should maintain context window size by removing old messages', async () => {
      // Add messages beyond context window (10)
      for (let i = 0; i < 15; i++) {
        await memory.addMessage({
          role: 'user',
          content: `Message ${i}`
        });
      }

      expect(memory.messages).toHaveLength(10); // contextWindow = 10
      expect(memory.messages[0].content).toBe('Message 5'); // Oldest kept message
      expect(memory.messages[9].content).toBe('Message 14'); // Newest message
    });

    test('should throw ValidationError for invalid message structure', async () => {
      await expect(memory.addMessage(null)).rejects.toThrow(ValidationError);
      await expect(memory.addMessage({})).rejects.toThrow(ValidationError);
      await expect(memory.addMessage({ role: 'user' })).rejects.toThrow(ValidationError);
      await expect(memory.addMessage({ content: 'test' })).rejects.toThrow(ValidationError);
    });

    test('should throw ValidationError for invalid role in strict mode', async () => {
      const message = {
        role: 'invalid',
        content: 'Test content'
      };

      await expect(memory.addMessage(message)).rejects.toThrow(ValidationError);
    });

    test('should allow invalid role when strict validation is disabled', async () => {
      mockConfig.get.mockImplementation((key, defaultValue) => {
        if (key === 'memory.enableStrictValidation') return false;
        if (key === 'memory.contextWindow') return 10;
        if (key === 'memory.maxMessageLength') return 1000;
        return defaultValue;
      });

      memory = new SimpleMemory('session-123');

      const message = {
        role: 'custom',
        content: 'Test content'
      };

      await memory.addMessage(message);
      expect(memory.messages[0].role).toBe('custom');
    });

    test('should throw ValidationError for message exceeding max length', async () => {
      const longContent = 'a'.repeat(20000); // Exceeds maxMessageLength of 10000
      const message = {
        role: 'user',
        content: longContent
      };

      await expect(memory.addMessage(message)).rejects.toThrow(ValidationError);
    });

    test('should throw ValidationError for invalid timestamp format', async () => {
      const message = {
        role: 'user',
        content: 'Test content',
        timestamp: 'invalid-timestamp'
      };

      await expect(memory.addMessage(message)).rejects.toThrow(ValidationError);
    });

    test('should handle metadata correctly', async () => {
      const message = {
        role: 'user',
        content: 'Test message',
        metadata: { 
          confidence: 0.95, 
          source: 'test',
          nested: { key: 'value' }
        }
      };

      await memory.addMessage(message);

      expect(memory.messages[0].metadata).toEqual({
        confidence: 0.95,
        source: 'test',
        nested: { key: 'value' }
      });
    });
  });

  describe('getMemoryContext', () => {
    beforeEach(() => {
      memory = new SimpleMemory('session-123');
    });

    test('should return empty string for no messages', async () => {
      const context = await memory.getMemoryContext();
      expect(context).toBe('');
    });

    test('should return formatted messages in text format', async () => {
      await memory.addMessage({ role: 'user', content: 'Hello' });
      await memory.addMessage({ role: 'assistant', content: 'Hi there!' });

      const context = await memory.getMemoryContext();
      
      expect(context).toContain('user: Hello');
      expect(context).toContain('assistant: Hi there!');
      expect(context.split('\n')).toHaveLength(2);
    });

    test('should include metadata when requested', async () => {
      await memory.addMessage({
        role: 'user',
        content: 'Test',
        metadata: { confidence: 0.9 }
      });

      const context = await memory.getMemoryContext(null, { includeMetadata: true });
      
      expect(context).toContain('user: Test');
      expect(context).toContain('{"confidence":0.9}');
    });

    test('should return JSON format when requested', async () => {
      await memory.addMessage({ role: 'user', content: 'Hello' });
      await memory.addMessage({ role: 'assistant', content: 'Hi!' });

      const context = await memory.getMemoryContext(null, { format: 'json' });
      
      const parsed = JSON.parse(context);
      expect(Array.isArray(parsed)).toBe(true);
      expect(parsed).toHaveLength(2);
      expect(parsed[0]).toEqual(expect.objectContaining({
        role: 'user',
        content: 'Hello'
      }));
    });

    test('should truncate context when maxLength specified', async () => {
      await memory.addMessage({ 
        role: 'user', 
        content: 'This is a very long message that should be truncated when maxLength is specified' 
      });

      const context = await memory.getMemoryContext(null, { maxLength: 30 });
      
      expect(context.length).toBeLessThanOrEqual(30);
      expect(context).toContain('... [truncated]');
    });

    test('should validate context options', async () => {
      await expect(
        memory.getMemoryContext(null, { maxLength: -1 })
      ).rejects.toThrow(ValidationError);

      await expect(
        memory.getMemoryContext(null, { includeMetadata: 'invalid' })
      ).rejects.toThrow(ValidationError);

      await expect(
        memory.getMemoryContext(null, { format: 'invalid' })
      ).rejects.toThrow(ValidationError);
    });

    test('should handle complex message scenarios', async () => {
      // Add various message types
      await memory.addMessage({ role: 'system', content: 'System message' });
      await memory.addMessage({ role: 'user', content: 'User question' });
      await memory.addMessage({ role: 'assistant', content: 'Assistant response' });

      const context = await memory.getMemoryContext();
      
      expect(context).toContain('system: System message');
      expect(context).toContain('user: User question');
      expect(context).toContain('assistant: Assistant response');
    });
  });

  describe('clearMemory', () => {
    beforeEach(() => {
      memory = new SimpleMemory('session-123');
    });

    test('should clear all messages', async () => {
      // Add some messages
      await memory.addMessage({ role: 'user', content: 'Message 1' });
      await memory.addMessage({ role: 'assistant', content: 'Message 2' });

      expect(memory.messages).toHaveLength(2);

      await memory.clearMemory();

      expect(memory.messages).toHaveLength(0);
    });

    test('should reset performance statistics', async () => {
      // Add messages and get context to generate stats
      await memory.addMessage({ role: 'user', content: 'Test' });
      await memory.getMemoryContext();

      await memory.clearMemory();

      const stats = await memory.getStats();
      expect(stats.performance.addMessageCount).toBe(0);
      expect(stats.performance.getContextCount).toBe(0);
    });

    test('should handle clearing empty memory', async () => {
      await memory.clearMemory();
      expect(memory.messages).toHaveLength(0);
    });
  });

  describe('getStats', () => {
    beforeEach(() => {
      memory = new SimpleMemory('session-123');
    });

    test('should return comprehensive statistics', async () => {
      await memory.addMessage({ role: 'user', content: 'Test message' });

      const stats = await memory.getStats();

      expect(stats).toEqual(expect.objectContaining({
        sessionId: 'session-123',
        messageCount: 1,
        type: 'SimpleMemory',
        configuration: expect.objectContaining({
          contextWindow: 10,
          maxMessageLength: 10000,
          enableStrictValidation: true,
          enablePerformanceLogging: true
        }),
        performance: expect.any(Object),
        lastAccessTime: expect.any(String),
        memoryUsage: expect.objectContaining({
          estimatedSizeBytes: expect.any(Number),
          messagesInMemory: 1
        })
      }));
    });

    test('should track performance metrics accurately', async () => {
      await memory.addMessage({ role: 'user', content: 'Test 1' });
      await memory.addMessage({ role: 'user', content: 'Test 2' });
      await memory.getMemoryContext();
      await memory.getMemoryContext();

      const stats = await memory.getStats();

      expect(stats.performance.addMessageCount).toBe(2);
      expect(stats.performance.getContextCount).toBe(2);
    });

    test('should estimate memory usage', async () => {
      await memory.addMessage({ role: 'user', content: 'Short' });
      await memory.addMessage({ role: 'user', content: 'A much longer message with more content' });

      const stats = await memory.getStats();

      expect(stats.memoryUsage.estimatedSizeBytes).toBeGreaterThan(0);
      expect(stats.memoryUsage.messagesInMemory).toBe(2);
    });
  });

  describe('Error Handling', () => {
    beforeEach(() => {
      memory = new SimpleMemory('session-123');
    });

    test('should wrap and rethrow errors with context', async () => {
      // Mock validation to throw an error
      const originalValidate = memory._validateMessage;
      memory._validateMessage = jest.fn(() => {
        throw new Error('Validation failed');
      });

      try {
        await memory.addMessage({ role: 'user', content: 'test' });
        fail('Expected error to be thrown');
      } catch (error) {
        expect(ErrorFactory.createError).toHaveBeenCalledWith(
          expect.any(Error),
          expect.objectContaining({
            operation: 'addMessage',
            memoryType: 'SimpleMemory',
            sessionId: 'session-123'
          })
        );
      }

      // Restore original method
      memory._validateMessage = originalValidate;
    });

    test('should handle configuration errors gracefully', () => {
      mockConfig.get.mockImplementation(() => {
        throw new Error('Config error');
      });

      expect(() => {
        new SimpleMemory('session-123');
      }).toThrow();
    });
  });

  describe('Edge Cases and Boundary Conditions', () => {
    beforeEach(() => {
      memory = new SimpleMemory('session-123');
    });

    test('should handle exactly context window size messages', async () => {
      // Add exactly contextWindow (10) messages
      for (let i = 0; i < 10; i++) {
        await memory.addMessage({
          role: 'user',
          content: `Message ${i}`
        });
      }

      expect(memory.messages).toHaveLength(10);

      // Add one more to trigger removal
      await memory.addMessage({
        role: 'user',
        content: 'Message 10'
      });

      expect(memory.messages).toHaveLength(10);
      expect(memory.messages[0].content).toBe('Message 1');
    });

    test('should handle messages at max length boundary', async () => {
      const maxLengthContent = 'a'.repeat(1000); // Exactly maxMessageLength
      const message = {
        role: 'user',
        content: maxLengthContent
      };

      await memory.addMessage(message);
      expect(memory.messages[0].content).toBe(maxLengthContent);
    });

    test('should handle empty content edge case', async () => {
      const message = {
        role: 'user',
        content: ''
      };

      await expect(memory.addMessage(message)).rejects.toThrow(ValidationError);
    });

    test('should handle whitespace-only content', async () => {
      const message = {
        role: 'user',
        content: '   '
      };

      await memory.addMessage(message);
      expect(memory.messages[0].content).toBe(''); // Trimmed to empty
    });

    test('should handle special characters in content', async () => {
      const message = {
        role: 'user',
        content: 'Special chars: 🚀 ñ ü ß 中文 العربية'
      };

      await memory.addMessage(message);
      expect(memory.messages[0].content).toBe('Special chars: 🚀 ñ ü ß 中文 العربية');
    });

    test('should handle concurrent operations', async () => {
      // Simulate concurrent addMessage calls
      const promises = [];
      for (let i = 0; i < 5; i++) {
        promises.push(memory.addMessage({
          role: 'user',
          content: `Concurrent message ${i}`
        }));
      }

      await Promise.all(promises);
      expect(memory.messages).toHaveLength(5);
    });
  });

  describe('Performance and Optimization', () => {
    beforeEach(() => {
      memory = new SimpleMemory('session-123');
    });

    test('should handle large number of messages efficiently', async () => {
      const startTime = Date.now();
      
      // Add many messages (should trigger multiple context window cleanups)
      for (let i = 0; i < 100; i++) {
        await memory.addMessage({
          role: 'user',
          content: `Message ${i}`
        });
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete in reasonable time (less than 1 second)
      expect(duration).toBeLessThan(1000);
      expect(memory.messages).toHaveLength(10); // Only context window size kept
    });

    test('should optimize memory usage with context window', async () => {
      // Add messages beyond context window
      for (let i = 0; i < 50; i++) {
        await memory.addMessage({
          role: 'user',
          content: `Message ${i}`
        });
      }

      const stats = await memory.getStats();
      
      // Memory usage should be bounded by context window
      expect(stats.memoryUsage.messagesInMemory).toBe(10);
      expect(stats.messageCount).toBe(10);
    });
  });

  describe('Integration with Configuration', () => {
    test('should respect different context window sizes', async () => {
      mockConfig.get.mockImplementation((key, defaultValue) => {
        if (key === 'memory.contextWindow') return 3;
        if (key === 'memory.maxMessageLength') return 1000;
        return defaultValue;
      });

      memory = new SimpleMemory('session-123');

      // Add more messages than context window
      for (let i = 0; i < 5; i++) {
        await memory.addMessage({
          role: 'user',
          content: `Message ${i}`
        });
      }

      expect(memory.messages).toHaveLength(3);
      expect(memory.messages[0].content).toBe('Message 2');
    });

    test('should respect different max message lengths', async () => {
      mockConfig.get.mockImplementation((key, defaultValue) => {
        if (key === 'memory.contextWindow') return 10;
        if (key === 'memory.maxMessageLength') return 50;
        return defaultValue;
      });

      memory = new SimpleMemory('session-123');

      const longMessage = {
        role: 'user',
        content: 'a'.repeat(100) // Exceeds limit of 50
      };

      await expect(memory.addMessage(longMessage)).rejects.toThrow(ValidationError);
    });
  });
});