/**
 * Comprehensive tests for MemoryFactory
 * Tests cover memory creation, validation, and configuration scenarios
 */

const MemoryFactory = require('../memoryFactory');
const SimpleMemory = require('../simpleMemory');
const InMemorySummaryMemory = require('../inMemorySummaryMemory');
const { ConfigManager } = require('../../config');

// Mock dependencies
jest.mock('../simpleMemory');
jest.mock('../inMemorySummaryMemory');
jest.mock('../../config', () => ({
  ConfigManager: jest.fn()
}));
jest.mock('../../utils/logger', () => ({
  defaultLogger: {
    child: jest.fn(() => ({
      info: jest.fn(),
      debug: jest.fn(),
      warn: jest.fn(),
      error: jest.fn()
    }))
  }
}));

describe('MemoryFactory', () => {
  let mockConfig;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Setup mock config
    mockConfig = {
      get: jest.fn(),
      validateConfiguration: jest.fn()
    };

    ConfigManager.mockImplementation(() => mockConfig);

    // Setup mock constructors
    SimpleMemory.mockImplementation((sessionId, config) => ({
      sessionId,
      config,
      type: 'SimpleMemory'
    }));

    InMemorySummaryMemory.mockImplementation((sessionId, enableKnowledge, config) => ({
      sessionId,
      enableKnowledgeExtraction: enableKnowledge,
      config,
      type: 'InMemorySummaryMemory'
    }));

    // Mock console methods to avoid noise in tests
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    // Restore console methods
    jest.restoreAllMocks();
  });

  describe('createMemory', () => {
    test('should create SimpleMemory for "simple" type', () => {
      const memory = MemoryFactory.createMemory('simple', 'session-123');

      expect(SimpleMemory).toHaveBeenCalledWith('session-123', expect.any(Object));
      expect(memory.type).toBe('SimpleMemory');
      expect(memory.sessionId).toBe('session-123');
    });

    test('should create InMemorySummaryMemory for "summary" type', () => {
      const memory = MemoryFactory.createMemory('summary', 'session-456');

      expect(InMemorySummaryMemory).toHaveBeenCalledWith('session-456', false, expect.any(Object));
      expect(memory.type).toBe('InMemorySummaryMemory');
      expect(memory.enableKnowledgeExtraction).toBe(false);
    });

    test('should create InMemorySummaryMemory with knowledge extraction for "summary_with_knowledge" type', () => {
      const memory = MemoryFactory.createMemory('summary_with_knowledge', 'session-789');

      expect(InMemorySummaryMemory).toHaveBeenCalledWith('session-789', true, expect.any(Object));
      expect(memory.type).toBe('InMemorySummaryMemory');
      expect(memory.enableKnowledgeExtraction).toBe(true);
    });

    test('should use provided ConfigManager', () => {
      const customConfig = { custom: true };
      MemoryFactory.createMemory('simple', 'session-123', customConfig);

      expect(SimpleMemory).toHaveBeenCalledWith('session-123', customConfig);
    });

    test('should create default ConfigManager when none provided', () => {
      MemoryFactory.createMemory('simple', 'session-123');

      expect(ConfigManager).toHaveBeenCalled();
      expect(SimpleMemory).toHaveBeenCalledWith('session-123', expect.any(Object));
    });

    test('should throw error for invalid sessionId', () => {
      expect(() => {
        MemoryFactory.createMemory('simple', '');
      }).toThrow('sessionId must be a non-empty string');

      expect(() => {
        MemoryFactory.createMemory('simple', null);
      }).toThrow('sessionId must be a non-empty string');

      expect(() => {
        MemoryFactory.createMemory('simple', 123);
      }).toThrow('sessionId must be a non-empty string');
    });

    test('should throw error for invalid type', () => {
      expect(() => {
        MemoryFactory.createMemory('', 'session-123');
      }).toThrow('type must be a non-empty string');

      expect(() => {
        MemoryFactory.createMemory(null, 'session-123');
      }).toThrow('type must be a non-empty string');
    });

    test('should fallback to simple memory for unknown type', () => {
      const memory = MemoryFactory.createMemory('unknown_type', 'session-123');

      expect(console.warn).toHaveBeenCalledWith(
        expect.stringContaining('Unknown memory type: unknown_type, falling back to simple memory')
      );
      expect(SimpleMemory).toHaveBeenCalledWith('session-123', expect.any(Object));
      expect(memory.type).toBe('SimpleMemory');
    });

    test('should handle memory creation errors', () => {
      SimpleMemory.mockImplementation(() => {
        throw new Error('Memory creation failed');
      });

      expect(() => {
        MemoryFactory.createMemory('simple', 'session-123');
      }).toThrow('Memory creation failed: Memory creation failed');

      expect(console.error).toHaveBeenCalledWith(
        'Failed to create simple memory:',
        'Memory creation failed'
      );
    });
  });

  describe('getSupportedTypes', () => {
    test('should return array of supported memory types', () => {
      const types = MemoryFactory.getSupportedTypes();

      expect(Array.isArray(types)).toBe(true);
      expect(types).toContain('simple');
      expect(types).toContain('summary');
      expect(types).toContain('summary_with_knowledge');
      expect(types).toHaveLength(3);
    });
  });

  describe('isValidMemoryType', () => {
    test('should return true for valid memory types', () => {
      expect(MemoryFactory.isValidMemoryType('simple')).toBe(true);
      expect(MemoryFactory.isValidMemoryType('summary')).toBe(true);
      expect(MemoryFactory.isValidMemoryType('summary_with_knowledge')).toBe(true);
    });

    test('should return false for invalid memory types', () => {
      expect(MemoryFactory.isValidMemoryType('invalid')).toBe(false);
      expect(MemoryFactory.isValidMemoryType('')).toBe(false);
      expect(MemoryFactory.isValidMemoryType(null)).toBe(false);
      expect(MemoryFactory.isValidMemoryType(undefined)).toBe(false);
      expect(MemoryFactory.isValidMemoryType(123)).toBe(false);
    });
  });

  describe('getMemoryTypeInfo', () => {
    test('should return info for valid memory types', () => {
      const simpleInfo = MemoryFactory.getMemoryTypeInfo('simple');
      expect(simpleInfo).toEqual(expect.objectContaining({
        name: 'Simple Memory',
        description: expect.any(String),
        class: 'SimpleMemory',
        features: expect.arrayContaining(['Message storage'])
      }));

      const summaryInfo = MemoryFactory.getMemoryTypeInfo('summary');
      expect(summaryInfo).toEqual(expect.objectContaining({
        name: 'Summary Memory',
        description: expect.any(String),
        class: 'InMemorySummaryMemory',
        features: expect.arrayContaining(['Automatic summarization'])
      }));

      const knowledgeInfo = MemoryFactory.getMemoryTypeInfo('summary_with_knowledge');
      expect(knowledgeInfo).toEqual(expect.objectContaining({
        name: 'Summary Memory with Knowledge Extraction',
        description: expect.any(String),
        class: 'InMemorySummaryMemory',
        features: expect.arrayContaining(['Knowledge extraction'])
      }));
    });

    test('should return null for invalid memory types', () => {
      expect(MemoryFactory.getMemoryTypeInfo('invalid')).toBeNull();
      expect(MemoryFactory.getMemoryTypeInfo('')).toBeNull();
      expect(MemoryFactory.getMemoryTypeInfo(null)).toBeNull();
    });
  });

  describe('getAllMemoryTypes', () => {
    test('should return complete memory type registry', () => {
      const allTypes = MemoryFactory.getAllMemoryTypes();

      expect(typeof allTypes).toBe('object');
      expect(allTypes).toHaveProperty('simple');
      expect(allTypes).toHaveProperty('summary');
      expect(allTypes).toHaveProperty('summary_with_knowledge');

      // Should be a copy, not the original
      expect(allTypes).not.toBe(MemoryFactory.MEMORY_TYPES);
    });
  });

  describe('createMemoryWithValidation', () => {
    test('should create memory with validation', () => {
      const memory = MemoryFactory.createMemoryWithValidation({
        type: 'simple',
        sessionId: 'session-123'
      });

      expect(SimpleMemory).toHaveBeenCalledWith('session-123', expect.any(Object));
      expect(memory.type).toBe('SimpleMemory');
    });

    test('should validate configuration when requested', () => {
      const customConfig = { validateConfiguration: jest.fn() };

      MemoryFactory.createMemoryWithValidation({
        type: 'simple',
        sessionId: 'session-123',
        configManager: customConfig,
        validateConfig: true
      });

      expect(customConfig.validateConfiguration).toHaveBeenCalled();
    });

    test('should throw error for missing options', () => {
      expect(() => {
        MemoryFactory.createMemoryWithValidation();
      }).toThrow('Options object is required');

      expect(() => {
        MemoryFactory.createMemoryWithValidation(null);
      }).toThrow('Options object is required');
    });

    test('should throw error for missing required fields', () => {
      expect(() => {
        MemoryFactory.createMemoryWithValidation({});
      }).toThrow('Memory type is required');

      expect(() => {
        MemoryFactory.createMemoryWithValidation({ type: 'simple' });
      }).toThrow('Session ID is required');
    });

    test('should throw error for invalid memory type', () => {
      expect(() => {
        MemoryFactory.createMemoryWithValidation({
          type: 'invalid',
          sessionId: 'session-123'
        });
      }).toThrow('Invalid memory type: invalid. Supported types: simple, summary, summary_with_knowledge');
    });

    test('should throw error for configuration validation failure', () => {
      const customConfig = {
        validateConfiguration: jest.fn(() => {
          throw new Error('Config validation failed');
        })
      };

      expect(() => {
        MemoryFactory.createMemoryWithValidation({
          type: 'simple',
          sessionId: 'session-123',
          configManager: customConfig,
          validateConfig: true
        });
      }).toThrow('Configuration validation failed: Config validation failed');
    });

    test('should skip configuration validation when disabled', () => {
      const customConfig = {
        validateConfiguration: jest.fn(() => {
          throw new Error('Should not be called');
        })
      };

      expect(() => {
        MemoryFactory.createMemoryWithValidation({
          type: 'simple',
          sessionId: 'session-123',
          configManager: customConfig,
          validateConfig: false
        });
      }).not.toThrow();

      expect(customConfig.validateConfiguration).not.toHaveBeenCalled();
    });
  });

  describe('getRecommendedType', () => {
    test('should recommend simple for simplicity preference', () => {
      const recommended = MemoryFactory.getRecommendedType({
        simplicity: true
      });

      expect(recommended).toBe('simple');
    });

    test('should recommend summary_with_knowledge for knowledge extraction', () => {
      const recommended = MemoryFactory.getRecommendedType({
        needsKnowledgeExtraction: true
      });

      expect(recommended).toBe('summary_with_knowledge');
    });

    test('should recommend summary for summarization needs', () => {
      const recommended = MemoryFactory.getRecommendedType({
        needsSummarization: true
      });

      expect(recommended).toBe('summary');
    });

    test('should recommend summary for high volume', () => {
      const recommended = MemoryFactory.getRecommendedType({
        highVolume: true
      });

      expect(recommended).toBe('summary');
    });

    test('should recommend simple as default', () => {
      const recommended = MemoryFactory.getRecommendedType({});
      expect(recommended).toBe('simple');

      const recommendedNoArgs = MemoryFactory.getRecommendedType();
      expect(recommendedNoArgs).toBe('simple');
    });

    test('should prioritize knowledge extraction over other features', () => {
      const recommended = MemoryFactory.getRecommendedType({
        needsKnowledgeExtraction: true,
        needsSummarization: true,
        highVolume: true,
        simplicity: true
      });

      expect(recommended).toBe('summary_with_knowledge');
    });
  });

  describe('Static Properties', () => {
    test('should have MEMORY_TYPES constant', () => {
      expect(MemoryFactory.MEMORY_TYPES).toBeDefined();
      expect(typeof MemoryFactory.MEMORY_TYPES).toBe('object');
      expect(MemoryFactory.MEMORY_TYPES).toHaveProperty('simple');
      expect(MemoryFactory.MEMORY_TYPES).toHaveProperty('summary');
      expect(MemoryFactory.MEMORY_TYPES).toHaveProperty('summary_with_knowledge');
    });

    test('should have consistent memory type structure', () => {
      Object.values(MemoryFactory.MEMORY_TYPES).forEach(typeInfo => {
        expect(typeInfo).toHaveProperty('name');
        expect(typeInfo).toHaveProperty('description');
        expect(typeInfo).toHaveProperty('class');
        expect(typeInfo).toHaveProperty('features');
        expect(Array.isArray(typeInfo.features)).toBe(true);
      });
    });
  });

  describe('Integration Tests', () => {
    test('should create different memory types with same interface', () => {
      const simpleMemory = MemoryFactory.createMemory('simple', 'session-1');
      const summaryMemory = MemoryFactory.createMemory('summary', 'session-2');
      const knowledgeMemory = MemoryFactory.createMemory('summary_with_knowledge', 'session-3');

      expect(simpleMemory.sessionId).toBe('session-1');
      expect(summaryMemory.sessionId).toBe('session-2');
      expect(knowledgeMemory.sessionId).toBe('session-3');

      expect(summaryMemory.enableKnowledgeExtraction).toBe(false);
      expect(knowledgeMemory.enableKnowledgeExtraction).toBe(true);
    });

    test('should handle factory method chaining', () => {
      const types = MemoryFactory.getSupportedTypes();
      
      types.forEach(type => {
        expect(MemoryFactory.isValidMemoryType(type)).toBe(true);
        expect(MemoryFactory.getMemoryTypeInfo(type)).not.toBeNull();
        
        const memory = MemoryFactory.createMemory(type, `session-${type}`);
        expect(memory.sessionId).toBe(`session-${type}`);
      });
    });
  });
});