/**
 * Tests for MemoryInterface abstract base class
 */

const MemoryInterface = require('../memoryInterface');

describe('MemoryInterface', () => {
  describe('Constructor', () => {
    test('should throw error when instantiated directly', () => {
      expect(() => {
        new MemoryInterface('session-123');
      }).toThrow("Abstract class 'MemoryInterface' cannot be instantiated directly");
    });

    test('should throw error with invalid sessionId', () => {
      class TestMemory extends MemoryInterface {}
      
      expect(() => {
        new TestMemory();
      }).toThrow('sessionId must be a non-empty string');

      expect(() => {
        new TestMemory('');
      }).toThrow('sessionId must be a non-empty string');

      expect(() => {
        new TestMemory(123);
      }).toThrow('sessionId must be a non-empty string');

      expect(() => {
        new TestMemory(null);
      }).toThrow('sessionId must be a non-empty string');
    });

    test('should create instance with valid sessionId in subclass', () => {
      class TestMemory extends MemoryInterface {
        async addMessage() {}
        async getMemoryContext() {}
        async clearMemory() {}
      }

      const memory = new TestMemory('session-123');
      expect(memory.sessionId).toBe('session-123');
      expect(memory.getSessionId()).toBe('session-123');
    });
  });

  describe('Abstract Methods', () => {
    let TestMemory;
    let memory;

    beforeEach(() => {
      TestMemory = class extends MemoryInterface {
        async addMessage() {}
        async getMemoryContext() {}
        async clearMemory() {}
      };
      memory = new TestMemory('session-123');
    });

    test('should throw error for unimplemented addMessage', async () => {
      TestMemory = class extends MemoryInterface {};
      memory = new TestMemory('session-123');

      await expect(memory.addMessage({})).rejects.toThrow("Method 'addMessage' must be implemented");
    });

    test('should throw error for unimplemented getMemoryContext', async () => {
      TestMemory = class extends MemoryInterface {};
      memory = new TestMemory('session-123');

      await expect(memory.getMemoryContext()).rejects.toThrow("Method 'getMemoryContext' must be implemented");
    });

    test('should throw error for unimplemented clearMemory', async () => {
      TestMemory = class extends MemoryInterface {};
      memory = new TestMemory('session-123');

      await expect(memory.clearMemory()).rejects.toThrow("Method 'clearMemory' must be implemented");
    });
  });

  describe('getSessionId', () => {
    test('should return correct session ID', () => {
      class TestMemory extends MemoryInterface {
        async addMessage() {}
        async getMemoryContext() {}
        async clearMemory() {}
      }

      const sessionId = 'test-session-456';
      const memory = new TestMemory(sessionId);
      expect(memory.getSessionId()).toBe(sessionId);
    });
  });

  describe('getStats', () => {
    test('should return default stats', async () => {
      class TestMemory extends MemoryInterface {
        async addMessage() {}
        async getMemoryContext() {}
        async clearMemory() {}
      }

      const memory = new TestMemory('session-789');
      const stats = await memory.getStats();

      expect(stats).toEqual({
        sessionId: 'session-789',
        messageCount: 0,
        type: 'TestMemory'
      });
    });

    test('should allow subclass to override stats', async () => {
      class TestMemory extends MemoryInterface {
        constructor(sessionId) {
          super(sessionId);
          this.messages = ['msg1', 'msg2'];
        }

        async addMessage() {}
        async getMemoryContext() {}
        async clearMemory() {}

        async getStats() {
          return {
            ...await super.getStats(),
            messageCount: this.messages.length,
            customProperty: 'test'
          };
        }
      }

      const memory = new TestMemory('session-custom');
      const stats = await memory.getStats();

      expect(stats).toEqual({
        sessionId: 'session-custom',
        messageCount: 2,
        type: 'TestMemory',
        customProperty: 'test'
      });
    });
  });
});