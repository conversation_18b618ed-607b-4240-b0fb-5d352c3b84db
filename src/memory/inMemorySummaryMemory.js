/**
 * In-Memory Summary Memory Implementation
 *
 * An advanced memory implementation that automatically summarizes older messages
 * and optionally extracts knowledge to maintain conversation context while
 * managing memory usage efficiently. This implementation provides intelligent
 * conversation history management with LLM-powered summarization.
 *
 * Features:
 * - Automatic message summarization when threshold is reached
 * - Optional knowledge extraction from conversations
 * - Configurable context window and summarization thresholds
 * - Robust error handling with fallback strategies
 * - Performance monitoring and detailed logging
 * - LLM response validation and retry logic
 *
 * @example
 * // Basic usage with summarization
 * const memory = new InMemorySummaryMemory('session-123');
 * await memory.addMessage({ role: 'user', content: 'Hello' });
 *
 * @example
 * // With knowledge extraction enabled
 * const memory = new InMemorySummaryMemory('session-456', true);
 * await memory.addMessage({ role: 'user', content: 'My name is <PERSON>' });
 * const context = await memory.getMemoryContext();
 *
 * @example
 * // With custom configuration
 * const config = new ConfigManager();
 * const memory = new InMemorySummaryMemory('session-789', false, config);
 */

const MemoryInterface = require('./memoryInterface');
const LLMModel = require('../models/llmModel');
const { prompts } = require('../utils/prompts');
const { ConfigManager } = require('../config');
const { MemoryError, ValidationError, APIError, ErrorFactory } = require('../utils/errors');
const { defaultLogger } = require('../utils/logger');

class InMemorySummaryMemory extends MemoryInterface {
  /**
   * Creates a new InMemorySummaryMemory instance
   * 
   * @param {string} sessionId - Unique session identifier for tracking conversations
   * @param {boolean} [enableKnowledgeExtraction=false] - Whether to extract knowledge from messages
   * @param {ConfigManager} [configManager] - Optional ConfigManager instance for configuration
   * @throws {ValidationError} If sessionId is invalid
   * @throws {ConfigurationError} If configuration is invalid
   * @throws {MemoryError} If LLM models cannot be initialized
   * 
   * @example
   * const memory = new InMemorySummaryMemory('user-session-123');
   * 
   * @example
   * const memory = new InMemorySummaryMemory('session-456', true, configManager);
   */
  constructor(sessionId, enableKnowledgeExtraction = false, configManager = null) {
    try {
      super(sessionId);
      
      // Initialize configuration
      this.config = configManager || new ConfigManager();
      
      // Create logger with session context
      this.logger = defaultLogger.child({ 
        component: 'InMemorySummaryMemory', 
        sessionId: this.sessionId 
      });
      
      // Load and validate configuration
      this._loadConfiguration(enableKnowledgeExtraction);
      
      // Initialize in-memory storage with metadata
      this.storage = {
        messages: [],
        summary: '',
        knowledge: '',
        metadata: {
          lastSummarization: null,
          lastKnowledgeExtraction: null,
          summarizationCount: 0,
          knowledgeExtractionCount: 0,
          totalMessagesProcessed: 0
        }
      };

      // Initialize LLM models with error handling
      this._initializeLLMModels();
      
      // Initialize performance tracking
      this._performanceStats = {
        addMessageCount: 0,
        getContextCount: 0,
        summarizationCount: 0,
        knowledgeExtractionCount: 0,
        totalSummarizationTime: 0,
        totalKnowledgeExtractionTime: 0,
        averageSummarizationTime: 0,
        averageKnowledgeExtractionTime: 0,
        failedSummarizations: 0,
        failedKnowledgeExtractions: 0
      };
      
      this.logger.info('InMemorySummaryMemory initialized successfully', {
        enableKnowledgeExtraction: this.enableKnowledgeExtraction,
        recentMessagesCount: this.recentMessagesCount,
        summaryThreshold: this.summaryThreshold,
        maxSummaryLength: this.maxSummaryLength,
        maxKnowledgeLength: this.maxKnowledgeLength
      });
      
    } catch (error) {
      const wrappedError = ErrorFactory.createError(error, {
        operation: 'constructor',
        sessionId,
        memoryType: 'InMemorySummaryMemory',
        enableKnowledgeExtraction
      });
      
      if (defaultLogger) {
        defaultLogger.error('Failed to initialize InMemorySummaryMemory', {
          error: wrappedError.getFormattedMessage(),
          sessionId
        });
      }
      
      throw wrappedError;
    }
  }

  /**
   * Load and validate configuration settings
   * @private
   * @param {boolean} enableKnowledgeExtraction - Knowledge extraction setting
   * @throws {ConfigurationError} If configuration is invalid
   */
  _loadConfiguration(enableKnowledgeExtraction) {
    try {
      // Knowledge extraction setting
      this.enableKnowledgeExtraction = enableKnowledgeExtraction || 
        this.config.get('memory.enableKnowledgeExtraction', false);
      
      // Context window and summarization settings
      this.recentMessagesCount = this.config.get('memory.contextWindow');
      this.summaryThreshold = this.config.get('memory.summaryThreshold');
      
      // Validate required numeric settings
      if (!Number.isInteger(this.recentMessagesCount) || this.recentMessagesCount < 1) {
        throw new ValidationError('Context window must be a positive integer', {
          field: 'memory.contextWindow',
          value: this.recentMessagesCount
        });
      }
      
      if (!Number.isInteger(this.summaryThreshold) || this.summaryThreshold < 1) {
        throw new ValidationError('Summary threshold must be a positive integer', {
          field: 'memory.summaryThreshold',
          value: this.summaryThreshold
        });
      }
      
      // Optional configuration with defaults
      this.maxSummaryLength = this.config.get('memory.maxSummaryLength', 2000);
      this.maxKnowledgeLength = this.config.get('memory.maxKnowledgeLength', 1000);
      this.enableSummaryValidation = this.config.get('memory.enableSummaryValidation', true);
      this.enableFallbackStrategies = this.config.get('memory.enableFallbackStrategies', true);
      this.maxRetryAttempts = this.config.get('memory.maxRetryAttempts', 3);
      this.enablePerformanceLogging = this.config.get('memory.enablePerformanceLogging', true);
      
      // Validate optional settings
      if (!Number.isInteger(this.maxSummaryLength) || this.maxSummaryLength < 100) {
        throw new ValidationError('Max summary length must be at least 100 characters', {
          field: 'memory.maxSummaryLength',
          value: this.maxSummaryLength
        });
      }
      
      if (!Number.isInteger(this.maxKnowledgeLength) || this.maxKnowledgeLength < 50) {
        throw new ValidationError('Max knowledge length must be at least 50 characters', {
          field: 'memory.maxKnowledgeLength',
          value: this.maxKnowledgeLength
        });
      }
      
    } catch (error) {
      throw ErrorFactory.createError(error, {
        operation: 'loadConfiguration',
        memoryType: 'InMemorySummaryMemory'
      });
    }
  }

  /**
   * Initialize LLM models with error handling
   * @private
   * @throws {MemoryError} If LLM models cannot be initialized
   */
  _initializeLLMModels() {
    try {
      // Initialize summary model
      const summaryModelName = this.config.get('models.summary.name');
      if (!summaryModelName) {
        throw new ValidationError('Summary model name is required', {
          field: 'models.summary.name',
          value: summaryModelName
        });
      }
      
      this.summaryModel = new LLMModel(summaryModelName, this.config);
      
      // Initialize knowledge extraction model if enabled
      if (this.enableKnowledgeExtraction) {
        const knowledgeModelName = this.config.get('models.knowledgeExtraction.name');
        if (!knowledgeModelName) {
          throw new ValidationError('Knowledge extraction model name is required when knowledge extraction is enabled', {
            field: 'models.knowledgeExtraction.name',
            value: knowledgeModelName
          });
        }
        
        this.knowledgeModel = new LLMModel(knowledgeModelName, this.config);
      }
      
    } catch (error) {
      throw new MemoryError('Failed to initialize LLM models', {
        operation: 'initializeLLMModels',
        memoryType: 'InMemorySummaryMemory',
        enableKnowledgeExtraction: this.enableKnowledgeExtraction,
        originalError: error.message
      });
    }
  }

  /**
   * Add a message to memory with automatic summarization and knowledge extraction
   * 
   * @param {Object} message - Message object to store
   * @param {string} message.role - Message role ('user', 'assistant', 'system')
   * @param {string} message.content - Message content/text
   * @param {string} [message.timestamp] - Optional timestamp (ISO string)
   * @param {Object} [message.metadata] - Optional additional metadata
   * @returns {Promise<void>} Promise that resolves when message is stored
   * @throws {ValidationError} If message format is invalid
   * @throws {MemoryError} If memory operation fails
   * @throws {APIError} If LLM operations fail
   * 
   * @example
   * await memory.addMessage({
   *   role: 'user',
   *   content: 'Tell me about machine learning',
   *   timestamp: new Date().toISOString()
   * });
   */
  async addMessage(message) {
    const timerId = this.enablePerformanceLogging ? 
      this.logger.startTimer('addMessage') : null;
    
    try {
      // Validate message input
      this._validateMessage(message);
      
      // Create a sanitized copy of the message
      const sanitizedMessage = this._sanitizeMessage(message);
      
      // Add message to storage
      this.storage.messages.push(sanitizedMessage);
      this.storage.metadata.totalMessagesProcessed++;
      this._performanceStats.addMessageCount++;
      
      // Check if we need to summarize
      const messageCount = this.storage.messages.length;
      const summarizationTrigger = this.recentMessagesCount + this.summaryThreshold;

      this.logger.debug('Message added to memory', {
        messageRole: sanitizedMessage.role,
        messageLength: sanitizedMessage.content.length,
        totalMessages: messageCount,
        summarizationTrigger
      });

      if (messageCount >= summarizationTrigger) {
        this.logger.info('Triggering summarization', {
          messageCount,
          summarizationTrigger,
          sessionId: this.sessionId
        });
        
        await this.summarizeOldMessages();
      } else {
        this.logger.debug('Summarization not needed yet', {
          messageCount,
          summarizationTrigger,
          remaining: summarizationTrigger - messageCount
        });
      }
      
    } catch (error) {
      const wrappedError = ErrorFactory.createError(error, {
        operation: 'addMessage',
        memoryType: 'InMemorySummaryMemory',
        sessionId: this.sessionId,
        messageRole: message?.role,
        messageLength: message?.content?.length,
        currentMessageCount: this.storage.messages.length
      });
      
      this.logger.error('Failed to add message', {
        error: wrappedError.getFormattedMessage(),
        messagePreview: message?.content?.substring(0, 100)
      });
      
      throw wrappedError;
    } finally {
      if (timerId && this.enablePerformanceLogging) {
        this.logger.endTimer(timerId, 'addMessage');
      }
    }
  }

  /**
   * Get formatted memory context for the current conversation
   * 
   * @param {Object} [currentMessage] - Optional current message for context-aware formatting
   * @param {string} [currentMessage.role] - Current message role
   * @param {string} [currentMessage.content] - Current message content
   * @param {Object} [options] - Optional formatting options
   * @param {number} [options.maxLength] - Maximum context length in characters
   * @param {boolean} [options.includeMetadata] - Whether to include message metadata
   * @param {string} [options.format] - Output format ('text' or 'json')
   * @returns {Promise<string>} Promise that resolves to formatted memory context
   * @throws {MemoryError} If memory retrieval fails
   * 
   * @example
   * const context = await memory.getMemoryContext();
   * console.log(context); // Includes summary, knowledge, and recent messages
   * 
   * @example
   * const context = await memory.getMemoryContext(null, { 
   *   maxLength: 1000, 
   *   format: 'json' 
   * });
   */
  async getMemoryContext(currentMessage, options = {}) {
    const timerId = this.enablePerformanceLogging ? 
      this.logger.startTimer('getMemoryContext') : null;
    
    try {
      // Validate options
      const validatedOptions = this._validateContextOptions(options);
      
      // Update performance statistics
      this._performanceStats.getContextCount++;
      
      let context = '';
      const contextParts = [];

      // Get summary if it exists
      if (this.storage.summary) {
        const summaryPart = `Previous conversation summary:\n${this.storage.summary}`;
        contextParts.push(summaryPart);
        
        this.logger.debug('Including summary in context', {
          summaryLength: this.storage.summary.length
        });
      }

      // Get knowledge if it exists and knowledge extraction is enabled
      if (this.enableKnowledgeExtraction && this.storage.knowledge) {
        const knowledgePart = `Important information about the user:\n${this.storage.knowledge}`;
        contextParts.push(knowledgePart);
        
        this.logger.debug('Including knowledge in context', {
          knowledgeLength: this.storage.knowledge.length
        });
      }

      // Get all recent messages
      if (this.storage.messages.length > 0) {
        const recentMessagesText = this.storage.messages
          .map(msg => {
            let formatted = `${msg.role}: ${msg.content}`;
            if (validatedOptions.includeMetadata && msg.metadata) {
              formatted += ` [${JSON.stringify(msg.metadata)}]`;
            }
            return formatted;
          })
          .join('\n');
        
        const messagesPart = `Recent conversation:\n${recentMessagesText}`;
        contextParts.push(messagesPart);
        
        this.logger.debug('Including recent messages in context', {
          messageCount: this.storage.messages.length,
          messagesLength: recentMessagesText.length
        });
      }

      // Combine context parts
      if (validatedOptions.format === 'json') {
        const contextObject = {
          summary: this.storage.summary || null,
          knowledge: this.enableKnowledgeExtraction ? (this.storage.knowledge || null) : null,
          recentMessages: this.storage.messages,
          metadata: this.storage.metadata
        };
        context = JSON.stringify(contextObject, null, 2);
      } else {
        context = contextParts.join('\n\n');
      }

      // Apply length limit if specified
      if (validatedOptions.maxLength && context.length > validatedOptions.maxLength) {
        context = this._truncateContext(context, validatedOptions.maxLength);
        this.logger.debug('Context truncated due to length limit', {
          originalLength: context.length,
          maxLength: validatedOptions.maxLength
        });
      }

      this.logger.debug('Memory context retrieved successfully', {
        contextLength: context.length,
        hasSummary: !!this.storage.summary,
        hasKnowledge: !!this.storage.knowledge,
        messageCount: this.storage.messages.length,
        format: validatedOptions.format
      });

      return context;
      
    } catch (error) {
      const wrappedError = ErrorFactory.createError(error, {
        operation: 'getMemoryContext',
        memoryType: 'InMemorySummaryMemory',
        sessionId: this.sessionId,
        messageCount: this.storage.messages.length,
        hasSummary: !!this.storage.summary,
        hasKnowledge: !!this.storage.knowledge
      });
      
      this.logger.error('Failed to get memory context', {
        error: wrappedError.getFormattedMessage()
      });
      
      throw wrappedError;
    } finally {
      if (timerId && this.enablePerformanceLogging) {
        this.logger.endTimer(timerId, 'getMemoryContext');
      }
    }
  }

  /**
   * Clear all stored memory for the current session
   * 
   * @returns {Promise<void>} Promise that resolves when memory is cleared
   * @throws {MemoryError} If memory clearing fails
   * 
   * @example
   * await memory.clearMemory();
   * console.log('Memory cleared for session:', memory.sessionId);
   */
  async clearMemory() {
    const timerId = this.enablePerformanceLogging ? 
      this.logger.startTimer('clearMemory') : null;
    
    try {
      const messageCount = this.storage.messages.length;
      const hasSummary = !!this.storage.summary;
      const hasKnowledge = !!this.storage.knowledge;
      
      // Clear storage
      this.storage = {
        messages: [],
        summary: '',
        knowledge: '',
        metadata: {
          lastSummarization: null,
          lastKnowledgeExtraction: null,
          summarizationCount: 0,
          knowledgeExtractionCount: 0,
          totalMessagesProcessed: 0
        }
      };
      
      // Reset performance statistics
      this._performanceStats = {
        addMessageCount: 0,
        getContextCount: 0,
        summarizationCount: 0,
        knowledgeExtractionCount: 0,
        totalSummarizationTime: 0,
        totalKnowledgeExtractionTime: 0,
        averageSummarizationTime: 0,
        averageKnowledgeExtractionTime: 0,
        failedSummarizations: 0,
        failedKnowledgeExtractions: 0
      };
      
      this.logger.info('Memory cleared successfully', {
        clearedMessageCount: messageCount,
        hadSummary: hasSummary,
        hadKnowledge: hasKnowledge
      });
      
    } catch (error) {
      const wrappedError = ErrorFactory.createError(error, {
        operation: 'clearMemory',
        memoryType: 'InMemorySummaryMemory',
        sessionId: this.sessionId
      });
      
      this.logger.error('Failed to clear memory', {
        error: wrappedError.getFormattedMessage()
      });
      
      throw wrappedError;
    } finally {
      if (timerId && this.enablePerformanceLogging) {
        this.logger.endTimer(timerId, 'clearMemory');
      }
    }
  }

  /**
   * Summarize old messages and store the summary with robust error handling
   * 
   * @returns {Promise<void>} Promise that resolves when summarization is complete
   * @throws {MemoryError} If summarization fails
   * @throws {APIError} If LLM API calls fail
   * 
   * @example
   * // Automatically called when message threshold is reached
   * await memory.summarizeOldMessages();
   */
  async summarizeOldMessages() {
    const timerId = this.enablePerformanceLogging ? 
      this.logger.startTimer('summarizeOldMessages') : null;
    
    let attempt = 0;
    const maxAttempts = this.maxRetryAttempts;
    
    while (attempt < maxAttempts) {
      try {
        attempt++;
        
        const messageCount = this.storage.messages.length;
        const messagesToKeep = this.recentMessagesCount;
        const messagesToSummarize = this.summaryThreshold;

        // Validate we have enough messages to summarize
        if (messageCount < messagesToKeep + messagesToSummarize) {
          this.logger.warn('Not enough messages to summarize', {
            messageCount,
            required: messagesToKeep + messagesToSummarize,
            sessionId: this.sessionId
          });
          return;
        }

        // Get the oldest messages to summarize
        const oldMessages = this.storage.messages.slice(0, messagesToSummarize);
        const oldMessagesText = oldMessages
          .map(msg => `${msg.role}: ${msg.content}`)
          .join('\n');

        this.logger.info('Starting summarization', {
          messagesToSummarize,
          messagesToKeep,
          totalMessages: messageCount,
          attempt,
          maxAttempts
        });

        // Get existing summary if any
        const existingSummary = this.storage.summary || '';

        // Create prompt for summarization
        const systemPrompt = prompts.SUMMARY_PROMPT;
        let prompt = '';
        
        if (existingSummary) {
          prompt = `Previous summary:\n${existingSummary}\n\nNew conversation to incorporate:\n${oldMessagesText}\n\n${prompts.SUMMARY_UPDATE_PROMPT}`;
        } else {
          prompt = `Conversation to summarize:\n${oldMessagesText}\n\nCreate a concise summary of this conversation.`;
        }

        // Generate summary with validation
        const newSummary = await this._generateSummaryWithValidation(systemPrompt, prompt, attempt);

        // Store the new summary
        this.storage.summary = newSummary;
        this.storage.metadata.lastSummarization = new Date().toISOString();
        this.storage.metadata.summarizationCount++;

        // Extract knowledge from the messages being summarized
        if (this.enableKnowledgeExtraction) {
          this.logger.info('Starting knowledge extraction from summarized messages', {
            messageCount: oldMessages.length
          });
          
          try {
            await this.extractAndUpdateKnowledge(oldMessages);
          } catch (knowledgeError) {
            // Log knowledge extraction error but don't fail summarization
            this.logger.error('Knowledge extraction failed during summarization', {
              error: knowledgeError.message,
              sessionId: this.sessionId
            });
            
            this._performanceStats.failedKnowledgeExtractions++;
            
            if (!this.enableFallbackStrategies) {
              throw knowledgeError;
            }
          }
        }

        // Remove the summarized messages
        this.storage.messages = this.storage.messages.slice(messagesToSummarize);

        // Update performance statistics
        this._performanceStats.summarizationCount++;
        this._updateSummarizationStats();

        this.logger.info('Summarization completed successfully', {
          summarizedMessages: messagesToSummarize,
          remainingMessages: this.storage.messages.length,
          summaryLength: newSummary.length,
          attempt
        });

        return; // Success, exit retry loop
        
      } catch (error) {
        const wrappedError = ErrorFactory.createError(error, {
          operation: 'summarizeOldMessages',
          memoryType: 'InMemorySummaryMemory',
          sessionId: this.sessionId,
          attempt,
          maxAttempts,
          messageCount: this.storage.messages.length
        });

        this.logger.error('Summarization attempt failed', {
          error: wrappedError.getFormattedMessage(),
          attempt,
          maxAttempts
        });

        this._performanceStats.failedSummarizations++;

        // If this is the last attempt or fallback strategies are disabled, throw the error
        if (attempt >= maxAttempts || !this.enableFallbackStrategies) {
          throw wrappedError;
        }

        // Wait before retrying (exponential backoff)
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
        this.logger.info(`Retrying summarization in ${delay}ms`, { attempt, maxAttempts });
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    if (timerId && this.enablePerformanceLogging) {
      this.logger.endTimer(timerId, 'summarizeOldMessages');
    }
  }

  /**
   * Extract knowledge from messages and update the knowledge with robust error handling
   * 
   * @param {Array} [messagesToProcess] - Optional array of messages to process for knowledge extraction
   * @returns {Promise<void>} Promise that resolves when knowledge extraction is complete
   * @throws {MemoryError} If knowledge extraction fails
   * @throws {APIError} If LLM API calls fail
   * 
   * @example
   * // Extract knowledge from specific messages
   * await memory.extractAndUpdateKnowledge(oldMessages);
   * 
   * @example
   * // Extract knowledge from all messages exceeding context window
   * await memory.extractAndUpdateKnowledge();
   */
  async extractAndUpdateKnowledge(messagesToProcess = null) {
    if (!this.enableKnowledgeExtraction) {
      this.logger.debug('Knowledge extraction is disabled, skipping');
      return;
    }

    const timerId = this.enablePerformanceLogging ? 
      this.logger.startTimer('extractAndUpdateKnowledge') : null;
    
    let attempt = 0;
    const maxAttempts = this.maxRetryAttempts;
    
    while (attempt < maxAttempts) {
      try {
        attempt++;
        
        let messagesToExtractFrom;
        let messagesToExtractFromText;

        if (messagesToProcess) {
          // Use the provided messages
          messagesToExtractFrom = messagesToProcess;
          messagesToExtractFromText = messagesToProcess
            .map(msg => `${msg.role}: ${msg.content}`)
            .join('\n');
          
          this.logger.debug('Using provided messages for knowledge extraction', {
            messageCount: messagesToProcess.length,
            attempt,
            maxAttempts
          });
        } else {
          // Get all messages
          const messageCount = this.storage.messages.length;

          // Only process messages that exceed the context window
          if (messageCount <= this.recentMessagesCount) {
            this.logger.debug('Not enough messages to extract knowledge', {
              messageCount,
              recentMessagesCount: this.recentMessagesCount,
              sessionId: this.sessionId
            });
            return;
          }

          // Get the messages to extract knowledge from (the oldest ones that would be summarized)
          const messagesToExtractCount = messageCount - this.recentMessagesCount;
          messagesToExtractFrom = this.storage.messages.slice(0, messagesToExtractCount);
          messagesToExtractFromText = messagesToExtractFrom
            .map(msg => `${msg.role}: ${msg.content}`)
            .join('\n');
          
          this.logger.debug('Using oldest messages for knowledge extraction', {
            messagesToExtractCount,
            totalMessages: messageCount,
            attempt,
            maxAttempts
          });
        }

        // Get existing knowledge if any
        const existingKnowledgeText = this.storage.knowledge || '';

        // Create prompt for knowledge extraction
        let prompt = '';
        if (existingKnowledgeText) {
          prompt = `Existing knowledge about the user:\n${existingKnowledgeText}\n\nNew conversation to analyze:\n${messagesToExtractFromText}\n\n${prompts.KNOWLEDGE_MERGE_PROMPT}`;
        } else {
          prompt = `Conversation to analyze:\n${messagesToExtractFromText}\n\n${prompts.KNOWLEDGE_EXTRACTION_PROMPT}`;
        }

        this.logger.info('Starting knowledge extraction', {
          hasExistingKnowledge: !!existingKnowledgeText,
          messagesToProcess: messagesToExtractFrom.length,
          attempt,
          maxAttempts
        });

        // Generate knowledge with validation
        const newKnowledgeResponse = await this._generateKnowledgeWithValidation(prompt, attempt);

        // Store the new knowledge
        this.storage.knowledge = newKnowledgeResponse;
        this.storage.metadata.lastKnowledgeExtraction = new Date().toISOString();
        this.storage.metadata.knowledgeExtractionCount++;

        // Update performance statistics
        this._performanceStats.knowledgeExtractionCount++;
        this._updateKnowledgeExtractionStats();

        this.logger.info('Knowledge extraction completed successfully', {
          knowledgeLength: newKnowledgeResponse.length,
          processedMessages: messagesToExtractFrom.length,
          attempt
        });

        return; // Success, exit retry loop
        
      } catch (error) {
        const wrappedError = ErrorFactory.createError(error, {
          operation: 'extractAndUpdateKnowledge',
          memoryType: 'InMemorySummaryMemory',
          sessionId: this.sessionId,
          attempt,
          maxAttempts,
          messageCount: messagesToProcess?.length || this.storage.messages.length
        });

        this.logger.error('Knowledge extraction attempt failed', {
          error: wrappedError.getFormattedMessage(),
          attempt,
          maxAttempts
        });

        this._performanceStats.failedKnowledgeExtractions++;

        // If this is the last attempt or fallback strategies are disabled, throw the error
        if (attempt >= maxAttempts || !this.enableFallbackStrategies) {
          throw wrappedError;
        }

        // Wait before retrying (exponential backoff)
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
        this.logger.info(`Retrying knowledge extraction in ${delay}ms`, { attempt, maxAttempts });
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    if (timerId && this.enablePerformanceLogging) {
      this.logger.endTimer(timerId, 'extractAndUpdateKnowledge');
    }
  }

  /**
   * Get enhanced memory statistics and metadata
   * 
   * @returns {Promise<Object>} Promise that resolves to comprehensive memory statistics
   * 
   * @example
   * const stats = await memory.getStats();
   * console.log('Summarization performance:', stats.performance.summarization);
   */
  async getStats() {
    return {
      sessionId: this.sessionId,
      messageCount: this.storage.messages.length,
      type: this.constructor.name,
      configuration: {
        enableKnowledgeExtraction: this.enableKnowledgeExtraction,
        recentMessagesCount: this.recentMessagesCount,
        summaryThreshold: this.summaryThreshold,
        maxSummaryLength: this.maxSummaryLength,
        maxKnowledgeLength: this.maxKnowledgeLength,
        enableSummaryValidation: this.enableSummaryValidation,
        enableFallbackStrategies: this.enableFallbackStrategies,
        maxRetryAttempts: this.maxRetryAttempts,
        enablePerformanceLogging: this.enablePerformanceLogging
      },
      storage: {
        messageCount: this.storage.messages.length,
        hasSummary: !!this.storage.summary,
        summaryLength: this.storage.summary.length,
        hasKnowledge: !!this.storage.knowledge,
        knowledgeLength: this.storage.knowledge.length,
        metadata: { ...this.storage.metadata }
      },
      performance: { ...this._performanceStats },
      memoryUsage: {
        estimatedSizeBytes: this._estimateMemoryUsage(),
        messagesInMemory: this.storage.messages.length
      }
    };
  }

  // Private helper methods

  /**
   * Validate message format and content
   * @private
   * @param {Object} message - Message to validate
   * @throws {ValidationError} If message is invalid
   */
  _validateMessage(message) {
    if (!message || typeof message !== 'object') {
      throw new ValidationError('Message must be a non-null object', {
        field: 'message',
        value: typeof message
      });
    }

    // Validate required fields
    if (!message.role || typeof message.role !== 'string') {
      throw new ValidationError('Message role must be a non-empty string', {
        field: 'message.role',
        value: message.role
      });
    }

    if (!message.content || typeof message.content !== 'string') {
      throw new ValidationError('Message content must be a non-empty string', {
        field: 'message.content',
        value: typeof message.content
      });
    }

    // Validate role values
    const validRoles = ['user', 'assistant', 'system'];
    if (!validRoles.includes(message.role)) {
      throw new ValidationError('Message role must be one of: user, assistant, system', {
        field: 'message.role',
        value: message.role,
        validValues: validRoles
      });
    }

    // Validate timestamp if provided
    if (message.timestamp && typeof message.timestamp === 'string') {
      const timestamp = new Date(message.timestamp);
      if (isNaN(timestamp.getTime())) {
        throw new ValidationError('Message timestamp must be a valid ISO string', {
          field: 'message.timestamp',
          value: message.timestamp
        });
      }
    }
  }

  /**
   * Create a sanitized copy of the message
   * @private
   * @param {Object} message - Original message
   * @returns {Object} Sanitized message
   */
  _sanitizeMessage(message) {
    const sanitized = {
      role: message.role.trim().toLowerCase(),
      content: message.content.trim(),
      timestamp: message.timestamp || new Date().toISOString()
    };

    // Include metadata if present
    if (message.metadata && typeof message.metadata === 'object') {
      sanitized.metadata = { ...message.metadata };
    }

    return sanitized;
  }

  /**
   * Validate context retrieval options
   * @private
   * @param {Object} options - Options to validate
   * @returns {Object} Validated options with defaults
   */
  _validateContextOptions(options) {
    const validated = {
      maxLength: null,
      includeMetadata: false,
      format: 'text',
      ...options
    };

    if (validated.maxLength !== null) {
      if (!Number.isInteger(validated.maxLength) || validated.maxLength < 1) {
        throw new ValidationError('maxLength must be a positive integer', {
          field: 'options.maxLength',
          value: validated.maxLength
        });
      }
    }

    if (typeof validated.includeMetadata !== 'boolean') {
      throw new ValidationError('includeMetadata must be a boolean', {
        field: 'options.includeMetadata',
        value: validated.includeMetadata
      });
    }

    if (!['text', 'json'].includes(validated.format)) {
      throw new ValidationError('format must be either "text" or "json"', {
        field: 'options.format',
        value: validated.format,
        validValues: ['text', 'json']
      });
    }

    return validated;
  }

  /**
   * Truncate context to fit within length limit
   * @private
   * @param {string} context - Context to truncate
   * @param {number} maxLength - Maximum allowed length
   * @returns {string} Truncated context
   */
  _truncateContext(context, maxLength) {
    if (context.length <= maxLength) {
      return context;
    }

    const truncationMarker = '\n... [truncated]';
    const availableLength = maxLength - truncationMarker.length;
    
    if (availableLength <= 0) {
      return truncationMarker.substring(1); // Remove leading newline
    }

    return context.substring(0, availableLength) + truncationMarker;
  }

  /**
   * Generate summary with validation and error handling
   * @private
   * @param {string} systemPrompt - System prompt for summarization
   * @param {string} prompt - User prompt for summarization
   * @param {number} attempt - Current attempt number
   * @returns {Promise<string>} Generated and validated summary
   * @throws {APIError} If LLM API call fails
   * @throws {ValidationError} If summary validation fails
   */
  async _generateSummaryWithValidation(systemPrompt, prompt, attempt) {
    try {
      // Generate summary using LLM
      const rawSummary = await this.summaryModel.generateResponse(systemPrompt, [
        { role: 'user', content: prompt }
      ]);

      // Validate summary if validation is enabled
      if (this.enableSummaryValidation) {
        this._validateSummaryResponse(rawSummary, attempt);
      }

      // Truncate if too long
      let summary = rawSummary;
      if (summary.length > this.maxSummaryLength) {
        summary = summary.substring(0, this.maxSummaryLength - 3) + '...';
        this.logger.debug('Summary truncated due to length limit', {
          originalLength: rawSummary.length,
          maxLength: this.maxSummaryLength,
          attempt
        });
      }

      return summary;

    } catch (error) {
      throw ErrorFactory.createError(error, {
        operation: 'generateSummaryWithValidation',
        memoryType: 'InMemorySummaryMemory',
        sessionId: this.sessionId,
        attempt
      });
    }
  }

  /**
   * Generate knowledge with validation and error handling
   * @private
   * @param {string} prompt - User prompt for knowledge extraction
   * @param {number} attempt - Current attempt number
   * @returns {Promise<string>} Generated and validated knowledge
   * @throws {APIError} If LLM API call fails
   * @throws {ValidationError} If knowledge validation fails
   */
  async _generateKnowledgeWithValidation(prompt, attempt) {
    try {
      // Generate knowledge using LLM
      const rawKnowledge = await this.knowledgeModel.generateResponse('', [
        { role: 'user', content: prompt }
      ]);

      // Validate knowledge response
      this._validateKnowledgeResponse(rawKnowledge, attempt);

      // Truncate if too long
      let knowledge = rawKnowledge;
      if (knowledge.length > this.maxKnowledgeLength) {
        knowledge = knowledge.substring(0, this.maxKnowledgeLength - 3) + '...';
        this.logger.debug('Knowledge truncated due to length limit', {
          originalLength: rawKnowledge.length,
          maxLength: this.maxKnowledgeLength,
          attempt
        });
      }

      return knowledge;

    } catch (error) {
      throw ErrorFactory.createError(error, {
        operation: 'generateKnowledgeWithValidation',
        memoryType: 'InMemorySummaryMemory',
        sessionId: this.sessionId,
        attempt
      });
    }
  }

  /**
   * Validate summary response from LLM
   * @private
   * @param {string} summary - Summary to validate
   * @param {number} attempt - Current attempt number
   * @throws {ValidationError} If summary is invalid
   */
  _validateSummaryResponse(summary, attempt) {
    if (!summary || typeof summary !== 'string') {
      throw new ValidationError('Summary must be a non-empty string', {
        field: 'summary',
        value: typeof summary,
        attempt
      });
    }

    if (summary.trim().length < 10) {
      throw new ValidationError('Summary is too short to be meaningful', {
        field: 'summary',
        value: summary.length,
        minLength: 10,
        attempt
      });
    }

    // Check for common LLM failure patterns
    const failurePatterns = [
      /^I (can't|cannot|am unable)/i,
      /^Sorry, I/i,
      /^I don't have/i,
      /^No (summary|information)/i
    ];

    for (const pattern of failurePatterns) {
      if (pattern.test(summary.trim())) {
        throw new ValidationError('Summary contains failure pattern', {
          field: 'summary',
          value: summary.substring(0, 100),
          pattern: pattern.toString(),
          attempt
        });
      }
    }
  }

  /**
   * Validate knowledge response from LLM
   * @private
   * @param {string} knowledge - Knowledge to validate
   * @param {number} attempt - Current attempt number
   * @throws {ValidationError} If knowledge is invalid
   */
  _validateKnowledgeResponse(knowledge, attempt) {
    if (!knowledge || typeof knowledge !== 'string') {
      throw new ValidationError('Knowledge must be a non-empty string', {
        field: 'knowledge',
        value: typeof knowledge,
        attempt
      });
    }

    if (knowledge.trim().length < 5) {
      throw new ValidationError('Knowledge is too short to be meaningful', {
        field: 'knowledge',
        value: knowledge.length,
        minLength: 5,
        attempt
      });
    }

    // Check for common LLM failure patterns
    const failurePatterns = [
      /^I (can't|cannot|am unable)/i,
      /^Sorry, I/i,
      /^I don't have/i,
      /^No (knowledge|information)/i
    ];

    for (const pattern of failurePatterns) {
      if (pattern.test(knowledge.trim())) {
        throw new ValidationError('Knowledge contains failure pattern', {
          field: 'knowledge',
          value: knowledge.substring(0, 100),
          pattern: pattern.toString(),
          attempt
        });
      }
    }
  }

  /**
   * Update summarization performance statistics
   * @private
   */
  _updateSummarizationStats() {
    if (this._performanceStats.summarizationCount > 0) {
      this._performanceStats.averageSummarizationTime = 
        this._performanceStats.totalSummarizationTime / this._performanceStats.summarizationCount;
    }
  }

  /**
   * Update knowledge extraction performance statistics
   * @private
   */
  _updateKnowledgeExtractionStats() {
    if (this._performanceStats.knowledgeExtractionCount > 0) {
      this._performanceStats.averageKnowledgeExtractionTime = 
        this._performanceStats.totalKnowledgeExtractionTime / this._performanceStats.knowledgeExtractionCount;
    }
  }

  /**
   * Estimate memory usage in bytes
   * @private
   * @returns {number} Estimated memory usage in bytes
   */
  _estimateMemoryUsage() {
    let totalSize = 0;
    
    // Messages
    for (const message of this.storage.messages) {
      totalSize += (message.content.length + message.role.length) * 2; // UTF-16
      totalSize += 100; // Base object overhead
      
      if (message.metadata) {
        totalSize += JSON.stringify(message.metadata).length * 2;
      }
    }
    
    // Summary and knowledge
    totalSize += this.storage.summary.length * 2;
    totalSize += this.storage.knowledge.length * 2;
    
    // Metadata
    totalSize += JSON.stringify(this.storage.metadata).length * 2;
    
    return totalSize;
  }
}

module.exports = InMemorySummaryMemory;
