/**
 * Configuration Error Classes
 * 
 * Custom error classes for configuration-related errors with descriptive messages
 * and helpful guidance for users.
 */

/**
 * Base configuration error class
 */
class ConfigError extends Error {
  constructor(message, code = 'CONFIG_ERROR') {
    super(message);
    this.name = 'ConfigError';
    this.code = code;
    Error.captureStackTrace(this, ConfigError);
  }
}

/**
 * Configuration validation error
 */
class ConfigValidationError extends ConfigError {
  constructor(message, validationErrors = []) {
    super(message, 'CONFIG_VALIDATION_ERROR');
    this.name = 'ConfigValidationError';
    this.validationErrors = validationErrors;
  }

  /**
   * Get formatted error message with validation details
   * @returns {string} Formatted error message
   */
  getFormattedMessage() {
    let message = this.message;
    
    if (this.validationErrors.length > 0) {
      message += '\n\nValidation errors:';
      this.validationErrors.forEach(error => {
        message += `\n  - ${error}`;
      });
    }

    message += '\n\nPlease check your environment variables and ensure they match the expected format.';
    message += '\nRefer to the .env.example file for proper configuration examples.';

    return message;
  }
}

/**
 * Missing API key error
 */
class MissingApiKeyError extends ConfigError {
  constructor() {
    const message = `No API key configured. Please set either OPENROUTER_API_KEY or OPENAI_API_KEY in your environment variables.

To fix this:
1. Copy .env.example to .env
2. Set either OPENROUTER_API_KEY or OPENAI_API_KEY (or both)
3. Restart the application

Example:
  OPENROUTER_API_KEY=your_openrouter_key_here
  # OR
  OPENAI_API_KEY=your_openai_key_here`;

    super(message, 'MISSING_API_KEY');
    this.name = 'MissingApiKeyError';
  }
}

/**
 * Invalid model configuration error
 */
class InvalidModelConfigError extends ConfigError {
  constructor(modelType, issue) {
    const message = `Invalid configuration for ${modelType} model: ${issue}

Please check the following:
1. Model name is not empty
2. Temperature is between 0.0 and 2.0
3. Max tokens is between 1 and 4096

Example configuration:
  ${modelType.toUpperCase()}_MODEL=openai/gpt-3.5-turbo
  ${modelType.toUpperCase()}_TEMPERATURE=0.7
  ${modelType.toUpperCase()}_MAX_TOKENS=150`;

    super(message, 'INVALID_MODEL_CONFIG');
    this.name = 'InvalidModelConfigError';
  }
}

/**
 * Invalid memory configuration error
 */
class InvalidMemoryConfigError extends ConfigError {
  constructor(issue) {
    const message = `Invalid memory configuration: ${issue}

Please check the following:
1. MEMORY_TYPE is one of: simple, summary, summary_with_knowledge
2. MEMORY_CONTEXT_WINDOW is between 1 and 100
3. SUMMARY_THRESHOLD is between 5 and 200
4. For summary memory types, SUMMARY_THRESHOLD must be greater than MEMORY_CONTEXT_WINDOW

Example configuration:
  MEMORY_TYPE=simple
  MEMORY_CONTEXT_WINDOW=10
  SUMMARY_THRESHOLD=20
  ENABLE_KNOWLEDGE_EXTRACTION=true`;

    super(message, 'INVALID_MEMORY_CONFIG');
    this.name = 'InvalidMemoryConfigError';
  }
}

/**
 * Invalid test configuration error
 */
class InvalidTestConfigError extends ConfigError {
  constructor(issue) {
    const message = `Invalid test configuration: ${issue}

Please check the following:
1. TEST_FACTS_FILE is not empty
2. TEST_FACTS_COUNT is between 1 and 100
3. MESSAGES_BETWEEN_FACTS is between 1 and 20

Example configuration:
  TEST_FACTS_FILE=simple
  TEST_FACTS_COUNT=10
  MESSAGES_BETWEEN_FACTS=5`;

    super(message, 'INVALID_TEST_CONFIG');
    this.name = 'InvalidTestConfigError';
  }
}

module.exports = {
  ConfigError,
  ConfigValidationError,
  MissingApiKeyError,
  InvalidModelConfigError,
  InvalidMemoryConfigError,
  InvalidTestConfigError
};