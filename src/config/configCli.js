#!/usr/bin/env node

/**
 * Configuration CLI Utility
 * 
 * Command-line utility for configuration management tasks like
 * generating documentation, validating configuration, and providing
 * configuration guidance.
 */

const { ConfigManager } = require('./index');
const fs = require('fs');
const path = require('path');
const { defaultLogger } = require('../utils/logger');

/**
 * Display help information
 */
function showHelp() {
  console.log(`
Configuration CLI Utility

Usage: node src/config/configCli.js <command> [options]

Commands:
  validate    Validate current configuration and show feedback
  docs        Generate configuration documentation
  example     Generate example .env file
  help        Show this help message

Examples:
  node src/config/configCli.js validate
  node src/config/configCli.js docs > CONFIG.md
  node src/config/configCli.js example > .env.example
`);
}

/**
 * Validate configuration and show detailed feedback
 */
function validateConfiguration() {
  console.log('🔍 Validating configuration...\n');
  
  try {
    const config = new ConfigManager();
    const feedback = config.validateConfigurationWithFeedback();
    
    if (feedback.isValid) {
      console.log('✅ Configuration is valid!\n');
    } else {
      console.log('❌ Configuration validation failed:\n');
      feedback.errors.forEach(error => {
        console.log(`  ${error}`);
      });
      console.log('');
    }
    
    if (feedback.warnings.length > 0) {
      console.log('⚠️  Configuration warnings:');
      feedback.warnings.forEach(warning => {
        console.log(`  - ${warning}`);
      });
      console.log('');
    }
    
    if (feedback.suggestions.length > 0) {
      console.log('💡 Configuration suggestions:');
      feedback.suggestions.forEach(suggestion => {
        console.log(`  - ${suggestion}`);
      });
      console.log('');
    }
    
    if (feedback.isValid && feedback.warnings.length === 0) {
      console.log('🎉 Your configuration is optimal!');
    }
    
  } catch (error) {
    console.error('❌ Configuration error:', error.message);
    if (error.getFormattedMessage) {
      console.error('\nDetailed error:');
      console.error(error.getFormattedMessage());
    }
    process.exit(1);
  }
}

/**
 * Generate configuration documentation
 */
function generateDocs() {
  try {
    // Create a temporary config manager with minimal environment
    const originalEnv = process.env.OPENROUTER_API_KEY;
    process.env.OPENROUTER_API_KEY = 'temp-key-for-docs';
    
    const config = new ConfigManager();
    const docs = config.generateConfigurationDocs();
    
    // Restore original environment
    if (originalEnv) {
      process.env.OPENROUTER_API_KEY = originalEnv;
    } else {
      delete process.env.OPENROUTER_API_KEY;
    }
    
    console.log(docs);
  } catch (error) {
    console.error('Error generating documentation:', error.message);
    process.exit(1);
  }
}

/**
 * Generate example .env file
 */
function generateExample() {
  const exampleContent = `# API Credentials
# OpenRouter is used by default, but you can use any OpenAI-compatible API
OPENROUTER_API_KEY=your_openrouter_key_here
# If you want to use OpenAI directly, uncomment and set this:
# OPENAI_API_KEY=your_openai_key_here

# LLM Models
USER_MODEL=openai/gpt-3.5-turbo
ASSISTANT_MODEL=openai/gpt-3.5-turbo
EVALUATOR_MODEL=openai/gpt-3.5-turbo
SUMMARY_MODEL=openai/gpt-3.5-turbo
KNOWLEDGE_EXTRACTION_MODEL=openai/gpt-3.5-turbo

# Model Temperatures (0.0 to 2.0)
USER_TEMPERATURE=0.8
ASSISTANT_TEMPERATURE=0.7
EVALUATOR_TEMPERATURE=0.2
SUMMARY_TEMPERATURE=0.3
KNOWLEDGE_EXTRACTION_TEMPERATURE=0.3

# Model Max Tokens
USER_MAX_TOKENS=150
ASSISTANT_MAX_TOKENS=150
EVALUATOR_MAX_TOKENS=200
SUMMARY_MAX_TOKENS=500
KNOWLEDGE_EXTRACTION_MAX_TOKENS=500

# Memory Configuration
MEMORY_TYPE=simple # Options: simple, summary, summary_with_knowledge
MEMORY_CONTEXT_WINDOW=10 # Number of messages to keep in simple memory
SUMMARY_THRESHOLD=20 # Number of messages before summarization
ENABLE_KNOWLEDGE_EXTRACTION=true # Whether to extract knowledge in summary memory

# Test Configuration
TEST_FACTS_FILE=simple
TEST_FACTS_COUNT=10 # Number of facts to test from the JSON file
MESSAGES_BETWEEN_FACTS=5 # Number of conversation messages between facts

# Output Configuration
SAVE_RESULTS=true # Whether to save results to file

# Verbose logging
VERBOSE_LOGGING=false

# Environment (development, test, production)
NODE_ENV=development
`;

  console.log(exampleContent);
}

/**
 * Main CLI function
 */
function main() {
  const command = process.argv[2];
  
  switch (command) {
    case 'validate':
      validateConfiguration();
      break;
    case 'docs':
      generateDocs();
      break;
    case 'example':
      generateExample();
      break;
    case 'help':
    case '--help':
    case '-h':
      showHelp();
      break;
    default:
      console.error('Unknown command:', command);
      showHelp();
      process.exit(1);
  }
}

// Run CLI if this file is executed directly
if (require.main === module) {
  main();
}

module.exports = {
  validateConfiguration,
  generateDocs,
  generateExample,
  showHelp
};