/**
 * Configuration Module Exports
 * 
 * This module provides a convenient way to import configuration-related classes
 * and utilities.
 */

const ConfigManager = require('./configManager');
const {
  ConfigError,
  ConfigValidationError,
  MissingApiKeyError,
  InvalidModelConfigError,
  InvalidMemoryConfigError,
  InvalidTestConfigError
} = require('./configError');
const { defaults, constraints, memoryTypes, environments } = require('./defaults');

module.exports = {
  ConfigManager,
  ConfigError,
  ConfigValidationError,
  MissingApiKeyError,
  InvalidModelConfigError,
  InvalidMemoryConfigError,
  InvalidTestConfigError,
  defaults,
  constraints,
  memoryTypes,
  environments
};