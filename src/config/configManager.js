/**
 * Centralized Configuration Management System
 * 
 * This module provides a centralized way to load, validate, and access
 * application configuration from environment variables with proper defaults
 * and validation.
 */

const Ajv = require('ajv');
const addFormats = require('ajv-formats');
const { defaults, constraints, memoryTypes, environments } = require('./defaults');
const {
  ConfigValidationError,
  MissingApiKeyError,
  InvalidModelConfigError,
  InvalidMemoryConfigError
} = require('./configError');

/**
 * ConfigManager class handles all configuration loading, validation, and access
 */
class ConfigManager {
  constructor() {
    this.ajv = new Ajv({ allErrors: true });
    addFormats(this.ajv);
    
    this.config = null;
    this.schema = this.getConfigSchema();
    
    this.loadConfiguration();
    this.validateConfiguration();
  }

  /**
   * Load configuration from environment variables with defaults
   * @returns {Object} Configuration object
   */
  loadConfiguration() {
    this.config = {
      // API Configuration
      api: {
        openrouter: {
          apiKey: process.env.OPENROUTER_API_KEY || null,
          baseURL: defaults.api.openrouter.baseURL
        },
        openai: {
          apiKey: process.env.OPENAI_API_KEY || null,
          baseURL: defaults.api.openai.baseURL
        }
      },

      // Model Configuration
      models: {
        user: {
          name: process.env.USER_MODEL || defaults.models.user.name,
          temperature: this.parseFloat(process.env.USER_TEMPERATURE, defaults.models.user.temperature),
          maxTokens: this.parseInt(process.env.USER_MAX_TOKENS, defaults.models.user.maxTokens)
        },
        assistant: {
          name: process.env.ASSISTANT_MODEL || defaults.models.assistant.name,
          temperature: this.parseFloat(process.env.ASSISTANT_TEMPERATURE, defaults.models.assistant.temperature),
          maxTokens: this.parseInt(process.env.ASSISTANT_MAX_TOKENS, defaults.models.assistant.maxTokens)
        },
        evaluator: {
          name: process.env.EVALUATOR_MODEL || defaults.models.evaluator.name,
          temperature: this.parseFloat(process.env.EVALUATOR_TEMPERATURE, defaults.models.evaluator.temperature),
          maxTokens: this.parseInt(process.env.EVALUATOR_MAX_TOKENS, defaults.models.evaluator.maxTokens)
        },
        summary: {
          name: process.env.SUMMARY_MODEL || defaults.models.summary.name,
          temperature: this.parseFloat(process.env.SUMMARY_TEMPERATURE, defaults.models.summary.temperature),
          maxTokens: this.parseInt(process.env.SUMMARY_MAX_TOKENS, defaults.models.summary.maxTokens)
        },
        knowledgeExtraction: {
          name: process.env.KNOWLEDGE_EXTRACTION_MODEL || defaults.models.knowledgeExtraction.name,
          temperature: this.parseFloat(process.env.KNOWLEDGE_EXTRACTION_TEMPERATURE, defaults.models.knowledgeExtraction.temperature),
          maxTokens: this.parseInt(process.env.KNOWLEDGE_EXTRACTION_MAX_TOKENS, defaults.models.knowledgeExtraction.maxTokens)
        }
      },

      // Memory Configuration
      memory: {
        type: process.env.MEMORY_TYPE || defaults.memory.type,
        contextWindow: this.parseInt(process.env.MEMORY_CONTEXT_WINDOW, defaults.memory.contextWindow),
        summaryThreshold: this.parseInt(process.env.SUMMARY_THRESHOLD, defaults.memory.summaryThreshold),
        enableKnowledgeExtraction: this.parseBoolean(process.env.ENABLE_KNOWLEDGE_EXTRACTION, defaults.memory.enableKnowledgeExtraction)
      },

      // Test Configuration
      test: {
        factsFile: process.env.TEST_FACTS_FILE || defaults.test.factsFile,
        factsCount: this.parseInt(process.env.TEST_FACTS_COUNT, defaults.test.factsCount),
        messagesBetweenFacts: this.parseInt(process.env.MESSAGES_BETWEEN_FACTS, defaults.test.messagesBetweenFacts)
      },

      // Output Configuration
      output: {
        saveResults: this.parseBoolean(process.env.SAVE_RESULTS, defaults.output.saveResults),
        verboseLogging: this.parseBoolean(process.env.VERBOSE_LOGGING, defaults.output.verboseLogging)
      },

      // Environment
      environment: process.env.NODE_ENV || defaults.environment
    };

    return this.config;
  }

  /**
   * Safely parse integer with fallback to default
   * @param {string} value - String value to parse
   * @param {number} defaultValue - Default value if parsing fails
   * @returns {number} Parsed integer or default
   */
  parseInt(value, defaultValue) {
    if (value === undefined || value === null || value === '') {
      return defaultValue;
    }
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? defaultValue : parsed;
  }

  /**
   * Safely parse float with fallback to default
   * @param {string} value - String value to parse
   * @param {number} defaultValue - Default value if parsing fails
   * @returns {number} Parsed float or default
   */
  parseFloat(value, defaultValue) {
    if (value === undefined || value === null || value === '') {
      return defaultValue;
    }
    const parsed = parseFloat(value);
    return isNaN(parsed) ? defaultValue : parsed;
  }

  /**
   * Safely parse boolean with fallback to default
   * @param {string} value - String value to parse
   * @param {boolean} defaultValue - Default value if parsing fails
   * @returns {boolean} Parsed boolean or default
   */
  parseBoolean(value, defaultValue) {
    if (value === undefined || value === null || value === '') {
      return defaultValue;
    }
    return value.toLowerCase() === 'true';
  }

  /**
   * Get the JSON schema for configuration validation
   * @returns {Object} JSON schema object
   */
  getConfigSchema() {
    return {
      type: 'object',
      required: ['models', 'memory', 'test'],
      properties: {
        api: {
          type: 'object',
          properties: {
            openrouter: {
              type: 'object',
              properties: {
                apiKey: { type: ['string', 'null'] },
                baseURL: { type: 'string', format: 'uri' }
              }
            },
            openai: {
              type: 'object',
              properties: {
                apiKey: { type: ['string', 'null'] },
                baseURL: { type: 'string', format: 'uri' }
              }
            }
          }
        },

        models: {
          type: 'object',
          required: ['user', 'assistant', 'evaluator', 'summary', 'knowledgeExtraction'],
          properties: {
            user: this.getModelSchema(),
            assistant: this.getModelSchema(),
            evaluator: this.getModelSchema(),
            summary: this.getModelSchema(),
            knowledgeExtraction: this.getModelSchema()
          }
        },

        memory: {
          type: 'object',
          required: ['type'],
          properties: {
            type: {
              type: 'string',
              enum: memoryTypes
            },
            contextWindow: {
              type: 'integer',
              minimum: constraints.memory.contextWindow.min,
              maximum: constraints.memory.contextWindow.max
            },
            summaryThreshold: {
              type: 'integer',
              minimum: constraints.memory.summaryThreshold.min,
              maximum: constraints.memory.summaryThreshold.max
            },
            enableKnowledgeExtraction: {
              type: 'boolean'
            }
          }
        },

        test: {
          type: 'object',
          required: ['factsFile', 'factsCount', 'messagesBetweenFacts'],
          properties: {
            factsFile: {
              type: 'string',
              minLength: 1
            },
            factsCount: {
              type: 'integer',
              minimum: constraints.test.factsCount.min,
              maximum: constraints.test.factsCount.max
            },
            messagesBetweenFacts: {
              type: 'integer',
              minimum: constraints.test.messagesBetweenFacts.min,
              maximum: constraints.test.messagesBetweenFacts.max
            }
          }
        },

        output: {
          type: 'object',
          properties: {
            saveResults: { type: 'boolean' },
            verboseLogging: { type: 'boolean' }
          }
        },

        environment: {
          type: 'string',
          enum: environments
        }
      }
    };
  }

  /**
   * Get schema for individual model configuration
   * @returns {Object} Model schema object
   */
  getModelSchema() {
    return {
      type: 'object',
      required: ['name'],
      properties: {
        name: {
          type: 'string',
          minLength: 1
        },
        temperature: {
          type: 'number',
          minimum: constraints.models.temperature.min,
          maximum: constraints.models.temperature.max
        },
        maxTokens: {
          type: 'integer',
          minimum: constraints.models.maxTokens.min,
          maximum: constraints.models.maxTokens.max
        }
      }
    };
  }

  /**
   * Validate the loaded configuration against the schema
   * @throws {ConfigValidationError} If configuration is invalid
   */
  validateConfiguration() {
    const validate = this.ajv.compile(this.schema);
    const valid = validate(this.config);

    if (!valid) {
      const errors = this.formatValidationErrors(validate.errors);
      throw new ConfigValidationError('Configuration validation failed', errors);
    }

    // Additional business logic validation
    this.validateBusinessRules();
  }

  /**
   * Validate business rules that can't be expressed in JSON schema
   * @throws {Error} If business rules are violated
   */
  validateBusinessRules() {
    // Ensure at least one API key is provided
    const hasOpenRouterKey = this.config.api.openrouter.apiKey;
    const hasOpenAIKey = this.config.api.openai.apiKey;

    if (!hasOpenRouterKey && !hasOpenAIKey) {
      throw new MissingApiKeyError();
    }

    // Validate memory type specific requirements
    if (this.config.memory.type === 'summary' || this.config.memory.type === 'summary_with_knowledge') {
      if (this.config.memory.summaryThreshold <= this.config.memory.contextWindow) {
        throw new InvalidMemoryConfigError(
          'SUMMARY_THRESHOLD must be greater than MEMORY_CONTEXT_WINDOW'
        );
      }
    }

    // Validate model names are not empty
    const modelTypes = ['user', 'assistant', 'evaluator', 'summary', 'knowledgeExtraction'];
    for (const modelType of modelTypes) {
      const modelName = this.config.models[modelType].name;
      if (!modelName || modelName.trim() === '') {
        throw new InvalidModelConfigError(modelType, 'Model name cannot be empty');
      }
    }
  }

  /**
   * Format validation errors into a readable array
   * @param {Array} errors - AJV validation errors
   * @returns {Array} Formatted error messages
   */
  formatValidationErrors(errors) {
    return errors.map(error => {
      const path = error.instancePath || 'root';
      const message = error.message;
      const allowedValues = error.params?.allowedValues 
        ? ` (allowed values: ${error.params.allowedValues.join(', ')})` 
        : '';
      
      // Add helpful context for common validation errors
      let helpText = '';
      if (error.keyword === 'minimum' || error.keyword === 'maximum') {
        helpText = this.getValidationHelpText(path, error.keyword, error.params);
      } else if (error.keyword === 'enum') {
        helpText = this.getEnumHelpText(path);
      }
      
      return `${path}: ${message}${allowedValues}${helpText}`;
    });
  }

  /**
   * Get helpful text for validation errors
   * @param {string} path - Configuration path
   * @param {string} keyword - Validation keyword
   * @param {Object} params - Validation parameters
   * @returns {string} Help text
   */
  getValidationHelpText(path, keyword, params) {
    // Remove leading slash and convert to dot notation
    const normalizedPath = path.replace(/^\//, '').replace(/\//g, '.');
    
    const helpTexts = {
      'models.user.temperature': '\n    Tip: User model temperature should be higher (0.7-1.0) for more creative responses',
      'models.assistant.temperature': '\n    Tip: Assistant temperature should be moderate (0.6-0.8) for balanced responses',
      'models.evaluator.temperature': '\n    Tip: Evaluator temperature should be low (0.1-0.3) for consistent scoring',
      'models.summary.temperature': '\n    Tip: Summary temperature should be low (0.2-0.4) for factual summaries',
      'models.knowledgeExtraction.temperature': '\n    Tip: Knowledge extraction temperature should be low (0.2-0.4) for accuracy',
      'memory.contextWindow': '\n    Tip: Context window should be 5-20 for most use cases',
      'memory.summaryThreshold': '\n    Tip: Summary threshold should be 2-3x the context window',
      'test.factsCount': '\n    Tip: Start with 5-15 facts for initial testing',
      'test.messagesBetweenFacts': '\n    Tip: 3-10 messages between facts provides good conversation flow'
    };

    return helpTexts[normalizedPath] || '';
  }

  /**
   * Get helpful text for enum validation errors
   * @param {string} path - Configuration path
   * @returns {string} Help text
   */
  getEnumHelpText(path) {
    // Remove leading slash and convert to dot notation
    const normalizedPath = path.replace(/^\//, '').replace(/\//g, '.');
    
    const enumHelp = {
      'memory.type': '\n    Tip: Use "simple" for basic testing, "summary" for longer conversations, "summary_with_knowledge" for advanced memory',
      'environment': '\n    Tip: Use "development" for local testing, "test" for automated tests, "production" for deployment'
    };

    return enumHelp[normalizedPath] || '';
  }

  /**
   * Get a configuration value by path
   * @param {string} path - Dot-separated path to the configuration value
   * @param {*} defaultValue - Default value if path doesn't exist
   * @returns {*} Configuration value
   */
  get(path, defaultValue = undefined) {
    const keys = path.split('.');
    let current = this.config;

    for (const key of keys) {
      if (current === null || current === undefined || !(key in current)) {
        return defaultValue;
      }
      current = current[key];
    }

    return current;
  }

  /**
   * Get model configuration by type
   * @param {string} modelType - Type of model (user, assistant, evaluator, etc.)
   * @returns {Object} Model configuration
   */
  getModelConfig(modelType) {
    const modelConfig = this.get(`models.${modelType}`);
    if (!modelConfig) {
      throw new Error(`Unknown model type: ${modelType}`);
    }
    return modelConfig;
  }

  /**
   * Get API configuration based on available keys
   * @returns {Object} API configuration with the preferred provider
   */
  getApiConfig() {
    const openRouterKey = this.config.api.openrouter.apiKey;
    const openAIKey = this.config.api.openai.apiKey;

    // Prefer OpenRouter if both are available, otherwise use what's available
    if (openRouterKey) {
      return {
        provider: 'openrouter',
        apiKey: openRouterKey,
        baseURL: this.config.api.openrouter.baseURL
      };
    } else if (openAIKey) {
      return {
        provider: 'openai',
        apiKey: openAIKey,
        baseURL: this.config.api.openai.baseURL
      };
    }

    throw new Error('No API key configured. Please set OPENROUTER_API_KEY or OPENAI_API_KEY');
  }

  /**
   * Check if verbose logging is enabled
   * @returns {boolean} True if verbose logging is enabled
   */
  isVerboseLogging() {
    return this.config.output.verboseLogging;
  }

  /**
   * Check if results should be saved
   * @returns {boolean} True if results should be saved
   */
  shouldSaveResults() {
    return this.config.output.saveResults;
  }

  /**
   * Get the complete configuration object (read-only)
   * @returns {Object} Complete configuration
   */
  getAll() {
    return JSON.parse(JSON.stringify(this.config)); // Return a deep copy
  }

  /**
   * Get environment-specific configuration
   * @returns {string} Current environment
   */
  getEnvironment() {
    return this.config.environment;
  }

  /**
   * Check if running in development mode
   * @returns {boolean} True if in development mode
   */
  isDevelopment() {
    return this.config.environment === 'development';
  }

  /**
   * Check if running in test mode
   * @returns {boolean} True if in test mode
   */
  isTest() {
    return this.config.environment === 'test';
  }

  /**
   * Check if running in production mode
   * @returns {boolean} True if in production mode
   */
  isProduction() {
    return this.config.environment === 'production';
  }

  /**
   * Generate configuration documentation
   * @returns {string} Formatted configuration documentation
   */
  generateConfigurationDocs() {
    const docs = [];
    
    docs.push('# Configuration Documentation\n');
    docs.push('This document describes all available configuration options for the LLM Memory Test Application.\n');
    
    // API Configuration
    docs.push('## API Configuration\n');
    docs.push('Configure API access for LLM providers.\n');
    docs.push('| Environment Variable | Default | Description |');
    docs.push('|---------------------|---------|-------------|');
    docs.push('| `OPENROUTER_API_KEY` | - | API key for OpenRouter (preferred) |');
    docs.push('| `OPENAI_API_KEY` | - | API key for OpenAI (alternative) |');
    docs.push('\n**Note:** At least one API key must be provided.\n');
    
    // Model Configuration
    docs.push('## Model Configuration\n');
    docs.push('Configure LLM models for different roles in the application.\n');
    docs.push('| Environment Variable | Default | Range | Description |');
    docs.push('|---------------------|---------|-------|-------------|');
    
    const modelTypes = ['USER', 'ASSISTANT', 'EVALUATOR', 'SUMMARY', 'KNOWLEDGE_EXTRACTION'];
    for (const modelType of modelTypes) {
      const key = modelType.toLowerCase();
      const defaultConfig = defaults.models[key === 'knowledge_extraction' ? 'knowledgeExtraction' : key];
      
      docs.push(`| \`${modelType}_MODEL\` | ${defaultConfig.name} | - | Model name for ${key} role |`);
      docs.push(`| \`${modelType}_TEMPERATURE\` | ${defaultConfig.temperature} | ${constraints.models.temperature.min}-${constraints.models.temperature.max} | Temperature for ${key} model |`);
      docs.push(`| \`${modelType}_MAX_TOKENS\` | ${defaultConfig.maxTokens} | ${constraints.models.maxTokens.min}-${constraints.models.maxTokens.max} | Max tokens for ${key} model |`);
    }
    docs.push('');
    
    // Memory Configuration
    docs.push('## Memory Configuration\n');
    docs.push('Configure memory behavior and thresholds.\n');
    docs.push('| Environment Variable | Default | Range/Options | Description |');
    docs.push('|---------------------|---------|---------------|-------------|');
    docs.push(`| \`MEMORY_TYPE\` | ${defaults.memory.type} | ${memoryTypes.join(', ')} | Type of memory implementation |`);
    docs.push(`| \`MEMORY_CONTEXT_WINDOW\` | ${defaults.memory.contextWindow} | ${constraints.memory.contextWindow.min}-${constraints.memory.contextWindow.max} | Number of messages to keep in context |`);
    docs.push(`| \`SUMMARY_THRESHOLD\` | ${defaults.memory.summaryThreshold} | ${constraints.memory.summaryThreshold.min}-${constraints.memory.summaryThreshold.max} | Messages before summarization |`);
    docs.push(`| \`ENABLE_KNOWLEDGE_EXTRACTION\` | ${defaults.memory.enableKnowledgeExtraction} | true/false | Enable knowledge extraction in summary memory |`);
    docs.push('');
    
    // Test Configuration
    docs.push('## Test Configuration\n');
    docs.push('Configure test execution parameters.\n');
    docs.push('| Environment Variable | Default | Range | Description |');
    docs.push('|---------------------|---------|-------|-------------|');
    docs.push(`| \`TEST_FACTS_FILE\` | ${defaults.test.factsFile} | - | Test facts file name (without .json) |`);
    docs.push(`| \`TEST_FACTS_COUNT\` | ${defaults.test.factsCount} | ${constraints.test.factsCount.min}-${constraints.test.factsCount.max} | Number of facts to test |`);
    docs.push(`| \`MESSAGES_BETWEEN_FACTS\` | ${defaults.test.messagesBetweenFacts} | ${constraints.test.messagesBetweenFacts.min}-${constraints.test.messagesBetweenFacts.max} | Messages between fact introductions |`);
    docs.push('');
    
    // Output Configuration
    docs.push('## Output Configuration\n');
    docs.push('Configure application output and logging.\n');
    docs.push('| Environment Variable | Default | Options | Description |');
    docs.push('|---------------------|---------|---------|-------------|');
    docs.push(`| \`SAVE_RESULTS\` | ${defaults.output.saveResults} | true/false | Save test results to file |`);
    docs.push(`| \`VERBOSE_LOGGING\` | ${defaults.output.verboseLogging} | true/false | Enable detailed logging |`);
    docs.push(`| \`NODE_ENV\` | ${defaults.environment} | ${environments.join(', ')} | Application environment |`);
    docs.push('');
    
    // Memory Type Guide
    docs.push('## Memory Type Guide\n');
    docs.push('- **simple**: Basic memory that keeps recent messages in context');
    docs.push('- **summary**: Summarizes old messages when threshold is reached');
    docs.push('- **summary_with_knowledge**: Summary memory with knowledge extraction');
    docs.push('');
    
    // Best Practices
    docs.push('## Best Practices\n');
    docs.push('- Use higher temperatures (0.7-1.0) for user models to encourage creativity');
    docs.push('- Use lower temperatures (0.1-0.3) for evaluator models for consistency');
    docs.push('- Set summary threshold to 2-3x the context window size');
    docs.push('- Start with 5-15 facts for initial testing');
    docs.push('- Use 3-10 messages between facts for natural conversation flow');
    
    return docs.join('\n');
  }

  /**
   * Validate configuration and provide detailed feedback
   * @returns {Object} Validation result with status and messages
   */
  validateConfigurationWithFeedback() {
    const result = {
      isValid: true,
      errors: [],
      warnings: [],
      suggestions: []
    };

    try {
      this.validateConfiguration();
    } catch (error) {
      result.isValid = false;
      result.errors.push(error.message);
      
      if (error.getFormattedMessage) {
        result.errors.push(error.getFormattedMessage());
      }
      
      return result;
    }

    // Add warnings and suggestions for suboptimal configurations
    this.addConfigurationWarnings(result);
    this.addConfigurationSuggestions(result);

    return result;
  }

  /**
   * Add configuration warnings for suboptimal settings
   * @param {Object} result - Validation result object
   */
  addConfigurationWarnings(result) {
    // Check for potentially problematic temperature settings
    if (this.config.models.evaluator.temperature > 0.5) {
      result.warnings.push('Evaluator temperature is high (>0.5) - this may cause inconsistent scoring');
    }

    if (this.config.models.user.temperature < 0.5) {
      result.warnings.push('User temperature is low (<0.5) - this may reduce conversation creativity');
    }

    // Check memory configuration
    if (this.config.memory.type === 'summary' && this.config.memory.summaryThreshold < this.config.memory.contextWindow * 2) {
      result.warnings.push('Summary threshold is less than 2x context window - may cause frequent summarization');
    }

    // Check test configuration
    if (this.config.test.factsCount > 50) {
      result.warnings.push('High fact count (>50) may result in long test execution times');
    }
  }

  /**
   * Add configuration suggestions for optimization
   * @param {Object} result - Validation result object
   */
  addConfigurationSuggestions(result) {
    // API configuration suggestions
    const hasOpenRouter = this.config.api.openrouter.apiKey;
    const hasOpenAI = this.config.api.openai.apiKey;
    
    if (hasOpenAI && !hasOpenRouter) {
      result.suggestions.push('Consider using OpenRouter for access to more model options');
    }

    // Memory configuration suggestions
    if (this.config.memory.type === 'simple' && this.config.test.factsCount > 20) {
      result.suggestions.push('Consider using summary memory for tests with many facts');
    }

    // Performance suggestions
    if (this.config.models.user.maxTokens > 200 || this.config.models.assistant.maxTokens > 200) {
      result.suggestions.push('Consider reducing max tokens for user/assistant models to improve performance');
    }
  }
}

module.exports = ConfigManager;