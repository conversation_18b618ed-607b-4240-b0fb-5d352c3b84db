/**
 * Default Configuration Values
 * 
 * This module contains all default configuration values used by the ConfigManager.
 * Centralizing defaults makes them easier to maintain and update.
 */

/**
 * Default configuration values
 */
const defaults = {
  // API Configuration
  api: {
    openrouter: {
      baseURL: 'https://openrouter.ai/api/v1'
    },
    openai: {
      baseURL: 'https://api.openai.com/v1'
    }
  },

  // Model Configuration
  models: {
    user: {
      name: 'openai/gpt-3.5-turbo',
      temperature: 0.8,
      maxTokens: 150
    },
    assistant: {
      name: 'openai/gpt-3.5-turbo',
      temperature: 0.7,
      maxTokens: 150
    },
    evaluator: {
      name: 'openai/gpt-3.5-turbo',
      temperature: 0.2,
      maxTokens: 200
    },
    summary: {
      name: 'openai/gpt-3.5-turbo',
      temperature: 0.3,
      maxTokens: 500
    },
    knowledgeExtraction: {
      name: 'openai/gpt-3.5-turbo',
      temperature: 0.3,
      maxTokens: 500
    }
  },

  // Memory Configuration
  memory: {
    type: 'simple',
    contextWindow: 10,
    summaryThreshold: 20,
    enableKnowledgeExtraction: false
  },

  // Test Configuration
  test: {
    factsFile: 'simple',
    factsCount: 10,
    messagesBetweenFacts: 5
  },

  // Output Configuration
  output: {
    saveResults: false,
    verboseLogging: false
  },

  // Environment
  environment: 'development'
};

/**
 * Configuration validation constraints
 */
const constraints = {
  models: {
    temperature: {
      min: 0.0,
      max: 2.0
    },
    maxTokens: {
      min: 1,
      max: 4096
    }
  },
  memory: {
    contextWindow: {
      min: 1,
      max: 100
    },
    summaryThreshold: {
      min: 5,
      max: 200
    }
  },
  test: {
    factsCount: {
      min: 1,
      max: 100
    },
    messagesBetweenFacts: {
      min: 1,
      max: 20
    }
  }
};

/**
 * Supported memory types
 */
const memoryTypes = ['simple', 'summary', 'summary_with_knowledge'];

/**
 * Supported environments
 */
const environments = ['development', 'test', 'production'];

module.exports = {
  defaults,
  constraints,
  memoryTypes,
  environments
};