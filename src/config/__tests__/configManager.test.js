/**
 * Tests for ConfigManager
 */

const ConfigManager = require('../configManager');
const {
  ConfigValidationError,
  MissingApi<PERSON>eyError,
  InvalidModelConfigError,
  InvalidMemoryConfigError
} = require('../configError');

describe('ConfigManager', () => {
  let originalEnv;

  beforeEach(() => {
    // Save original environment
    originalEnv = { ...process.env };
    
    // Clear environment variables
    delete process.env.OPENROUTER_API_KEY;
    delete process.env.OPENAI_API_KEY;
    delete process.env.USER_MODEL;
    delete process.env.MEMORY_TYPE;
    delete process.env.TEST_FACTS_COUNT;
  });

  afterEach(() => {
    // Restore original environment
    process.env = originalEnv;
  });

  describe('constructor', () => {
    it('should throw MissingApiKeyError when no API keys are provided', () => {
      expect(() => new ConfigManager()).toThrow(MissingApiKeyError);
    });

    it('should create instance successfully with OpenRouter API key', () => {
      process.env.OPENROUTER_API_KEY = 'test-key';
      
      const config = new ConfigManager();
      expect(config).toBeInstanceOf(ConfigManager);
      expect(config.getApiConfig().provider).toBe('openrouter');
    });

    it('should create instance successfully with OpenAI API key', () => {
      process.env.OPENAI_API_KEY = 'test-key';
      
      const config = new ConfigManager();
      expect(config).toBeInstanceOf(ConfigManager);
      expect(config.getApiConfig().provider).toBe('openai');
    });
  });

  describe('configuration loading', () => {
    beforeEach(() => {
      process.env.OPENROUTER_API_KEY = 'test-key';
    });

    it('should load default values when environment variables are not set', () => {
      const config = new ConfigManager();
      
      expect(config.get('models.user.name')).toBe('openai/gpt-3.5-turbo');
      expect(config.get('models.user.temperature')).toBe(0.8);
      expect(config.get('memory.type')).toBe('simple');
      expect(config.get('test.factsCount')).toBe(10);
    });

    it('should use environment variables when provided', () => {
      process.env.USER_MODEL = 'custom/model';
      process.env.USER_TEMPERATURE = '0.5';
      process.env.MEMORY_TYPE = 'summary';
      process.env.TEST_FACTS_COUNT = '20';
      
      const config = new ConfigManager();
      
      expect(config.get('models.user.name')).toBe('custom/model');
      expect(config.get('models.user.temperature')).toBe(0.5);
      expect(config.get('memory.type')).toBe('summary');
      expect(config.get('test.factsCount')).toBe(20);
    });

    it('should handle boolean parsing correctly', () => {
      process.env.SAVE_RESULTS = 'true';
      process.env.VERBOSE_LOGGING = 'false';
      process.env.ENABLE_KNOWLEDGE_EXTRACTION = 'true';
      
      const config = new ConfigManager();
      
      expect(config.get('output.saveResults')).toBe(true);
      expect(config.get('output.verboseLogging')).toBe(false);
      expect(config.get('memory.enableKnowledgeExtraction')).toBe(true);
    });
  });

  describe('validation', () => {
    beforeEach(() => {
      process.env.OPENROUTER_API_KEY = 'test-key';
    });

    it('should throw InvalidMemoryConfigError for invalid memory type', () => {
      process.env.MEMORY_TYPE = 'invalid';
      
      expect(() => new ConfigManager()).toThrow(ConfigValidationError);
    });

    it('should throw InvalidMemoryConfigError when summary threshold is not greater than context window', () => {
      process.env.MEMORY_TYPE = 'summary';
      process.env.MEMORY_CONTEXT_WINDOW = '10';
      process.env.SUMMARY_THRESHOLD = '10';
      
      expect(() => new ConfigManager()).toThrow(InvalidMemoryConfigError);
    });

    it('should throw ConfigValidationError for invalid temperature values', () => {
      process.env.USER_TEMPERATURE = '3.0'; // Above maximum
      
      expect(() => new ConfigManager()).toThrow(ConfigValidationError);
    });

    it('should throw ConfigValidationError for invalid token counts', () => {
      process.env.USER_MAX_TOKENS = '0'; // Below minimum
      
      expect(() => new ConfigManager()).toThrow(ConfigValidationError);
    });
  });

  describe('configuration access methods', () => {
    beforeEach(() => {
      process.env.OPENROUTER_API_KEY = 'test-key';
    });

    it('should get configuration values by path', () => {
      const config = new ConfigManager();
      
      expect(config.get('models.user.name')).toBe('openai/gpt-3.5-turbo');
      expect(config.get('memory.type')).toBe('simple');
      expect(config.get('nonexistent.path', 'default')).toBe('default');
    });

    it('should get model configuration by type', () => {
      const config = new ConfigManager();
      
      const userModel = config.getModelConfig('user');
      expect(userModel).toHaveProperty('name');
      expect(userModel).toHaveProperty('temperature');
      expect(userModel).toHaveProperty('maxTokens');
    });

    it('should throw error for unknown model type', () => {
      const config = new ConfigManager();
      
      expect(() => config.getModelConfig('unknown')).toThrow('Unknown model type: unknown');
    });

    it('should provide utility methods for common checks', () => {
      process.env.VERBOSE_LOGGING = 'true';
      process.env.SAVE_RESULTS = 'true';
      process.env.NODE_ENV = 'development';
      
      const config = new ConfigManager();
      
      expect(config.isVerboseLogging()).toBe(true);
      expect(config.shouldSaveResults()).toBe(true);
      expect(config.isDevelopment()).toBe(true);
      expect(config.isTest()).toBe(false);
    });
  });

  describe('enhanced validation and feedback', () => {
    beforeEach(() => {
      process.env.OPENROUTER_API_KEY = 'test-key';
    });

    it('should provide detailed validation feedback for valid configuration', () => {
      const config = new ConfigManager();
      const feedback = config.validateConfigurationWithFeedback();
      
      expect(feedback.isValid).toBe(true);
      expect(feedback.errors).toHaveLength(0);
      expect(Array.isArray(feedback.warnings)).toBe(true);
      expect(Array.isArray(feedback.suggestions)).toBe(true);
    });

    it('should provide detailed validation feedback for invalid configuration', () => {
      process.env.USER_TEMPERATURE = '3.0'; // Invalid temperature
      
      expect(() => new ConfigManager()).toThrow();
      
      // Reset to valid config for feedback test
      process.env.USER_TEMPERATURE = '0.8';
      const config = new ConfigManager();
      
      // Test with invalid config object (simulate validation failure)
      const originalValidate = config.validateConfiguration;
      config.validateConfiguration = () => {
        throw new ConfigValidationError('Test validation error', ['Test error']);
      };
      
      const feedback = config.validateConfigurationWithFeedback();
      
      expect(feedback.isValid).toBe(false);
      expect(feedback.errors.length).toBeGreaterThan(0);
    });

    it('should generate configuration warnings for suboptimal settings', () => {
      process.env.EVALUATOR_TEMPERATURE = '0.8'; // High temperature for evaluator
      process.env.USER_TEMPERATURE = '0.3'; // Low temperature for user
      process.env.TEST_FACTS_COUNT = '60'; // High fact count
      
      const config = new ConfigManager();
      const feedback = config.validateConfigurationWithFeedback();
      
      expect(feedback.warnings).toContain('Evaluator temperature is high (>0.5) - this may cause inconsistent scoring');
      expect(feedback.warnings).toContain('User temperature is low (<0.5) - this may reduce conversation creativity');
      expect(feedback.warnings).toContain('High fact count (>50) may result in long test execution times');
    });

    it('should generate configuration suggestions for optimization', () => {
      process.env.OPENAI_API_KEY = 'test-openai-key';
      delete process.env.OPENROUTER_API_KEY;
      process.env.MEMORY_TYPE = 'simple';
      process.env.TEST_FACTS_COUNT = '25';
      process.env.USER_MAX_TOKENS = '300';
      
      const config = new ConfigManager();
      const feedback = config.validateConfigurationWithFeedback();
      
      expect(feedback.suggestions).toContain('Consider using OpenRouter for access to more model options');
      expect(feedback.suggestions).toContain('Consider using summary memory for tests with many facts');
      expect(feedback.suggestions).toContain('Consider reducing max tokens for user/assistant models to improve performance');
    });
  });

  describe('configuration documentation generation', () => {
    beforeEach(() => {
      process.env.OPENROUTER_API_KEY = 'test-key';
    });

    it('should generate comprehensive configuration documentation', () => {
      const config = new ConfigManager();
      const docs = config.generateConfigurationDocs();
      
      expect(docs).toContain('# Configuration Documentation');
      expect(docs).toContain('## API Configuration');
      expect(docs).toContain('## Model Configuration');
      expect(docs).toContain('## Memory Configuration');
      expect(docs).toContain('## Test Configuration');
      expect(docs).toContain('## Output Configuration');
      expect(docs).toContain('## Memory Type Guide');
      expect(docs).toContain('## Best Practices');
      
      // Check for specific environment variables
      expect(docs).toContain('OPENROUTER_API_KEY');
      expect(docs).toContain('USER_MODEL');
      expect(docs).toContain('MEMORY_TYPE');
      expect(docs).toContain('TEST_FACTS_COUNT');
      expect(docs).toContain('SAVE_RESULTS');
    });

    it('should include default values and constraints in documentation', () => {
      const config = new ConfigManager();
      const docs = config.generateConfigurationDocs();
      
      expect(docs).toContain('openai/gpt-3.5-turbo'); // Default model
      expect(docs).toContain('simple'); // Default memory type
      expect(docs).toContain('0-2'); // Temperature range
      expect(docs).toContain('1-4096'); // Max tokens range
    });
  });

  describe('enhanced error formatting', () => {
    beforeEach(() => {
      process.env.OPENROUTER_API_KEY = 'test-key';
    });

    it('should provide helpful validation error messages', () => {
      process.env.USER_TEMPERATURE = '3.0'; // Above maximum
      
      try {
        new ConfigManager();
        fail('Should have thrown validation error');
      } catch (error) {
        const formattedMessage = error.getFormattedMessage();
        expect(formattedMessage).toContain('must be <= 2');
        expect(formattedMessage).toContain('User model temperature should be higher');
      }
    });

    it('should provide helpful enum error messages', () => {
      process.env.MEMORY_TYPE = 'invalid_type';
      
      try {
        new ConfigManager();
        fail('Should have thrown validation error');
      } catch (error) {
        const formattedMessage = error.getFormattedMessage();
        expect(formattedMessage).toContain('must be equal to one of the allowed values');
        expect(formattedMessage).toContain('Use "simple" for basic testing');
      }
    });
  });
});