/**
 * Data Loader - Test Facts Management and Loading System
 *
 * This module provides comprehensive data loading capabilities for the LLM Memory
 * Test Application. It handles multiple test fact scenarios, supports various data
 * formats, provides data validation, and offers advanced features like merged
 * random fact generation with complexity balancing.
 *
 * Key Features:
 * - Multiple test scenario support (customer, simple, casual, etc.)
 * - JSON file format validation and error handling
 * - Merged random fact generation with complexity alternation
 * - Automatic fallback to default datasets
 * - Comprehensive logging and error reporting
 * - Scenario discovery and enumeration
 * - Data integrity validation
 * - Configurable fact count limiting
 *
 * Supported Data Formats:
 * - Standard JSON arrays with fact objects
 * - Facts with complexity indicators (simple/complex)
 * - Structured fact objects with id, fact, question, answer fields
 * - Extensible schema for additional metadata
 *
 * @fileoverview Test facts data loading and management utilities
 * @module dataLoader
 * 
 * @example
 * // Load facts from specific scenario
 * const facts = await loadTestFacts(10, 'customer');
 * console.log(`Loaded ${facts.length} facts`);
 * 
 * @example
 * // Get all available scenarios
 * const scenarios = await getAllTestFactScenarios();
 * console.log('Available scenarios:', scenarios);
 * 
 * @example
 * // Create merged random facts
 * const mergedFacts = await createMergedRandomFacts(20);
 * console.log('Created mixed complexity facts:', mergedFacts.length);
 */

const fs = require('fs').promises;
const path = require('path');
const { defaultLogger } = require('./logger');
const { DataIntegrityChecker } = require('../validation/dataIntegrityChecker');
const { DataMigrationManager } = require('../validation/dataMigration');

/**
 * Load and parse all test facts JSON files from the data directory
 * 
 * This function discovers and loads all test fact files in the data directory,
 * parsing each JSON file and organizing them by scenario name. It provides
 * comprehensive error handling for individual file parsing failures while
 * continuing to process other files.
 * 
 * @returns {Promise<Object>} Object mapping scenario names to fact arrays
 * @returns {Array<Object>} returns[scenarioName] - Array of fact objects for each scenario
 * @throws {Error} Throws if data directory cannot be accessed
 * @throws {Error} Throws if no valid fact files are found
 * 
 * @example
 * const allFacts = await loadAllTestFactsFiles();
 * console.log('Available scenarios:', Object.keys(allFacts));
 * console.log('Customer facts:', allFacts.customer.length);
 * 
 * @example
 * // Process all scenarios
 * const factFiles = await loadAllTestFactsFiles();
 * Object.entries(factFiles).forEach(([scenario, facts]) => {
 *   console.log(`${scenario}: ${facts.length} facts`);
 * });
 */
async function loadAllTestFactsFiles() {
  try {
    const dataDir = path.join(__dirname, '../../data');
    const files = await fs.readdir(dataDir);

    // Filter for test_facts JSON files
    const factFiles = files.filter(
      file => file.startsWith('test_facts_') && file.endsWith('.json')
    );

    const allFacts = {};

    // Load each file
    for (const file of factFiles) {
      const filePath = path.join(dataDir, file);
      const fileData = await fs.readFile(filePath, 'utf8');
      try {
        const parsedData = JSON.parse(fileData);
        // Use the file name without extension as the key
        const key = file.replace('test_facts_', '').replace('.json', '');
        allFacts[key] = parsedData;
      } catch (parseError) {
        defaultLogger.warn('Error parsing test facts file', {
          file,
          error: parseError.message
        });
      }
    }

    return allFacts;
  } catch (error) {
    defaultLogger.error('Error loading test facts files', {
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
}

/**
 * Discover and enumerate all available test fact scenarios
 * 
 * This function scans the data directory to identify all available test fact
 * scenarios by examining file names. It extracts scenario names by removing
 * the standard prefix and extension, providing a clean list of available
 * test scenarios for dynamic scenario selection.
 * 
 * @returns {Promise<Array<string>>} Array of scenario names (e.g., ['customer', 'simple', 'casual'])
 * @throws {Error} Throws if data directory cannot be accessed
 * 
 * @example
 * const scenarios = await getAllTestFactScenarios();
 * console.log('Available scenarios:', scenarios);
 * // Output: ['customer', 'simple', 'casual']
 * 
 * @example
 * // Use for dynamic scenario selection
 * const scenarios = await getAllTestFactScenarios();
 * for (const scenario of scenarios) {
 *   const facts = await loadTestFacts(5, scenario);
 *   console.log(`${scenario}: ${facts.length} facts loaded`);
 * }
 */
async function getAllTestFactScenarios() {
  try {
    const dataDir = path.join(__dirname, '../../data');
    const files = await fs.readdir(dataDir);

    // Filter for test_facts JSON files and extract scenario names
    const scenarios = files
      .filter(file => file.startsWith('test_facts_') && file.endsWith('.json'))
      .map(file => file.replace('test_facts_', '').replace('.json', ''));

    return scenarios;
  } catch (error) {
    defaultLogger.error('Error getting test fact scenarios', {
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
}

/**
 * Create merged and randomized facts with alternating complexity pattern
 * 
 * This function combines facts from all available scenario files and creates
 * a balanced dataset with alternating simple and complex facts. It ensures
 * proper complexity distribution for comprehensive memory testing while
 * maintaining randomization within each complexity category.
 * 
 * Process:
 * 1. Load all available test fact files
 * 2. Categorize facts by complexity (simple/complex)
 * 3. Shuffle facts within each complexity category
 * 4. Create alternating pattern (simple at odd positions, complex at even)
 * 5. Reassign sequential IDs for consistency
 * 6. Validate complexity pattern and log warnings
 * 
 * @param {number} count - Number of facts to include in the merged dataset
 * @returns {Promise<Array<Object>>} Array of merged and randomized facts with alternating complexity
 * @returns {number} returns[].id - Sequential fact identifier
 * @returns {string} returns[].fact - Fact statement
 * @returns {string} returns[].question - Question to test fact retention
 * @returns {string} returns[].answer - Expected answer
 * @returns {string} returns[].complexity - Fact complexity ('simple' or 'complex')
 * @throws {Error} Throws if no fact files are available
 * @throws {Error} Throws if insufficient facts for requested count
 * 
 * @example
 * const mergedFacts = await createMergedRandomFacts(10);
 * console.log(`Created ${mergedFacts.length} facts`);
 * mergedFacts.forEach((fact, index) => {
 *   const expectedComplexity = index % 2 === 0 ? 'simple' : 'complex';
 *   console.log(`Position ${index + 1}: ${fact.complexity} (expected: ${expectedComplexity})`);
 * });
 * 
 * @example
 * // Use for balanced testing
 * const facts = await createMergedRandomFacts(20);
 * const simpleFacts = facts.filter(f => f.complexity === 'simple');
 * const complexFacts = facts.filter(f => f.complexity === 'complex');
 * console.log(`Simple: ${simpleFacts.length}, Complex: ${complexFacts.length}`);
 */
async function createMergedRandomFacts(count) {
  try {
    defaultLogger.info('Creating merged random facts with alternating complexity', {
      requestedCount: count
    });

    // Load all test facts files
    const allFactsFiles = await loadAllTestFactsFiles();

    // Merge all facts into two arrays: simple and complex
    const simpleFacts = [];
    const complexFacts = [];

    // Flatten all facts from all files into a single array
    const allFacts = [];
    Object.values(allFactsFiles).forEach(factsArray => {
      allFacts.push(...factsArray);
    });

    // Categorize facts by complexity
    allFacts.forEach(fact => {
      // If complexity is not specified, determine it based on fact length
      // (shorter facts are generally simpler)
      if (!fact.complexity) {
        fact.complexity = fact.fact.length < 50 ? 'simple' : 'complex';
      }

      if (fact.complexity === 'simple') {
        simpleFacts.push(fact);
      } else if (fact.complexity === 'complex') {
        complexFacts.push(fact);
      }
    });

    defaultLogger.info('Categorized facts by complexity', {
      simpleFacts: simpleFacts.length,
      complexFacts: complexFacts.length,
      totalFacts: allFacts.length
    });

    // Shuffle both arrays
    const shuffleArray = arr => {
      for (let i = arr.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [arr[i], arr[j]] = [arr[j], arr[i]];
      }
      return arr;
    };

    const shuffledSimple = shuffleArray([...simpleFacts]);
    const shuffledComplex = shuffleArray([...complexFacts]);

    // Create alternating array with simple facts at odd positions and complex at even
    const mergedFacts = [];
    const pairsNeeded = Math.ceil(count / 2);

    for (let i = 0; i < pairsNeeded; i++) {
      // Add simple fact (odd position)
      if (i < shuffledSimple.length && mergedFacts.length < count) {
        mergedFacts.push({
          ...shuffledSimple[i],
          id: mergedFacts.length + 1 // Reassign IDs to be sequential
        });
      }

      // Add complex fact (even position)
      if (i < shuffledComplex.length && mergedFacts.length < count) {
        mergedFacts.push({
          ...shuffledComplex[i],
          id: mergedFacts.length + 1 // Reassign IDs to be sequential
        });
      }
    }

    defaultLogger.info('Created merged facts with alternating complexity', {
      createdCount: mergedFacts.length,
      requestedCount: count
    });

    // Verify alternating complexity
    let complexityWarnings = 0;
    for (let i = 0; i < mergedFacts.length; i++) {
      const expectedComplexity = i % 2 === 0 ? 'simple' : 'complex';
      if (mergedFacts[i].complexity !== expectedComplexity) {
        complexityWarnings++;
        defaultLogger.warn('Fact complexity mismatch', {
          position: i + 1,
          actualComplexity: mergedFacts[i].complexity,
          expectedComplexity,
          factId: mergedFacts[i].id
        });
      }
    }

    if (complexityWarnings === 0) {
      defaultLogger.debug('All facts have correct alternating complexity pattern');
    }

    return mergedFacts;
  } catch (error) {
    defaultLogger.error('Error creating merged random facts', {
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
}

/**
 * Load test facts from specified scenario with comprehensive error handling
 * 
 * This is the primary function for loading test facts in the application. It supports
 * multiple loading modes including specific scenarios, merged random facts, and
 * automatic fallback to default datasets. The function integrates with the
 * configuration system and provides robust error handling.
 * 
 * Loading Modes:
 * - Specific scenario: Load facts from named scenario file
 * - Merged random: Create balanced dataset from all scenarios
 * - Default fallback: Use default dataset if specified scenario not found
 * - Configuration integration: Use ConfigManager settings if no scenario specified
 * 
 * @param {number} count - Maximum number of facts to load (will be limited by available facts)
 * @param {string} [scenario] - Optional scenario name to override configuration
 * @param {ConfigManager} [configManager] - Optional ConfigManager instance for configuration access
 * @returns {Promise<Array<Object>>} Array of test fact objects
 * @returns {number|string} returns[].id - Fact identifier
 * @returns {string} returns[].fact - Fact statement to inject in conversation
 * @returns {string} returns[].question - Question to test fact retention
 * @returns {string} returns[].answer - Expected answer for evaluation
 * @returns {string} [returns[].complexity] - Fact complexity level ('simple' or 'complex')
 * @throws {Error} Throws if no valid fact files can be found
 * @throws {Error} Throws if JSON parsing fails
 * @throws {Error} Throws if file system access fails
 * 
 * @example
 * // Load facts from specific scenario
 * const facts = await loadTestFacts(10, 'customer');
 * console.log(`Loaded ${facts.length} customer facts`);
 * 
 * @example
 * // Load with configuration
 * const config = new ConfigManager();
 * const facts = await loadTestFacts(5, null, config);
 * 
 * @example
 * // Load merged random facts
 * const facts = await loadTestFacts(20, 'merged_random');
 * console.log('Mixed complexity facts:', facts.map(f => f.complexity));
 * 
 * @example
 * // Handle loading with fallback
 * try {
 *   const facts = await loadTestFacts(10, 'nonexistent');
 * } catch (error) {
 *   console.log('Will fallback to default facts');
 * }
 */
async function loadTestFacts(count, scenario, configManager = null) {
  try {
    // Import ConfigManager only when needed to avoid circular dependencies
    const { ConfigManager } = require('../config');
    const config = configManager || new ConfigManager();
    
    // Get the test facts file type from configuration or use the provided scenario
    const testFactsFile = scenario || config.get('test.factsFile') || 'casual';

    // Check if we should use merged_random mode
    if (testFactsFile === 'merged_random') {
      return await createMergedRandomFacts(count);
    }

    // The 'all' mode is handled by the main application, not here
    // This function just loads facts from a specific file

    defaultLogger.info('Loading test facts from scenario', {
      scenario: testFactsFile,
      requestedCount: count
    });

    // Construct the file path based on the selected test facts file
    const factsPath = path.join(
      __dirname,
      `../../data/test_facts_${testFactsFile}.json`
    );

    // If the file doesn't exist, fall back to the default test_facts.json
    let finalPath = factsPath;
    try {
      await fs.access(factsPath);
    } catch (e) {
      defaultLogger.warn('Test facts file not found, falling back to default', {
        requestedPath: factsPath,
        fallbackPath: 'test_facts.json'
      });
      finalPath = path.join(__dirname, '../../data/test_facts.json');
    }

    // Initialize migration manager for schema evolution support
    const migrationManager = new DataMigrationManager();
    
    // Read and parse the file first
    const fileContent = await fs.readFile(finalPath, 'utf8');
    let parsedData;
    try {
      parsedData = JSON.parse(fileContent);
    } catch (parseError) {
      defaultLogger.error('Failed to parse test facts file as JSON', {
        filePath: finalPath,
        error: parseError.message
      });
      throw new Error(`Invalid JSON in test facts file: ${parseError.message}`);
    }

    // Check if data migration is needed
    const currentVersion = migrationManager.detectSchemaVersion('testFacts', parsedData);
    const latestVersion = migrationManager.getLatestVersion('testFacts');
    
    let migratedData = parsedData;
    if (currentVersion !== latestVersion) {
      defaultLogger.info('Test facts file requires schema migration', {
        filePath: finalPath,
        currentVersion,
        latestVersion
      });

      try {
        const migrationResult = migrationManager.migrateToLatest('testFacts', parsedData);
        migratedData = migrationResult.data;
        
        defaultLogger.info('Test facts schema migration completed', {
          filePath: finalPath,
          fromVersion: migrationResult.fromVersion,
          toVersion: migrationResult.toVersion,
          migrationsApplied: migrationResult.migrationsApplied
        });

        // Optionally save the migrated data back to file (disabled by default to avoid modifying source files)
        const shouldAutoSaveMigrations = config.get('data.autoSaveMigrations', false);
        if (shouldAutoSaveMigrations) {
          const migratedContent = JSON.stringify(migratedData, null, 2);
          await fs.writeFile(finalPath, migratedContent);
          defaultLogger.info('Migrated test facts saved to file', { filePath: finalPath });
        }
      } catch (migrationError) {
        defaultLogger.error('Test facts schema migration failed', {
          filePath: finalPath,
          error: migrationError.message,
          currentVersion,
          latestVersion
        });
        // Continue with original data if migration fails
        defaultLogger.warn('Continuing with original data format', { filePath: finalPath });
      }
    } else {
      defaultLogger.debug('Test facts file is already at latest schema version', {
        filePath: finalPath,
        version: currentVersion
      });
    }

    // Perform data integrity validation on the (potentially migrated) test facts data
    const integrityChecker = new DataIntegrityChecker();
    
    defaultLogger.debug('Validating test facts file integrity', {
      filePath: finalPath,
      schemaVersion: currentVersion !== latestVersion ? `${currentVersion} → ${latestVersion}` : currentVersion
    });

    const integrityResult = await integrityChecker.validateTestFactsFileIntegrity(finalPath);
    
    if (!integrityResult.isValid) {
      defaultLogger.error('Test facts file failed integrity validation', {
        filePath: finalPath,
        issues: integrityResult.issues || []
      });
      throw new Error(`Test facts file integrity validation failed: ${integrityResult.issues?.join(', ') || 'Unknown issues'}`);
    }

    // Log warnings if any
    if (integrityResult.warnings && integrityResult.warnings.length > 0) {
      defaultLogger.warn('Test facts file integrity warnings', {
        filePath: finalPath,
        warnings: integrityResult.warnings
      });
    }

    // Log integrity check summary
    if (integrityResult.integrityChecks) {
      const checksStatus = Object.entries(integrityResult.integrityChecks).map(([check, result]) => ({
        check,
        valid: result.isValid,
        issues: result.issues?.length || 0
      }));
      
      defaultLogger.debug('Test facts integrity checks completed', {
        filePath: finalPath,
        checks: checksStatus
      });
    }

    // Use migrated data if available, otherwise use the data from integrity validation
    const facts = migratedData;

    // Return the requested number of facts or all if count is greater than available facts
    return facts.slice(0, Math.min(count, facts.length));
  } catch (error) {
    defaultLogger.error('Error loading test facts', {
      error: error.message,
      stack: error.stack,
      scenario: scenario || 'default'
    });
    throw error;
  }
}

module.exports = {
  loadTestFacts,
  loadAllTestFactsFiles,
  getAllTestFactScenarios,
  createMergedRandomFacts
};
