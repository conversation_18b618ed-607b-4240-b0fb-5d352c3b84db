/**
 * Comprehensive tests for Evaluator
 * Tests cover memory evaluation, scoring logic, and result generation
 */

const Evaluator = require('../evaluator');
const fs = require('fs').promises;

// Mock all dependencies
jest.mock('../../models/llmModel', () => {
  return jest.fn();
});

jest.mock('../prompts', () => ({
  prompts: {
    EVALUATOR_PROMPT: 'You are an objective evaluator',
    SCORING_PROMPT: 'Score the response from 0-10'
  }
}));

jest.mock('../../config', () => ({
  ConfigManager: jest.fn()
}));

jest.mock('../logger', () => ({
  defaultLogger: {
    child: jest.fn(() => ({
      info: jest.fn(),
      debug: jest.fn(),
      warn: jest.fn(),
      error: jest.fn()
    })),
    info: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
  }
}));

jest.mock('fs', () => ({
  promises: {
    writeFile: jest.fn()
  }
}));

const LLMModel = require('../../models/llmModel');
const { ConfigManager } = require('../../config');

describe('Evaluator', () => {
  let mockConfig;
  let mockEvaluatorModel;
  let evaluator;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Mock configuration
    mockConfig = {
      get: jest.fn((key) => {
        const config = {
          'models.evaluator.name': 'gpt-4'
        };
        return config[key];
      })
    };

    // Mock LLM model
    mockEvaluatorModel = {
      generateResponse: jest.fn(),
      setPerformanceTracker: jest.fn()
    };

    ConfigManager.mockImplementation(() => mockConfig);
    LLMModel.mockImplementation(() => mockEvaluatorModel);

    // Mock console.log to avoid noise in tests
    jest.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    // Restore console.log
    jest.restoreAllMocks();
  });

  describe('Constructor', () => {
    test('should create Evaluator with valid sessionId', () => {
      evaluator = new Evaluator('session-123');

      expect(evaluator.sessionId).toBe('session-123');
      expect(LLMModel).toHaveBeenCalledWith('gpt-4', mockConfig);
      expect(evaluator.performanceTracker).toBeNull();
    });

    test('should throw error with invalid sessionId', () => {
      expect(() => {
        new Evaluator('');
      }).toThrow('Session ID must be a non-empty string');

      expect(() => {
        new Evaluator(null);
      }).toThrow('Session ID must be a non-empty string');
    });

    test('should use custom config when provided', () => {
      const customConfig = {
        get: jest.fn().mockReturnValue('custom-evaluator-model')
      };

      evaluator = new Evaluator('session-123', customConfig);

      expect(evaluator.config).toBe(customConfig);
      expect(LLMModel).toHaveBeenCalledWith('custom-evaluator-model', customConfig);
    });
  });

  describe('setPerformanceTracker', () => {
    beforeEach(() => {
      evaluator = new Evaluator('session-123');
    });

    test('should set performance tracker and pass to model', () => {
      const mockTracker = { track: jest.fn() };

      evaluator.setPerformanceTracker(mockTracker);

      expect(evaluator.performanceTracker).toBe(mockTracker);
      expect(mockEvaluatorModel.setPerformanceTracker).toHaveBeenCalledWith(mockTracker);
    });

    test('should handle model without setPerformanceTracker method', () => {
      delete mockEvaluatorModel.setPerformanceTracker;

      const mockTracker = { track: jest.fn() };

      expect(() => {
        evaluator.setPerformanceTracker(mockTracker);
      }).not.toThrow();

      expect(evaluator.performanceTracker).toBe(mockTracker);
    });
  });

  describe('scoreResponse', () => {
    beforeEach(() => {
      evaluator = new Evaluator('session-123');
    });

    test('should score perfect match as high score', async () => {
      mockEvaluatorModel.generateResponse.mockResolvedValue('Score: 10');

      const score = await evaluator.scoreResponse('Paris', 'Paris');

      expect(score).toBe(10);
      expect(mockEvaluatorModel.generateResponse).toHaveBeenCalledWith(
        'Score the response from 0-10',
        expect.arrayContaining([
          expect.objectContaining({
            content: expect.stringContaining('Expected answer: "Paris"')
          })
        ])
      );
    });

    test('should score incorrect answer as low score', async () => {
      mockEvaluatorModel.generateResponse.mockResolvedValue('Score: 2');

      const score = await evaluator.scoreResponse('London', 'Paris');

      expect(score).toBe(2);
    });

    test('should extract numeric score from various response formats', async () => {
      const testCases = [
        { response: '8', expected: 8 },
        { response: 'Score: 7', expected: 7 },
        { response: 'The score is 9 out of 10', expected: 9 },
        { response: 'Rating: 5/10', expected: 5 },
        { response: '10 points', expected: 10 }
      ];

      for (const testCase of testCases) {
        mockEvaluatorModel.generateResponse.mockResolvedValue(testCase.response);
        const score = await evaluator.scoreResponse('test', 'test');
        expect(score).toBe(testCase.expected);
      }
    });

    test('should clamp scores to 0-10 range', async () => {
      mockEvaluatorModel.generateResponse.mockResolvedValue('Score: 15');
      let score = await evaluator.scoreResponse('test', 'test');
      expect(score).toBe(10);

      mockEvaluatorModel.generateResponse.mockResolvedValue('Score: -5');
      score = await evaluator.scoreResponse('test', 'test');
      expect(score).toBe(0);
    });

    test('should default to 0 if no numeric score found', async () => {
      mockEvaluatorModel.generateResponse.mockResolvedValue('No score available');

      const score = await evaluator.scoreResponse('test', 'test');

      expect(score).toBe(0);
    });

    test('should handle API errors', async () => {
      mockEvaluatorModel.generateResponse.mockRejectedValue(new Error('API error'));

      await expect(evaluator.scoreResponse('test', 'test')).rejects.toThrow('API error');
    });
  });

  describe('evaluateMemory', () => {
    let mockMemory;
    let facts;
    let conversationLog;

    beforeEach(() => {
      evaluator = new Evaluator('session-123');

      mockMemory = {
        getMemoryContext: jest.fn().mockResolvedValue('Memory context for evaluation')
      };

      facts = [
        {
          id: 1,
          fact: 'Paris is the capital of France',
          question: 'What is the capital of France?',
          answer: 'Paris',
          complexity: 'simple'
        },
        {
          id: 2,
          fact: 'Quantum mechanics describes subatomic behavior',
          question: 'What does quantum mechanics describe?',
          answer: 'Subatomic behavior',
          complexity: 'complex'
        }
      ];

      conversationLog = [
        { role: 'user', content: 'Paris is the capital of France' },
        { role: 'assistant', content: 'Thank you for that information!' }
      ];

      mockEvaluatorModel.generateResponse
        .mockResolvedValueOnce('Paris') // Response to first question
        .mockResolvedValueOnce('8') // Score for first response
        .mockResolvedValueOnce('Subatomic particles') // Response to second question
        .mockResolvedValueOnce('7'); // Score for second response
    });

    test('should evaluate all facts and return comprehensive results', async () => {
      const results = await evaluator.evaluateMemory(mockMemory, facts, conversationLog);

      expect(results).toHaveLength(2);

      expect(results[0]).toEqual(expect.objectContaining({
        factId: 1,
        fact: 'Paris is the capital of France',
        question: 'What is the capital of France?',
        expectedAnswer: 'Paris',
        actualResponse: 'Paris',
        score: 8,
        complexity: 'simple',
        memoryContext: 'Memory context for evaluation'
      }));

      expect(results[1]).toEqual(expect.objectContaining({
        factId: 2,
        fact: 'Quantum mechanics describes subatomic behavior',
        question: 'What does quantum mechanics describe?',
        expectedAnswer: 'Subatomic behavior',
        actualResponse: 'Subatomic particles',
        score: 7,
        complexity: 'complex',
        memoryContext: 'Memory context for evaluation'
      }));
    });

    test('should call memory.getMemoryContext for each fact', async () => {
      await evaluator.evaluateMemory(mockMemory, facts, conversationLog);

      expect(mockMemory.getMemoryContext).toHaveBeenCalledTimes(2);
      expect(mockMemory.getMemoryContext).toHaveBeenCalledWith(
        'What is the capital of France?',
        { includeRelevantHistory: true }
      );
    });

    test('should handle facts without complexity field', async () => {
      const factsWithoutComplexity = [
        {
          id: 1,
          fact: 'Test fact',
          question: 'Test question?',
          answer: 'Test answer'
          // No complexity field
        }
      ];

      mockEvaluatorModel.generateResponse
        .mockResolvedValueOnce('Test response')
        .mockResolvedValueOnce('5');

      const results = await evaluator.evaluateMemory(mockMemory, factsWithoutComplexity, conversationLog);

      expect(results[0].complexity).toBe('simple'); // Default value
    });

    test('should handle evaluation errors gracefully', async () => {
      mockMemory.getMemoryContext.mockRejectedValueOnce(new Error('Memory error'));

      const results = await evaluator.evaluateMemory(mockMemory, [facts[0]], conversationLog);

      expect(results).toHaveLength(1);
      expect(results[0]).toEqual(expect.objectContaining({
        factId: 1,
        actualResponse: 'Error: Could not evaluate',
        score: 0,
        complexity: 'simple'
      }));
    });

    test('should integrate with performance tracker', async () => {
      const mockTracker = {
        startOperation: jest.fn().mockReturnValue('operation-id'),
        endOperation: jest.fn()
      };
      evaluator.setPerformanceTracker(mockTracker);

      await evaluator.evaluateMemory(mockMemory, [facts[0]], conversationLog);

      expect(mockTracker.startOperation).toHaveBeenCalledWith('fact-evaluation', expect.any(Object));
      expect(mockTracker.endOperation).toHaveBeenCalledWith('operation-id', expect.any(Object));
    });

    test('should handle empty facts array', async () => {
      const results = await evaluator.evaluateMemory(mockMemory, [], conversationLog);

      expect(results).toHaveLength(0);
    });
  });

  describe('calculateOverallScore', () => {
    beforeEach(() => {
      evaluator = new Evaluator('session-123');
    });

    test('should calculate scores for mixed complexity results', () => {
      const results = [
        { score: 8, complexity: 'simple' },
        { score: 6, complexity: 'complex' },
        { score: 10, complexity: 'simple' },
        { score: 4, complexity: 'complex' }
      ];

      const scores = evaluator.calculateOverallScore(results);

      expect(scores.overall).toBe(70); // (8+6+10+4)/40 * 100 = 70%
      expect(scores.simple).toBe(90); // (8+10)/20 * 100 = 90%
      expect(scores.complex).toBe(50); // (6+4)/20 * 100 = 50%
    });

    test('should handle only simple facts', () => {
      const results = [
        { score: 8, complexity: 'simple' },
        { score: 6, complexity: 'simple' }
      ];

      const scores = evaluator.calculateOverallScore(results);

      expect(scores.overall).toBe(70); // (8+6)/20 * 100 = 70%
      expect(scores.simple).toBe(70);
      expect(scores.complex).toBe(0); // No complex facts
    });

    test('should handle only complex facts', () => {
      const results = [
        { score: 7, complexity: 'complex' },
        { score: 9, complexity: 'complex' }
      ];

      const scores = evaluator.calculateOverallScore(results);

      expect(scores.overall).toBe(80); // (7+9)/20 * 100 = 80%
      expect(scores.simple).toBe(0); // No simple facts
      expect(scores.complex).toBe(80);
    });

    test('should handle empty results', () => {
      const scores = evaluator.calculateOverallScore([]);

      expect(scores.overall).toBe(0);
      expect(scores.simple).toBe(0);
      expect(scores.complex).toBe(0);
    });

    test('should handle perfect scores', () => {
      const results = [
        { score: 10, complexity: 'simple' },
        { score: 10, complexity: 'complex' }
      ];

      const scores = evaluator.calculateOverallScore(results);

      expect(scores.overall).toBe(100);
      expect(scores.simple).toBe(100);
      expect(scores.complex).toBe(100);
    });

    test('should handle zero scores', () => {
      const results = [
        { score: 0, complexity: 'simple' },
        { score: 0, complexity: 'complex' }
      ];

      const scores = evaluator.calculateOverallScore(results);

      expect(scores.overall).toBe(0);
      expect(scores.simple).toBe(0);
      expect(scores.complex).toBe(0);
    });

    test('should round scores to 2 decimal places', () => {
      const results = [
        { score: 3, complexity: 'simple' }, // 3/10 = 30%
        { score: 7, complexity: 'simple' }, // 7/10 = 70%
        { score: 5, complexity: 'simple' }  // 5/10 = 50%
      ];

      const scores = evaluator.calculateOverallScore(results);

      expect(scores.overall).toBe(50); // (3+7+5)/30 * 100 = 50%
      expect(scores.simple).toBe(50);
    });
  });

  describe('Progress Tracking', () => {
    beforeEach(() => {
      evaluator = new Evaluator('session-123');
    });

    describe('createProgressBar', () => {
      test('should create progress bar for different percentages', () => {
        expect(evaluator.createProgressBar(0)).toBe('[░░░░░░░░░░░░░░░░░░░░]   0%');
        expect(evaluator.createProgressBar(50)).toBe('[██████████░░░░░░░░░░]  50%');
        expect(evaluator.createProgressBar(100)).toBe('[████████████████████] 100%');
      });

      test('should handle edge cases', () => {
        expect(evaluator.createProgressBar(1)).toBe('[░░░░░░░░░░░░░░░░░░░░]   1%');
        expect(evaluator.createProgressBar(99)).toBe('[███████████████████░]  99%');
      });
    });

    describe('createPercentageBar', () => {
      test('should create percentage bars for visualization', () => {
        expect(evaluator.createPercentageBar(0)).toBe('[                    ]');
        expect(evaluator.createPercentageBar(50)).toBe('[##########          ]');
        expect(evaluator.createPercentageBar(100)).toBe('[####################]');
      });

      test('should clamp values to 0-100 range', () => {
        expect(evaluator.createPercentageBar(-10)).toBe('[                    ]');
        expect(evaluator.createPercentageBar(150)).toBe('[####################]');
      });
    });

    describe('formatPercentage', () => {
      test('should format percentages with leading zeros', () => {
        expect(evaluator.formatPercentage(0)).toBe('000%');
        expect(evaluator.formatPercentage(5)).toBe('005%');
        expect(evaluator.formatPercentage(50)).toBe('050%');
        expect(evaluator.formatPercentage(100)).toBe('100%');
      });

      test('should clamp values to 0-100 range', () => {
        expect(evaluator.formatPercentage(-10)).toBe('000%');
        expect(evaluator.formatPercentage(150)).toBe('100%');
      });
    });
  });

  describe('saveResultsToFile', () => {
    beforeEach(() => {
      evaluator = new Evaluator('session-123');
    });

    test('should save comprehensive results to file', async () => {
      const testData = {
        sessionId: 'session-123',
        memoryType: 'simple',
        testFactsFile: 'customer',
        testFactsCount: 2,
        messagesBetweenFacts: 3,
        userModel: 'gpt-3.5-turbo',
        assistantModel: 'gpt-4',
        evaluatorModel: 'gpt-4',
        summaryModel: 'gpt-3.5-turbo',
        knowledgeExtractionModel: 'gpt-3.5-turbo',
        overallScore: { overall: 75.5, simple: 80.0, complex: 70.0 },
        evaluationResults: [
          {
            factId: 1,
            fact: 'Paris is the capital of France',
            question: 'What is the capital of France?',
            expectedAnswer: 'Paris',
            actualResponse: 'Paris',
            score: 8,
            complexity: 'simple',
            memoryContext: 'Previous conversation context'
          }
        ],
        conversationLog: [
          { role: 'user', content: 'Paris is the capital of France' },
          { role: 'assistant', content: 'Thank you for that information!' }
        ],
        memoryContexts: ['Context 1', 'Context 2'],
        performanceMetrics: {
          session: { totalDuration: 5000 },
          operations: { completed: 10, total: 10, successRate: 100, averageDuration: 500 }
        }
      };

      await evaluator.saveResultsToFile('test-results.md', testData);

      expect(fs.writeFile).toHaveBeenCalledWith(
        'test-results.md',
        expect.stringContaining('# LLM Memory Test Results'),
        'utf8'
      );

      const savedContent = fs.writeFile.mock.calls[0][1];
      expect(savedContent).toContain('Session ID**: session-123');
      expect(savedContent).toContain('Memory Type**: simple');
      expect(savedContent).toContain('Overall Score**: 75.50%');
      expect(savedContent).toContain('Simple Facts Score**: 80.00%');
      expect(savedContent).toContain('Complex Facts Score**: 70.00%');
      expect(savedContent).toContain('Paris is the capital of France');
    });

    test('should handle simple score format', async () => {
      const testData = {
        sessionId: 'session-123',
        memoryType: 'simple',
        overallScore: 85.5, // Simple number instead of object
        evaluationResults: [],
        conversationLog: []
      };

      await evaluator.saveResultsToFile('test-results.md', testData);

      const savedContent = fs.writeFile.mock.calls[0][1];
      expect(savedContent).toContain('Overall Score**: 85.50%');
    });

    test('should include memory retention visualization', async () => {
      const testData = {
        sessionId: 'session-123',
        overallScore: 75,
        evaluationResults: [
          { factId: 1, score: 8, complexity: 'simple' },
          { factId: 2, score: 6, complexity: 'complex' }
        ],
        conversationLog: []
      };

      await evaluator.saveResultsToFile('test-results.md', testData);

      const savedContent = fs.writeFile.mock.calls[0][1];
      expect(savedContent).toContain('Memory Retention Visualization');
      expect(savedContent).toContain('080% s [################    ]');
      expect(savedContent).toContain('060% c [############        ]');
    });

    test('should handle performance metrics', async () => {
      const testData = {
        sessionId: 'session-123',
        overallScore: 75,
        evaluationResults: [],
        conversationLog: [],
        performanceMetrics: {
          session: { totalDuration: 10000 },
          operations: { 
            completed: 8, 
            total: 10, 
            successRate: 80, 
            averageDuration: 1250,
            longestOperation: { name: 'fact-evaluation', duration: 2000 },
            shortestOperation: { name: 'memory-context', duration: 100 }
          }
        },
        apiStats: {
          overall: {
            totalCalls: 20,
            successRate: 95,
            totalTokens: 5000,
            averageResponseTime: 800,
            totalResponseTime: 16000
          },
          byModel: {
            'gpt-4': {
              calls: 10,
              successRate: 100,
              totalTokens: 3000,
              averageTokensPerCall: 300,
              averageResponseTime: 900,
              minResponseTime: 500,
              maxResponseTime: 1200
            }
          }
        }
      };

      await evaluator.saveResultsToFile('test-results.md', testData);

      const savedContent = fs.writeFile.mock.calls[0][1];
      expect(savedContent).toContain('Performance Metrics');
      expect(savedContent).toContain('Total Duration**: 10.00s');
      expect(savedContent).toContain('Operation Success Rate**: 80%');
      expect(savedContent).toContain('Total API Calls**: 20');
      expect(savedContent).toContain('API Success Rate**: 95%');
    });

    test('should handle file write errors', async () => {
      fs.writeFile.mockRejectedValue(new Error('File write error'));

      const testData = {
        sessionId: 'session-123',
        overallScore: 75,
        evaluationResults: [],
        conversationLog: []
      };

      await expect(evaluator.saveResultsToFile('test-results.md', testData)).rejects.toThrow('File write error');
    });
  });

  describe('Error Handling and Edge Cases', () => {
    beforeEach(() => {
      evaluator = new Evaluator('session-123');
    });

    test('should handle malformed score responses', async () => {
      const malformedResponses = [
        'No score',
        'Score: abc',
        'Invalid response',
        '',
        null
      ];

      for (const response of malformedResponses) {
        mockEvaluatorModel.generateResponse.mockResolvedValue(response);
        const score = await evaluator.scoreResponse('test', 'test');
        expect(score).toBe(0);
      }
    });

    test('should handle memory context retrieval failures during evaluation', async () => {
      const mockMemory = {
        getMemoryContext: jest.fn().mockRejectedValue(new Error('Context error'))
      };

      const facts = [
        { id: 1, fact: 'Test', question: 'Test?', answer: 'Test' }
      ];

      const results = await evaluator.evaluateMemory(mockMemory, facts, []);

      expect(results).toHaveLength(1);
      expect(results[0].actualResponse).toBe('Error: Could not evaluate');
      expect(results[0].score).toBe(0);
    });

    test('should handle LLM response generation failures during evaluation', async () => {
      const mockMemory = {
        getMemoryContext: jest.fn().mockResolvedValue('Context')
      };

      mockEvaluatorModel.generateResponse.mockRejectedValue(new Error('LLM error'));

      const facts = [
        { id: 1, fact: 'Test', question: 'Test?', answer: 'Test' }
      ];

      const results = await evaluator.evaluateMemory(mockMemory, facts, []);

      expect(results).toHaveLength(1);
      expect(results[0].actualResponse).toBe('Error: Could not evaluate');
      expect(results[0].score).toBe(0);
    });

    test('should handle concurrent evaluations', async () => {
      const mockMemory = {
        getMemoryContext: jest.fn().mockResolvedValue('Context')
      };

      mockEvaluatorModel.generateResponse
        .mockResolvedValue('Response')
        .mockResolvedValue('8');

      const facts = [
        { id: 1, fact: 'Test 1', question: 'Test 1?', answer: 'Test 1' },
        { id: 2, fact: 'Test 2', question: 'Test 2?', answer: 'Test 2' }
      ];

      // Run multiple evaluations concurrently
      const promises = [
        evaluator.evaluateMemory(mockMemory, [facts[0]], []),
        evaluator.evaluateMemory(mockMemory, [facts[1]], [])
      ];

      const results = await Promise.all(promises);

      expect(results).toHaveLength(2);
      results.forEach(result => {
        expect(result).toHaveLength(1);
        expect(result[0].score).toBeGreaterThanOrEqual(0);
      });
    });
  });
});