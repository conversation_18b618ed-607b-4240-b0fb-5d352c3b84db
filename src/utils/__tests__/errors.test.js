/**
 * Tests for error classification system
 */

const {
  BaseError,
  ConfigurationError,
  APIError,
  RateLimitError,
  AuthenticationError,
  ValidationError,
  FileSystemError,
  MemoryError,
  TimeoutError,
  CircuitBreakerError,
  ErrorFactory
} = require('../errors');

describe('Error Classification System', () => {
  describe('BaseError', () => {
    test('should create error with message and context', () => {
      const context = { userId: '123', operation: 'test' };
      const error = new BaseError('Test error', context);
      
      expect(error.message).toBe('Test error');
      expect(error.context).toEqual(context);
      expect(error.timestamp).toBeDefined();
      expect(error.name).toBe('BaseError');
    });

    test('should format error message with context', () => {
      const error = new BaseError('Test error', { key: 'value' });
      const formatted = error.getFormattedMessage();
      
      expect(formatted).toContain('[BaseError] Test error');
      expect(formatted).toContain('Context:');
      expect(formatted).toContain('key');
      expect(formatted).toContain('Timestamp:');
    });

    test('should not be retryable by default', () => {
      const error = new BaseError('Test error');
      expect(error.isRetryable()).toBe(false);
    });
  });

  describe('APIError', () => {
    test('should be retryable for certain status codes', () => {
      const retryableCodes = [408, 429, 500, 502, 503, 504];
      
      retryableCodes.forEach(code => {
        const error = new APIError('API error', { statusCode: code });
        expect(error.isRetryable()).toBe(true);
      });
    });

    test('should not be retryable for client errors', () => {
      const nonRetryableCodes = [400, 401, 403, 404];
      
      nonRetryableCodes.forEach(code => {
        const error = new APIError('API error', { statusCode: code });
        expect(error.isRetryable()).toBe(false);
      });
    });

    test('should be retryable for network errors', () => {
      const networkErrors = ['ECONNRESET', 'ENOTFOUND', 'ETIMEDOUT', 'timeout'];
      
      networkErrors.forEach(errorMsg => {
        const error = new APIError(errorMsg);
        expect(error.isRetryable()).toBe(true);
      });
    });

    test('should return appropriate retry delay', () => {
      const rateLimitError = new APIError('Rate limited', { statusCode: 429 });
      expect(rateLimitError.getRetryDelay()).toBe(5000);

      const serviceError = new APIError('Service unavailable', { statusCode: 503 });
      expect(serviceError.getRetryDelay()).toBe(10000);

      const retryAfterError = new APIError('Rate limited', { retryAfter: 60 });
      expect(retryAfterError.getRetryDelay()).toBe(60000);
    });
  });

  describe('RateLimitError', () => {
    test('should always be retryable', () => {
      const error = new RateLimitError('Rate limited');
      expect(error.isRetryable()).toBe(true);
    });

    test('should use retry-after header when available', () => {
      const error = new RateLimitError('Rate limited', { retryAfter: 30 });
      expect(error.getRetryDelay()).toBe(30000);
    });

    test('should use default delay when no retry-after', () => {
      const error = new RateLimitError('Rate limited');
      expect(error.getRetryDelay()).toBe(30000);
    });
  });

  describe('AuthenticationError', () => {
    test('should not be retryable', () => {
      const error = new AuthenticationError('Unauthorized');
      expect(error.isRetryable()).toBe(false);
    });
  });

  describe('ValidationError', () => {
    test('should not be retryable', () => {
      const error = new ValidationError('Invalid input');
      expect(error.isRetryable()).toBe(false);
    });

    test('should store field and value information', () => {
      const error = new ValidationError('Invalid email', { 
        field: 'email', 
        value: 'invalid-email' 
      });
      
      expect(error.field).toBe('email');
      expect(error.value).toBe('invalid-email');
    });
  });

  describe('FileSystemError', () => {
    test('should be retryable for certain error codes', () => {
      const retryableCodes = ['EBUSY', 'EMFILE', 'ENFILE'];
      
      retryableCodes.forEach(code => {
        const error = new FileSystemError(`File error: ${code}`);
        expect(error.isRetryable()).toBe(true);
      });
    });

    test('should not be retryable for permanent errors', () => {
      const error = new FileSystemError('ENOENT: file not found');
      expect(error.isRetryable()).toBe(false);
    });
  });

  describe('MemoryError', () => {
    test('should be retryable for certain operations', () => {
      const retryableOps = ['summarize', 'extract_knowledge'];
      
      retryableOps.forEach(op => {
        const error = new MemoryError('Memory operation failed', { operation: op });
        expect(error.isRetryable()).toBe(true);
      });
    });

    test('should not be retryable for other operations', () => {
      const error = new MemoryError('Memory operation failed', { operation: 'add_message' });
      expect(error.isRetryable()).toBe(false);
    });
  });

  describe('TimeoutError', () => {
    test('should be retryable', () => {
      const error = new TimeoutError('Operation timed out');
      expect(error.isRetryable()).toBe(true);
    });

    test('should return appropriate retry delay', () => {
      const error = new TimeoutError('Operation timed out');
      expect(error.getRetryDelay()).toBe(3000);
    });
  });

  describe('CircuitBreakerError', () => {
    test('should not be retryable', () => {
      const error = new CircuitBreakerError('Circuit breaker is open');
      expect(error.isRetryable()).toBe(false);
    });
  });

  describe('ErrorFactory', () => {
    test('should create APIError for network errors', () => {
      const networkError = new Error('ENOTFOUND');
      networkError.code = 'ENOTFOUND';
      
      const error = ErrorFactory.createError(networkError);
      expect(error).toBeInstanceOf(APIError);
    });

    test('should create AuthenticationError for 401 status', () => {
      const authError = new Error('Unauthorized');
      authError.status = 401;
      
      const error = ErrorFactory.createError(authError);
      expect(error).toBeInstanceOf(AuthenticationError);
    });

    test('should create RateLimitError for 429 status', () => {
      const rateLimitError = new Error('Too Many Requests');
      rateLimitError.status = 429;
      
      const error = ErrorFactory.createError(rateLimitError);
      expect(error).toBeInstanceOf(RateLimitError);
    });

    test('should create TimeoutError for timeout messages', () => {
      const timeoutError = new Error('Request timeout');
      
      const error = ErrorFactory.createError(timeoutError);
      expect(error).toBeInstanceOf(TimeoutError);
    });

    test('should create ValidationError for validation messages', () => {
      const validationError = new Error('Validation failed');
      
      const error = ErrorFactory.createError(validationError);
      expect(error).toBeInstanceOf(ValidationError);
    });

    test('should create FileSystemError for file system codes', () => {
      const fsError = new Error('File not found');
      fsError.code = 'ENOENT';
      
      const error = ErrorFactory.createError(fsError);
      expect(error).toBeInstanceOf(FileSystemError);
    });

    test('should create BaseError for unknown errors', () => {
      const unknownError = new Error('Unknown error');
      
      const error = ErrorFactory.createError(unknownError);
      expect(error).toBeInstanceOf(BaseError);
    });

    test('should preserve context information', () => {
      const originalError = new Error('Test error');
      const context = { operation: 'test', userId: '123' };
      
      const error = ErrorFactory.createError(originalError, context);
      expect(error.context).toEqual(expect.objectContaining(context));
    });
  });
});