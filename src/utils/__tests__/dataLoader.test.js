/**
 * Comprehensive tests for DataLoader
 * Tests cover test fact loading, scenario discovery, and data validation
 */

const dataLoader = require('../dataLoader');
const fs = require('fs').promises;
const path = require('path');

// Mock fs module
jest.mock('fs', () => ({
  promises: {
    readdir: jest.fn(),
    readFile: jest.fn(),
    access: jest.fn()
  }
}));

// Mock path module
jest.mock('path', () => ({
  join: jest.fn((...args) => args.join('/')),
  resolve: jest.fn()
}));

// Mock logger
jest.mock('../logger', () => ({
  defaultLogger: {
    info: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
  }
}));

// Mock config
jest.mock('../../config', () => ({
  ConfigManager: jest.fn()
}));

describe('DataLoader', () => {
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Mock path.join to return predictable paths
    path.join.mockImplementation((...args) => args.join('/'));
  });

  describe('loadAllTestFactsFiles', () => {
    test('should load all test fact files from data directory', async () => {
      const mockFiles = [
        'test_facts_customer.json',
        'test_facts_simple.json',
        'other_file.txt',
        'test_facts_casual.json'
      ];

      const mockFactsData = {
        customer: [
          { id: 1, fact: 'Customer fact 1', question: 'Q1?', answer: 'A1' }
        ],
        simple: [
          { id: 1, fact: 'Simple fact 1', question: 'Q1?', answer: 'A1' }
        ],
        casual: [
          { id: 1, fact: 'Casual fact 1', question: 'Q1?', answer: 'A1' }
        ]
      };

      fs.readdir.mockResolvedValue(mockFiles);
      fs.readFile.mockImplementation((filePath) => {
        if (filePath.includes('customer')) {
          return Promise.resolve(JSON.stringify(mockFactsData.customer));
        } else if (filePath.includes('simple')) {
          return Promise.resolve(JSON.stringify(mockFactsData.simple));
        } else if (filePath.includes('casual')) {
          return Promise.resolve(JSON.stringify(mockFactsData.casual));
        }
        return Promise.reject(new Error('File not found'));
      });

      const result = await dataLoader.loadAllTestFactsFiles();

      expect(result).toEqual(mockFactsData);
      expect(fs.readdir).toHaveBeenCalledWith(expect.stringContaining('data'));
      expect(fs.readFile).toHaveBeenCalledTimes(3); // Only JSON files
    });

    test('should handle JSON parsing errors gracefully', async () => {
      const mockFiles = [
        'test_facts_valid.json',
        'test_facts_invalid.json'
      ];

      fs.readdir.mockResolvedValue(mockFiles);
      fs.readFile.mockImplementation((filePath) => {
        if (filePath.includes('valid')) {
          return Promise.resolve(JSON.stringify([{ id: 1, fact: 'Valid fact' }]));
        } else {
          return Promise.resolve('invalid json content');
        }
      });

      const result = await dataLoader.loadAllTestFactsFiles();

      expect(result).toEqual({
        valid: [{ id: 1, fact: 'Valid fact' }]
      });
      // Should not include invalid file
      expect(result.invalid).toBeUndefined();
    });

    test('should handle directory read errors', async () => {
      fs.readdir.mockRejectedValue(new Error('Directory not found'));

      await expect(dataLoader.loadAllTestFactsFiles()).rejects.toThrow('Directory not found');
    });

    test('should handle empty data directory', async () => {
      fs.readdir.mockResolvedValue([]);

      const result = await dataLoader.loadAllTestFactsFiles();

      expect(result).toEqual({});
    });

    test('should filter non-test-fact files correctly', async () => {
      const mockFiles = [
        'test_facts_scenario1.json',
        'other_data.json',
        'readme.txt',
        'test_facts_scenario2.json',
        'backup.json'
      ];

      fs.readdir.mockResolvedValue(mockFiles);
      fs.readFile.mockImplementation((filePath) => {
        if (filePath.includes('scenario1')) {
          return Promise.resolve(JSON.stringify([{ id: 1, fact: 'Scenario 1' }]));
        } else if (filePath.includes('scenario2')) {
          return Promise.resolve(JSON.stringify([{ id: 1, fact: 'Scenario 2' }]));
        }
        return Promise.reject(new Error('Should not be called'));
      });

      const result = await dataLoader.loadAllTestFactsFiles();

      expect(result).toEqual({
        scenario1: [{ id: 1, fact: 'Scenario 1' }],
        scenario2: [{ id: 1, fact: 'Scenario 2' }]
      });
      expect(fs.readFile).toHaveBeenCalledTimes(2);
    });
  });

  describe('getAllTestFactScenarios', () => {
    test('should return list of available scenarios', async () => {
      const mockFiles = [
        'test_facts_customer.json',
        'test_facts_simple.json',
        'other_file.txt',
        'test_facts_casual.json',
        'test_facts_complex.json'
      ];

      fs.readdir.mockResolvedValue(mockFiles);

      const result = await dataLoader.getAllTestFactScenarios();

      expect(result).toEqual(['customer', 'simple', 'casual', 'complex']);
    });

    test('should handle empty directory', async () => {
      fs.readdir.mockResolvedValue([]);

      const result = await dataLoader.getAllTestFactScenarios();

      expect(result).toEqual([]);
    });

    test('should handle directory read errors', async () => {
      fs.readdir.mockRejectedValue(new Error('Access denied'));

      await expect(dataLoader.getAllTestFactScenarios()).rejects.toThrow('Access denied');
    });

    test('should filter out non-test-fact files', async () => {
      const mockFiles = [
        'test_facts_valid.json',
        'data.json',
        'test_facts_another.json',
        'readme.md',
        'backup_test_facts_old.json'
      ];

      fs.readdir.mockResolvedValue(mockFiles);

      const result = await dataLoader.getAllTestFactScenarios();

      expect(result).toEqual(['valid', 'another']);
    });
  });

  describe('createMergedRandomFacts', () => {
    beforeEach(() => {
      // Mock loadAllTestFactsFiles for createMergedRandomFacts
      const mockAllFacts = {
        scenario1: [
          { id: 1, fact: 'Simple fact 1', complexity: 'simple', question: 'Q1?', answer: 'A1' },
          { id: 2, fact: 'Complex fact 1', complexity: 'complex', question: 'Q2?', answer: 'A2' },
          { id: 3, fact: 'Simple fact 2', complexity: 'simple', question: 'Q3?', answer: 'A3' }
        ],
        scenario2: [
          { id: 4, fact: 'Complex fact 2', complexity: 'complex', question: 'Q4?', answer: 'A4' },
          { id: 5, fact: 'Simple fact 3', complexity: 'simple', question: 'Q5?', answer: 'A5' },
          { id: 6, fact: 'Complex fact 3', complexity: 'complex', question: 'Q6?', answer: 'A6' }
        ]
      };

      // Mock the loadAllTestFactsFiles function
      jest.spyOn(dataLoader, 'loadAllTestFactsFiles').mockResolvedValue(mockAllFacts);
    });

    test('should create merged facts with alternating complexity', async () => {
      const result = await dataLoader.createMergedRandomFacts(6);

      expect(result).toHaveLength(6);

      // Check alternating pattern: simple at even indices, complex at odd indices
      for (let i = 0; i < result.length; i++) {
        const expectedComplexity = i % 2 === 0 ? 'simple' : 'complex';
        expect(result[i].complexity).toBe(expectedComplexity);
      }

      // Check that IDs are reassigned sequentially
      result.forEach((fact, index) => {
        expect(fact.id).toBe(index + 1);
      });
    });

    test('should handle facts without complexity field', async () => {
      const mockAllFacts = {
        scenario1: [
          { id: 1, fact: 'Short fact', question: 'Q1?', answer: 'A1' }, // Should be simple
          { id: 2, fact: 'This is a very long fact that contains a lot of information and should be classified as complex', question: 'Q2?', answer: 'A2' } // Should be complex
        ]
      };

      jest.spyOn(dataLoader, 'loadAllTestFactsFiles').mockResolvedValue(mockAllFacts);

      const result = await dataLoader.createMergedRandomFacts(2);

      expect(result).toHaveLength(2);
      expect(result[0].complexity).toBe('simple'); // Short fact
      expect(result[1].complexity).toBe('complex'); // Long fact
    });

    test('should handle insufficient facts for requested count', async () => {
      const mockAllFacts = {
        scenario1: [
          { id: 1, fact: 'Simple fact', complexity: 'simple', question: 'Q1?', answer: 'A1' }
        ]
      };

      jest.spyOn(dataLoader, 'loadAllTestFactsFiles').mockResolvedValue(mockAllFacts);

      const result = await dataLoader.createMergedRandomFacts(10);

      expect(result).toHaveLength(1); // Only one fact available
      expect(result[0].complexity).toBe('simple');
    });

    test('should handle empty fact files', async () => {
      jest.spyOn(dataLoader, 'loadAllTestFactsFiles').mockResolvedValue({});

      const result = await dataLoader.createMergedRandomFacts(5);

      expect(result).toHaveLength(0);
    });

    test('should preserve original fact properties', async () => {
      const mockAllFacts = {
        scenario1: [
          { 
            id: 1, 
            fact: 'Test fact', 
            complexity: 'simple', 
            question: 'Test question?', 
            answer: 'Test answer',
            metadata: { source: 'test' }
          }
        ]
      };

      jest.spyOn(dataLoader, 'loadAllTestFactsFiles').mockResolvedValue(mockAllFacts);

      const result = await dataLoader.createMergedRandomFacts(1);

      expect(result[0]).toEqual(expect.objectContaining({
        id: 1, // Reassigned
        fact: 'Test fact',
        complexity: 'simple',
        question: 'Test question?',
        answer: 'Test answer',
        metadata: { source: 'test' }
      }));
    });

    test('should handle loadAllTestFactsFiles errors', async () => {
      jest.spyOn(dataLoader, 'loadAllTestFactsFiles').mockRejectedValue(new Error('Load error'));

      await expect(dataLoader.createMergedRandomFacts(5)).rejects.toThrow('Load error');
    });
  });

  describe('loadTestFacts', () => {
    beforeEach(() => {
      // Mock ConfigManager
      const { ConfigManager } = require('../../config');
      ConfigManager.mockImplementation(() => ({
        get: jest.fn((key, defaultValue) => {
          const config = {
            'test.factsFile': 'customer'
          };
          return config[key] || defaultValue;
        })
      }));
    });

    test('should load facts from specific scenario', async () => {
      const mockFacts = [
        { id: 1, fact: 'Customer fact 1', question: 'Q1?', answer: 'A1' },
        { id: 2, fact: 'Customer fact 2', question: 'Q2?', answer: 'A2' }
      ];

      fs.access.mockResolvedValue(); // File exists
      fs.readFile.mockResolvedValue(JSON.stringify(mockFacts));

      const result = await dataLoader.loadTestFacts(5, 'customer');

      expect(result).toEqual(mockFacts);
      expect(fs.readFile).toHaveBeenCalledWith(
        expect.stringContaining('test_facts_customer.json'),
        'utf8'
      );
    });

    test('should limit facts to requested count', async () => {
      const mockFacts = [
        { id: 1, fact: 'Fact 1' },
        { id: 2, fact: 'Fact 2' },
        { id: 3, fact: 'Fact 3' },
        { id: 4, fact: 'Fact 4' },
        { id: 5, fact: 'Fact 5' }
      ];

      fs.access.mockResolvedValue();
      fs.readFile.mockResolvedValue(JSON.stringify(mockFacts));

      const result = await dataLoader.loadTestFacts(3, 'test');

      expect(result).toHaveLength(3);
      expect(result).toEqual(mockFacts.slice(0, 3));
    });

    test('should return all facts if count exceeds available', async () => {
      const mockFacts = [
        { id: 1, fact: 'Fact 1' },
        { id: 2, fact: 'Fact 2' }
      ];

      fs.access.mockResolvedValue();
      fs.readFile.mockResolvedValue(JSON.stringify(mockFacts));

      const result = await dataLoader.loadTestFacts(10, 'test');

      expect(result).toHaveLength(2);
      expect(result).toEqual(mockFacts);
    });

    test('should fallback to default file when scenario file not found', async () => {
      const mockFacts = [
        { id: 1, fact: 'Default fact', question: 'Q?', answer: 'A' }
      ];

      fs.access.mockRejectedValueOnce(new Error('File not found')); // Scenario file doesn't exist
      fs.readFile.mockResolvedValue(JSON.stringify(mockFacts));

      const result = await dataLoader.loadTestFacts(5, 'nonexistent');

      expect(result).toEqual(mockFacts);
      expect(fs.readFile).toHaveBeenCalledWith(
        expect.stringContaining('test_facts.json'),
        'utf8'
      );
    });

    test('should use merged_random mode', async () => {
      jest.spyOn(dataLoader, 'createMergedRandomFacts').mockResolvedValue([
        { id: 1, fact: 'Merged fact', complexity: 'simple' }
      ]);

      const result = await dataLoader.loadTestFacts(5, 'merged_random');

      expect(result).toHaveLength(1);
      expect(dataLoader.createMergedRandomFacts).toHaveBeenCalledWith(5);
    });

    test('should use configuration when no scenario provided', async () => {
      const mockFacts = [{ id: 1, fact: 'Config fact' }];

      fs.access.mockResolvedValue();
      fs.readFile.mockResolvedValue(JSON.stringify(mockFacts));

      const result = await dataLoader.loadTestFacts(5); // No scenario provided

      expect(result).toEqual(mockFacts);
      expect(fs.readFile).toHaveBeenCalledWith(
        expect.stringContaining('test_facts_customer.json'), // From config
        'utf8'
      );
    });

    test('should use custom config manager', async () => {
      const customConfig = {
        get: jest.fn().mockReturnValue('custom_scenario')
      };

      const mockFacts = [{ id: 1, fact: 'Custom fact' }];

      fs.access.mockResolvedValue();
      fs.readFile.mockResolvedValue(JSON.stringify(mockFacts));

      const result = await dataLoader.loadTestFacts(5, null, customConfig);

      expect(customConfig.get).toHaveBeenCalledWith('test.factsFile');
      expect(fs.readFile).toHaveBeenCalledWith(
        expect.stringContaining('test_facts_custom_scenario.json'),
        'utf8'
      );
    });

    test('should handle JSON parsing errors', async () => {
      fs.access.mockResolvedValue();
      fs.readFile.mockResolvedValue('invalid json');

      await expect(dataLoader.loadTestFacts(5, 'test')).rejects.toThrow();
    });

    test('should handle file read errors', async () => {
      fs.access.mockResolvedValue();
      fs.readFile.mockRejectedValue(new Error('Read error'));

      await expect(dataLoader.loadTestFacts(5, 'test')).rejects.toThrow('Read error');
    });

    test('should handle fallback file not found', async () => {
      fs.access.mockRejectedValue(new Error('File not found')); // Both files don't exist
      fs.readFile.mockRejectedValue(new Error('File not found'));

      await expect(dataLoader.loadTestFacts(5, 'nonexistent')).rejects.toThrow('File not found');
    });
  });

  describe('Error Handling and Edge Cases', () => {
    test('should handle concurrent loadTestFacts calls', async () => {
      const mockFacts = [
        { id: 1, fact: 'Fact 1' },
        { id: 2, fact: 'Fact 2' }
      ];

      fs.access.mockResolvedValue();
      fs.readFile.mockResolvedValue(JSON.stringify(mockFacts));

      const promises = [
        dataLoader.loadTestFacts(2, 'scenario1'),
        dataLoader.loadTestFacts(2, 'scenario2'),
        dataLoader.loadTestFacts(2, 'scenario3')
      ];

      const results = await Promise.all(promises);

      expect(results).toHaveLength(3);
      results.forEach(result => {
        expect(result).toEqual(mockFacts);
      });
    });

    test('should handle zero count request', async () => {
      const mockFacts = [{ id: 1, fact: 'Fact 1' }];

      fs.access.mockResolvedValue();
      fs.readFile.mockResolvedValue(JSON.stringify(mockFacts));

      const result = await dataLoader.loadTestFacts(0, 'test');

      expect(result).toHaveLength(0);
    });

    test('should handle negative count request', async () => {
      const mockFacts = [{ id: 1, fact: 'Fact 1' }];

      fs.access.mockResolvedValue();
      fs.readFile.mockResolvedValue(JSON.stringify(mockFacts));

      const result = await dataLoader.loadTestFacts(-5, 'test');

      expect(result).toHaveLength(0);
    });

    test('should handle empty JSON array', async () => {
      fs.access.mockResolvedValue();
      fs.readFile.mockResolvedValue(JSON.stringify([]));

      const result = await dataLoader.loadTestFacts(5, 'test');

      expect(result).toEqual([]);
    });

    test('should handle malformed fact objects', async () => {
      const mockFacts = [
        { id: 1, fact: 'Valid fact', question: 'Q?', answer: 'A' },
        { id: 2 }, // Missing required fields
        { fact: 'No ID fact', question: 'Q?', answer: 'A' },
        null, // Null fact
        'invalid fact' // String instead of object
      ];

      fs.access.mockResolvedValue();
      fs.readFile.mockResolvedValue(JSON.stringify(mockFacts));

      const result = await dataLoader.loadTestFacts(5, 'test');

      // Should return all facts as-is (validation is handled elsewhere)
      expect(result).toEqual(mockFacts);
    });
  });

  describe('Integration Tests', () => {
    test('should work with realistic fact data structure', async () => {
      const realisticFacts = [
        {
          id: 1,
          fact: 'Paris is the capital of France',
          question: 'What is the capital of France?',
          answer: 'Paris',
          complexity: 'simple',
          category: 'geography',
          metadata: {
            source: 'world_facts',
            difficulty: 1,
            tags: ['europe', 'capitals']
          }
        },
        {
          id: 2,
          fact: 'Quantum entanglement is a phenomenon where particles become correlated',
          question: 'What is quantum entanglement?',
          answer: 'A phenomenon where particles become correlated',
          complexity: 'complex',
          category: 'physics',
          metadata: {
            source: 'science_facts',
            difficulty: 8,
            tags: ['quantum', 'physics', 'particles']
          }
        }
      ];

      fs.access.mockResolvedValue();
      fs.readFile.mockResolvedValue(JSON.stringify(realisticFacts));

      const result = await dataLoader.loadTestFacts(2, 'realistic');

      expect(result).toEqual(realisticFacts);
      expect(result[0]).toHaveProperty('metadata');
      expect(result[1]).toHaveProperty('complexity', 'complex');
    });

    test('should handle mixed scenario loading workflow', async () => {
      // First, get available scenarios
      fs.readdir.mockResolvedValue([
        'test_facts_customer.json',
        'test_facts_simple.json'
      ]);

      const scenarios = await dataLoader.getAllTestFactScenarios();
      expect(scenarios).toEqual(['customer', 'simple']);

      // Then load facts from each scenario
      const customerFacts = [{ id: 1, fact: 'Customer fact' }];
      const simpleFacts = [{ id: 1, fact: 'Simple fact' }];

      fs.access.mockResolvedValue();
      fs.readFile
        .mockResolvedValueOnce(JSON.stringify(customerFacts))
        .mockResolvedValueOnce(JSON.stringify(simpleFacts));

      const customerResult = await dataLoader.loadTestFacts(5, 'customer');
      const simpleResult = await dataLoader.loadTestFacts(5, 'simple');

      expect(customerResult).toEqual(customerFacts);
      expect(simpleResult).toEqual(simpleFacts);
    });
  });
});