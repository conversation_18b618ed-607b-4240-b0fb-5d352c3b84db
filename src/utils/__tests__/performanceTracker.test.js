/**
 * Performance Tracker Tests
 * 
 * Comprehensive test suite for the PerformanceTracker class covering
 * all functionality including operation tracking, API call monitoring,
 * memory usage tracking, system resource monitoring, and metrics collection.
 */

// Mock os module
jest.mock('os', () => ({
  totalmem: jest.fn(() => 8 * 1024 * 1024 * 1024), // 8GB
  freemem: jest.fn(() => 4 * 1024 * 1024 * 1024), // 4GB
  loadavg: jest.fn(() => [1.5, 1.2, 1.0]),
  uptime: jest.fn(() => 86400), // 1 day
  cpus: jest.fn(() => [
    { times: { user: 1000, nice: 0, sys: 500, idle: 8500, irq: 0 } },
    { times: { user: 1200, nice: 0, sys: 600, idle: 8200, irq: 0 } }
  ])
}));

// Mock logger
jest.mock('../logger', () => ({
  defaultLogger: {
    info: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    child: jest.fn(() => ({
      info: jest.fn(),
      debug: jest.fn(),
      warn: jest.fn(),
      error: jest.fn()
    }))
  }
}));

const PerformanceTracker = require('../performanceTracker');

describe('PerformanceTracker', () => {
  let tracker;
  let mockLogger;
  let originalHrtime;

  beforeAll(() => {
    // Store original hrtime
    originalHrtime = process.hrtime;
  });

  afterAll(() => {
    // Restore original hrtime
    process.hrtime = originalHrtime;
  });

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Mock logger
    mockLogger = {
      info: jest.fn(),
      debug: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
      child: jest.fn(() => mockLogger)
    };

    // Mock hrtime for consistent timing
    let hrtimeCounter = 0n;
    process.hrtime = {
      bigint: jest.fn(() => {
        hrtimeCounter += 1000000n; // 1ms increments
        return hrtimeCounter;
      })
    };

    // Mock process.memoryUsage
    process.memoryUsage = jest.fn(() => ({
      rss: 50 * 1024 * 1024,
      heapTotal: 30 * 1024 * 1024,
      heapUsed: 20 * 1024 * 1024,
      external: 5 * 1024 * 1024
    }));

    // Create fresh tracker instance
    tracker = new PerformanceTracker('test-session-123');
  });

  afterEach(() => {
    if (tracker) {
      tracker.finalize();
    }
    jest.clearAllTimers();
  });

  describe('Constructor', () => {
    it('should create a new PerformanceTracker with valid session ID', () => {
      expect(tracker.sessionId).toBe('test-session-123');
      expect(tracker.isSystemMonitoring()).toBe(true);
    });

    it('should throw error for invalid session ID', () => {
      expect(() => new PerformanceTracker('')).toThrow('Session ID must be a non-empty string');
      expect(() => new PerformanceTracker(null)).toThrow('Session ID must be a non-empty string');
      expect(() => new PerformanceTracker(123)).toThrow('Session ID must be a non-empty string');
    });

    it('should accept custom options', () => {
      const customTracker = new PerformanceTracker('test', {
        enableSystemMonitoring: false,
        systemMonitoringInterval: 10000,
        enableDetailedLogging: true
      });

      expect(customTracker.isSystemMonitoring()).toBe(false);
      expect(customTracker.options.systemMonitoringInterval).toBe(10000);
      expect(customTracker.options.enableDetailedLogging).toBe(true);

      customTracker.finalize();
    });
  });

  describe('Operation Tracking', () => {
    it('should start and end operations successfully', () => {
      const operationId = tracker.startOperation('test-operation');
      expect(operationId).toMatch(/test-operation_\d+_[a-z0-9]+/);

      const summary = tracker.endOperation(operationId);
      expect(summary.operationName).toBe('test-operation');
      expect(summary.status).toBe('completed');
      expect(summary.duration).toBeGreaterThan(0);
    });

    it('should track operation context and metadata', () => {
      const context = {
        type: 'api-call',
        component: 'LLMModel',
        metadata: { model: 'gpt-3.5-turbo' }
      };

      const operationId = tracker.startOperation('llm-call', context);
      const summary = tracker.endOperation(operationId, {
        success: true,
        metadata: { tokensUsed: 150 }
      });

      expect(summary.result.metadata.tokensUsed).toBe(150);
    });

    it('should handle operation failures', () => {
      const operationId = tracker.startOperation('failing-operation');
      const summary = tracker.endOperation(operationId, {
        success: false,
        error: 'Test error'
      });

      expect(summary.status).toBe('failed');
      expect(summary.result.error).toBe('Test error');
    });

    it('should throw error for non-existent operation', () => {
      expect(() => tracker.endOperation('non-existent')).toThrow('Operation not found');
    });

    it('should throw error for already completed operation', () => {
      const operationId = tracker.startOperation('test-operation');
      tracker.endOperation(operationId);
      
      expect(() => tracker.endOperation(operationId)).toThrow('Operation not found');
    });

    it('should find operations by name', () => {
      const operationId = tracker.startOperation('named-operation');
      const summary = tracker.endOperation('named-operation');
      
      expect(summary.operationName).toBe('named-operation');
    });

    it('should update operation statistics correctly', () => {
      // Start and end multiple operations
      for (let i = 0; i < 3; i++) {
        const opId = tracker.startOperation(`operation-${i}`);
        tracker.endOperation(opId);
      }

      const metrics = tracker.getMetrics();
      expect(metrics.operations.total).toBe(3);
      expect(metrics.operations.completed).toBe(3);
      expect(metrics.operations.failed).toBe(0);
      expect(metrics.operations.averageDuration).toBeGreaterThan(0);
    });
  });

  describe('API Call Tracking', () => {
    it('should record API calls with basic information', () => {
      tracker.recordApiCall('gpt-3.5-turbo', 1500, 250, true);

      const apiStats = tracker.getApiStatistics();
      expect(apiStats.overall.totalCalls).toBe(1);
      expect(apiStats.overall.successfulCalls).toBe(1);
      expect(apiStats.overall.totalTokens).toBe(250);
      expect(apiStats.byModel['gpt-3.5-turbo'].calls).toBe(1);
    });

    it('should record failed API calls', () => {
      tracker.recordApiCall('claude-3-sonnet', 5000, 0, false, {
        error: 'Rate limit exceeded'
      });

      const apiStats = tracker.getApiStatistics();
      expect(apiStats.overall.failedCalls).toBe(1);
      expect(apiStats.overall.successRate).toBe('0.00');
      expect(apiStats.byModel['claude-3-sonnet'].failedCalls).toBe(1);
    });

    it('should track per-model statistics', () => {
      tracker.recordApiCall('gpt-3.5-turbo', 1000, 100, true);
      tracker.recordApiCall('gpt-3.5-turbo', 1500, 150, true);
      tracker.recordApiCall('claude-3-sonnet', 2000, 200, true);

      const apiStats = tracker.getApiStatistics();
      
      expect(apiStats.byModel['gpt-3.5-turbo'].calls).toBe(2);
      expect(apiStats.byModel['gpt-3.5-turbo'].totalTokens).toBe(250);
      expect(apiStats.byModel['gpt-3.5-turbo'].averageResponseTime).toBe(1250);
      
      expect(apiStats.byModel['claude-3-sonnet'].calls).toBe(1);
      expect(apiStats.byModel['claude-3-sonnet'].totalTokens).toBe(200);
    });

    it('should calculate response time statistics', () => {
      const responseTimes = [1000, 1500, 2000, 1200, 1800];
      
      responseTimes.forEach((time, index) => {
        tracker.recordApiCall('test-model', time, 100, true);
      });

      const apiStats = tracker.getApiStatistics();
      const modelStats = apiStats.byModel['test-model'];
      
      expect(modelStats.minResponseTime).toBe(1000);
      expect(modelStats.maxResponseTime).toBe(2000);
      expect(modelStats.averageResponseTime).toBe(1500);
    });

    it('should validate API call parameters', () => {
      expect(() => tracker.recordApiCall('', 1000, 100)).toThrow('Model name must be a non-empty string');
      expect(() => tracker.recordApiCall('model', -100, 100)).toThrow('Response time must be a non-negative number');
      expect(() => tracker.recordApiCall('model', 1000, -50)).toThrow('Token count must be a non-negative number');
    });

    it('should handle detailed API call information', () => {
      tracker.recordApiCall('gpt-4', 2500, 300, true, {
        promptTokens: 200,
        completionTokens: 100,
        operationType: 'chat'
      });

      const apiStats = tracker.getApiStatistics();
      const recentCall = apiStats.recentCalls[0];
      
      expect(recentCall.details.promptTokens).toBe(200);
      expect(recentCall.details.completionTokens).toBe(100);
      expect(recentCall.details.operationType).toBe('chat');
    });
  });

  describe('Memory Monitoring', () => {
    it('should track memory usage snapshots', () => {
      tracker.startMemoryMonitoring();
      
      const memoryStats = tracker.getMemoryStatistics();
      expect(memoryStats.current.heapUsed).toBeGreaterThan(0);
      expect(memoryStats.current.heapTotal).toBeGreaterThan(0);
      expect(memoryStats.current.heapUtilization).toMatch(/\d+\.\d+/);
    });

    it('should track peak memory usage', () => {
      // Mock increasing memory usage
      let heapUsed = 20 * 1024 * 1024;
      process.memoryUsage = jest.fn(() => ({
        rss: 50 * 1024 * 1024,
        heapTotal: 30 * 1024 * 1024,
        heapUsed: heapUsed,
        external: 5 * 1024 * 1024
      }));

      tracker.startMemoryMonitoring();
      
      // Simulate memory increase
      heapUsed = 25 * 1024 * 1024;
      tracker._takeMemorySnapshot();
      
      const memoryStats = tracker.getMemoryStatistics();
      expect(memoryStats.peak.heapUsed).toBeGreaterThan(20);
    });

    it('should calculate memory trends', () => {
      // Create multiple snapshots with increasing memory
      for (let i = 0; i < 5; i++) {
        const heapUsed = (20 + i * 2) * 1024 * 1024;
        process.memoryUsage = jest.fn(() => ({
          rss: 50 * 1024 * 1024,
          heapTotal: 30 * 1024 * 1024,
          heapUsed: heapUsed,
          external: 5 * 1024 * 1024
        }));
        tracker._takeMemorySnapshot();
      }

      const memoryStats = tracker.getMemoryStatistics();
      expect(memoryStats.trends.trend).toBe('increasing');
      expect(parseFloat(memoryStats.trends.changePercent)).toBeGreaterThan(0);
    });

    it('should track garbage collection events', () => {
      // Mock global.gc
      global.gc = jest.fn();
      
      tracker.startMemoryMonitoring();
      
      // Simulate GC call
      global.gc();
      
      const memoryStats = tracker.getMemoryStatistics();
      expect(memoryStats.gcEvents).toBe(1);
    });
  });

  describe('System Monitoring', () => {
    beforeEach(() => {
      // Use fake timers for interval testing
      jest.useFakeTimers();
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('should start and stop system monitoring', () => {
      tracker.stopSystemMonitoring();
      expect(tracker.isSystemMonitoring()).toBe(false);
      
      tracker.startSystemMonitoring();
      expect(tracker.isSystemMonitoring()).toBe(true);
      
      tracker.stopSystemMonitoring();
      expect(tracker.isSystemMonitoring()).toBe(false);
    });

    it('should take system snapshots at intervals', () => {
      tracker.stopSystemMonitoring(); // Stop existing monitoring
      tracker.startSystemMonitoring();
      
      // Fast-forward time to trigger snapshots
      jest.advanceTimersByTime(10000); // 10 seconds
      
      const systemStats = tracker.getSystemStatistics();
      expect(systemStats.snapshots.length).toBeGreaterThanOrEqual(1);
    });

    it('should calculate CPU usage', () => {
      tracker.startSystemMonitoring();
      
      const systemStats = tracker.getSystemStatistics();
      expect(systemStats.cpu.current).toBeGreaterThanOrEqual(0);
      expect(systemStats.cpu.current).toBeLessThanOrEqual(100);
    });

    it('should track system memory statistics', () => {
      const systemStats = tracker.getSystemStatistics();
      
      expect(systemStats.memory.total).toBeGreaterThan(0);
      expect(systemStats.memory.free).toBeGreaterThan(0);
      expect(systemStats.memory.used).toBeGreaterThan(0);
      expect(parseFloat(systemStats.memory.utilization)).toBeGreaterThan(0);
    });

    it('should track load average and uptime', () => {
      const systemStats = tracker.getSystemStatistics();
      
      expect(Array.isArray(systemStats.load.current)).toBe(true);
      expect(systemStats.load.current).toHaveLength(3);
      expect(systemStats.load.cores).toBe(2);
      expect(systemStats.uptime.system).toBeGreaterThan(0);
      expect(systemStats.uptime.process).toBeGreaterThan(0);
    });
  });

  describe('Metrics and Statistics', () => {
    it('should provide comprehensive metrics', () => {
      // Add some test data
      const opId = tracker.startOperation('test-op');
      tracker.endOperation(opId);
      tracker.recordApiCall('test-model', 1000, 100, true);

      const metrics = tracker.getMetrics();
      
      expect(metrics.session.sessionId).toBe('test-session-123');
      expect(metrics.operations.total).toBe(1);
      expect(metrics.api.totalCalls).toBe(1);
      expect(metrics.memory.currentUsage).toBeGreaterThan(0);
      expect(metrics.system.totalMemory).toBeGreaterThan(0);
    });

    it('should calculate derived metrics correctly', () => {
      // Add successful and failed operations
      const op1 = tracker.startOperation('success-op');
      tracker.endOperation(op1, { success: true });
      
      const op2 = tracker.startOperation('fail-op');
      tracker.endOperation(op2, { success: false });

      // Add successful and failed API calls
      tracker.recordApiCall('model1', 1000, 100, true);
      tracker.recordApiCall('model2', 2000, 0, false);

      const metrics = tracker.getMetrics();
      
      expect(metrics.operations.successRate).toBe('50.00');
      expect(metrics.operations.failureRate).toBe('50.00');
      expect(metrics.api.successRate).toBe('50.00');
      expect(metrics.api.failureRate).toBe('50.00');
    });

    it('should provide operation statistics by name', () => {
      // Create multiple operations with same name
      for (let i = 0; i < 3; i++) {
        const opId = tracker.startOperation('repeated-operation');
        tracker.endOperation(opId);
      }

      const opStats = tracker.getOperationStatistics();
      const repeatedOpStats = opStats.byName.find(op => op.name === 'repeated-operation');
      
      expect(repeatedOpStats.count).toBe(3);
      expect(repeatedOpStats.successCount).toBe(3);
      expect(repeatedOpStats.successRate).toBe('100.00');
    });

    it('should track longest and shortest operations', () => {
      // Mock different operation durations
      let duration = 1000000n; // 1ms
      mockProcess.hrtime.bigint.mockImplementation(() => {
        const current = duration;
        duration += 1000000n; // Increment by 1ms each call
        return current;
      });

      const op1 = tracker.startOperation('short-op');
      tracker.endOperation(op1);
      
      const op2 = tracker.startOperation('long-op');
      // Simulate longer operation
      duration += 5000000n; // Add 5ms
      tracker.endOperation(op2);

      const metrics = tracker.getMetrics();
      expect(metrics.operations.longestOperation.name).toBe('long-op');
      expect(metrics.operations.shortestOperation.name).toBe('short-op');
    });
  });

  describe('Data Export', () => {
    beforeEach(() => {
      // Add some test data
      const opId = tracker.startOperation('export-test');
      tracker.endOperation(opId);
      tracker.recordApiCall('export-model', 1500, 200, true);
    });

    it('should export data in JSON format', () => {
      const jsonData = tracker.exportData('json');
      const data = JSON.parse(jsonData);
      
      expect(data.sessionId).toBe('test-session-123');
      expect(data.exportTimestamp).toBeDefined();
      expect(data.metrics).toBeDefined();
      expect(data.operations).toHaveLength(1);
      expect(data.apiCalls).toHaveLength(1);
    });

    it('should export data in CSV format', () => {
      const csvData = tracker.exportData('csv');
      
      expect(csvData).toContain('API Calls');
      expect(csvData).toContain('Operations');
      expect(csvData).toContain('export-model');
      expect(csvData).toContain('export-test');
    });

    it('should export summary format', () => {
      const summary = tracker.exportData('summary');
      
      expect(summary).toContain('Performance Summary');
      expect(summary).toContain('test-session-123');
      expect(summary).toContain('SESSION OVERVIEW');
      expect(summary).toContain('OPERATIONS');
      expect(summary).toContain('API CALLS');
    });

    it('should throw error for unsupported format', () => {
      expect(() => tracker.exportData('xml')).toThrow('Unsupported export format: xml');
    });
  });

  describe('Reset and Cleanup', () => {
    it('should reset all metrics and data', () => {
      // Add some test data
      const opId = tracker.startOperation('reset-test');
      tracker.endOperation(opId);
      tracker.recordApiCall('reset-model', 1000, 100, true);

      // Verify data exists
      let metrics = tracker.getMetrics();
      expect(metrics.operations.total).toBe(1);
      expect(metrics.api.totalCalls).toBe(1);

      // Reset
      tracker.reset();

      // Verify data is cleared
      metrics = tracker.getMetrics();
      expect(metrics.operations.total).toBe(0);
      expect(metrics.api.totalCalls).toBe(0);
    });

    it('should optionally stop system monitoring on reset', () => {
      expect(tracker.isSystemMonitoring()).toBe(true);
      
      tracker.reset(false);
      expect(tracker.isSystemMonitoring()).toBe(false);
    });

    it('should finalize properly', () => {
      tracker.finalize();
      
      expect(tracker.isSystemMonitoring()).toBe(false);
      expect(tracker.metrics.session.endTime).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid operation names', () => {
      expect(() => tracker.startOperation('')).toThrow('Operation name must be a non-empty string');
      expect(() => tracker.startOperation(null)).toThrow('Operation name must be a non-empty string');
    });

    it('should handle memory monitoring gracefully without global.gc', () => {
      delete global.gc;
      
      expect(() => tracker.startMemoryMonitoring()).not.toThrow();
    });

    it('should handle system monitoring errors gracefully', () => {
      // Mock os.cpus to throw error
      const os = require('os');
      os.cpus.mockImplementation(() => {
        throw new Error('CPU info unavailable');
      });

      expect(() => tracker._takeSystemSnapshot()).not.toThrow();
    });
  });

  describe('Performance Edge Cases', () => {
    it('should handle rapid operation start/stop cycles', () => {
      const operations = [];
      
      // Start many operations rapidly
      for (let i = 0; i < 100; i++) {
        operations.push(tracker.startOperation(`rapid-op-${i}`));
      }
      
      // End them all
      operations.forEach(opId => tracker.endOperation(opId));
      
      const metrics = tracker.getMetrics();
      expect(metrics.operations.total).toBe(100);
      expect(metrics.operations.completed).toBe(100);
    });

    it('should handle large numbers of API calls', () => {
      // Record many API calls
      for (let i = 0; i < 1000; i++) {
        tracker.recordApiCall(`model-${i % 5}`, 1000 + i, 100 + i, i % 10 !== 0);
      }
      
      const apiStats = tracker.getApiStatistics();
      expect(apiStats.overall.totalCalls).toBe(1000);
      expect(Object.keys(apiStats.byModel)).toHaveLength(5);
    });

    it('should limit snapshot history to prevent memory growth', () => {
      // Take many memory snapshots
      for (let i = 0; i < 150; i++) {
        tracker._takeMemorySnapshot();
      }
      
      expect(tracker.memorySnapshots.length).toBeLessThanOrEqual(50);
    });
  });
});