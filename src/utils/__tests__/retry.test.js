/**
 * Tests for retry utility and circuit breaker
 */

const {
  RetryUtility,
  CircuitBreaker,
  CIRCUIT_STATES,
  DEFAULT_RETRY_CONFIG,
  withRetry
} = require('../retry');
const { APIError, TimeoutError, AuthenticationError } = require('../errors');

describe('Retry Utility System', () => {
  describe('CircuitBreaker', () => {
    let circuitBreaker;

    beforeEach(() => {
      circuitBreaker = new CircuitBreaker({
        failureThreshold: 3,
        recoveryTimeout: 1000
      });
    });

    test('should start in CLOSED state', () => {
      expect(circuitBreaker.state).toBe(CIRCUIT_STATES.CLOSED);
      expect(circuitBreaker.canExecute()).toBe(true);
    });

    test('should open after failure threshold', () => {
      // Record failures up to threshold
      for (let i = 0; i < 3; i++) {
        circuitBreaker.recordFailure();
      }
      
      expect(circuitBreaker.state).toBe(CIRCUIT_STATES.OPEN);
      expect(circuitBreaker.canExecute()).toBe(false);
    });

    test('should transition to HALF_OPEN after recovery timeout', async () => {
      // Open the circuit
      for (let i = 0; i < 3; i++) {
        circuitBreaker.recordFailure();
      }
      
      expect(circuitBreaker.state).toBe(CIRCUIT_STATES.OPEN);
      
      // Wait for recovery timeout
      await new Promise(resolve => setTimeout(resolve, 1100));
      
      // Should allow execution and transition to HALF_OPEN
      expect(circuitBreaker.canExecute()).toBe(true);
      expect(circuitBreaker.state).toBe(CIRCUIT_STATES.HALF_OPEN);
    });

    test('should close after successful executions in HALF_OPEN', () => {
      // Open the circuit
      for (let i = 0; i < 3; i++) {
        circuitBreaker.recordFailure();
      }
      
      // Manually set to HALF_OPEN for testing
      circuitBreaker.state = CIRCUIT_STATES.HALF_OPEN;
      
      // Record successful executions
      circuitBreaker.recordSuccess();
      circuitBreaker.recordSuccess();
      
      expect(circuitBreaker.state).toBe(CIRCUIT_STATES.CLOSED);
    });

    test('should return to OPEN on failure in HALF_OPEN', () => {
      circuitBreaker.state = CIRCUIT_STATES.HALF_OPEN;
      
      circuitBreaker.recordFailure();
      
      expect(circuitBreaker.state).toBe(CIRCUIT_STATES.OPEN);
    });

    test('should track statistics correctly', () => {
      circuitBreaker.canExecute(); // totalRequests++
      circuitBreaker.recordSuccess(); // successfulRequests++
      circuitBreaker.recordFailure(); // failedRequests++
      
      const stats = circuitBreaker.getStats();
      
      expect(stats.totalRequests).toBe(1);
      expect(stats.successfulRequests).toBe(1);
      expect(stats.failedRequests).toBe(1);
      expect(stats.successRate).toBe(100); // 1/1 * 100
    });

    test('should reset to initial state', () => {
      // Open the circuit
      for (let i = 0; i < 3; i++) {
        circuitBreaker.recordFailure();
      }
      
      circuitBreaker.reset();
      
      expect(circuitBreaker.state).toBe(CIRCUIT_STATES.CLOSED);
      expect(circuitBreaker.failureCount).toBe(0);
      expect(circuitBreaker.canExecute()).toBe(true);
    });
  });

  describe('RetryUtility', () => {
    let retryUtility;

    beforeEach(() => {
      retryUtility = new RetryUtility({
        maxAttempts: 3,
        baseDelay: 100,
        maxDelay: 1000,
        backoffMultiplier: 2,
        jitterFactor: 0
      });
    });

    test('should calculate exponential backoff delay', () => {
      expect(retryUtility.calculateDelay(0)).toBe(100);
      expect(retryUtility.calculateDelay(1)).toBe(200);
      expect(retryUtility.calculateDelay(2)).toBe(400);
    });

    test('should respect maximum delay', () => {
      const delay = retryUtility.calculateDelay(10); // Would be 102400 without cap
      expect(delay).toBe(1000);
    });

    test('should identify retryable errors', () => {
      const apiError = new APIError('Network error');
      const timeoutError = new TimeoutError('Timeout');
      const authError = new AuthenticationError('Unauthorized');
      
      expect(retryUtility.isRetryableError(apiError)).toBe(true);
      expect(retryUtility.isRetryableError(timeoutError)).toBe(true);
      expect(retryUtility.isRetryableError(authError)).toBe(false);
    });

    test('should succeed on first attempt', async () => {
      const mockFn = jest.fn().mockResolvedValue('success');
      
      const result = await retryUtility.executeWithRetry(mockFn);
      
      expect(result).toBe('success');
      expect(mockFn).toHaveBeenCalledTimes(1);
    });

    test('should retry on retryable errors', async () => {
      const mockFn = jest.fn()
        .mockRejectedValueOnce(new APIError('Network error'))
        .mockRejectedValueOnce(new TimeoutError('Timeout'))
        .mockResolvedValue('success');
      
      const result = await retryUtility.executeWithRetry(mockFn);
      
      expect(result).toBe('success');
      expect(mockFn).toHaveBeenCalledTimes(3);
    });

    test('should not retry on non-retryable errors', async () => {
      const mockFn = jest.fn().mockRejectedValue(new AuthenticationError('Unauthorized'));
      
      await expect(retryUtility.executeWithRetry(mockFn)).rejects.toThrow('Unauthorized');
      expect(mockFn).toHaveBeenCalledTimes(1);
    });

    test('should fail after max attempts', async () => {
      const mockFn = jest.fn().mockRejectedValue(new APIError('Network error'));
      
      await expect(retryUtility.executeWithRetry(mockFn)).rejects.toThrow('Network error');
      expect(mockFn).toHaveBeenCalledTimes(3);
    });

    test('should respect circuit breaker', async () => {
      const mockFn = jest.fn().mockRejectedValue(new APIError('Network error'));
      
      // Fail enough times to open circuit breaker
      for (let i = 0; i < 5; i++) {
        try {
          await retryUtility.executeWithRetry(mockFn, { operationId: 'test' });
        } catch (error) {
          // Expected to fail
        }
      }
      
      // Next call should fail immediately due to circuit breaker
      await expect(
        retryUtility.executeWithRetry(mockFn, { operationId: 'test' })
      ).rejects.toThrow('Circuit breaker is open');
    });

    test('should handle timeout', async () => {
      const mockFn = jest.fn().mockImplementation(() => 
        new Promise(resolve => setTimeout(resolve, 200))
      );
      
      await expect(
        retryUtility.executeWithRetry(mockFn, { timeoutMs: 100 })
      ).rejects.toThrow('timed out');
    });

    test('should provide statistics', () => {
      const stats = retryUtility.getStats();
      expect(typeof stats).toBe('object');
    });

    test('should reset circuit breakers', () => {
      retryUtility.resetCircuitBreakers();
      // Should not throw and should reset internal state
      expect(() => retryUtility.resetCircuitBreakers()).not.toThrow();
    });
  });

  describe('withRetry convenience function', () => {
    test('should work with default configuration', async () => {
      const mockFn = jest.fn()
        .mockRejectedValueOnce(new APIError('Network error'))
        .mockResolvedValue('success');
      
      const result = await withRetry(mockFn, { maxAttempts: 2, baseDelay: 10 });
      
      expect(result).toBe('success');
      expect(mockFn).toHaveBeenCalledTimes(2);
    });

    test('should handle custom configuration', async () => {
      const mockFn = jest.fn().mockResolvedValue('success');
      
      const result = await withRetry(mockFn, {
        maxAttempts: 1,
        retryableErrors: ['APIError']
      });
      
      expect(result).toBe('success');
      expect(mockFn).toHaveBeenCalledTimes(1);
    });
  });

  describe('Integration tests', () => {
    test('should work with async functions that throw', async () => {
      const mockFn = jest.fn().mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 10));
        const error = new Error('Async error');
        error.statusCode = 500; // Make it retryable
        throw error;
      });
      
      await expect(withRetry(mockFn, { maxAttempts: 2, baseDelay: 10 }))
        .rejects.toThrow('Async error');
      
      expect(mockFn).toHaveBeenCalledTimes(2);
    });
  });
});