/**
 * Comprehensive tests for ConversationSimulator
 * Tests cover conversation orchestration, fact injection, and LLM interactions
 */

const ConversationSimulator = require('../conversationSimulator');

// Mock all dependencies
jest.mock('../../models/llmModel', () => {
  return jest.fn();
});

jest.mock('../prompts', () => ({
  prompts: {
    ASSISTANT_PROMPT: 'You are a helpful assistant',
    USER_PROMPT: 'You are a human user named <PERSON>'
  }
}));

jest.mock('../../config', () => ({
  ConfigManager: jest.fn()
}));

jest.mock('../logger', () => ({
  defaultLogger: {
    child: jest.fn(() => ({
      info: jest.fn(),
      debug: jest.fn(),
      warn: jest.fn(),
      error: jest.fn()
    })),
    info: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
  }
}));

const LLMModel = require('../../models/llmModel');
const { ConfigManager } = require('../../config');

describe('ConversationSimulator', () => {
  let mockMemory;
  let mockConfig;
  let mockUserModel;
  let mockAssistantModel;
  let simulator;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Mock memory interface
    mockMemory = {
      addMessage: jest.fn().mockResolvedValue(undefined),
      getMemoryContext: jest.fn().mockResolvedValue('Previous conversation context')
    };

    // Mock configuration
    mockConfig = {
      get: jest.fn((key) => {
        const config = {
          'models.user.name': 'gpt-3.5-turbo',
          'models.assistant.name': 'gpt-4'
        };
        return config[key];
      }),
      isVerboseLogging: jest.fn().mockReturnValue(false)
    };

    // Mock LLM models
    mockUserModel = {
      generateResponse: jest.fn(),
      setPerformanceTracker: jest.fn()
    };
    mockAssistantModel = {
      generateResponse: jest.fn(),
      setPerformanceTracker: jest.fn()
    };

    ConfigManager.mockImplementation(() => mockConfig);
    LLMModel.mockImplementation((modelName) => {
      return modelName === 'gpt-3.5-turbo' ? mockUserModel : mockAssistantModel;
    });

    // Mock console.log to avoid noise in tests
    jest.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    // Restore console.log
    jest.restoreAllMocks();
  });

  describe('Constructor', () => {
    test('should create ConversationSimulator with valid parameters', () => {
      simulator = new ConversationSimulator(mockMemory, 'session-123');

      expect(simulator.memory).toBe(mockMemory);
      expect(simulator.sessionId).toBe('session-123');
      expect(simulator.conversationLog).toEqual([]);
      expect(simulator.memoryContexts).toEqual([]);
      expect(LLMModel).toHaveBeenCalledTimes(2);
    });

    test('should throw error with invalid memory', () => {
      expect(() => {
        new ConversationSimulator(null, 'session-123');
      }).toThrow('Memory instance is required');
    });

    test('should throw error with invalid sessionId', () => {
      expect(() => {
        new ConversationSimulator(mockMemory, '');
      }).toThrow('Session ID must be a non-empty string');

      expect(() => {
        new ConversationSimulator(mockMemory, null);
      }).toThrow('Session ID must be a non-empty string');
    });

    test('should use custom config when provided', () => {
      const customConfig = {
        get: jest.fn().mockReturnValue('custom-model'),
        isVerboseLogging: jest.fn().mockReturnValue(true)
      };

      simulator = new ConversationSimulator(mockMemory, 'session-123', customConfig);

      expect(simulator.config).toBe(customConfig);
      expect(customConfig.get).toHaveBeenCalledWith('models.user.name');
      expect(customConfig.get).toHaveBeenCalledWith('models.assistant.name');
    });
  });

  describe('setPerformanceTracker', () => {
    beforeEach(() => {
      simulator = new ConversationSimulator(mockMemory, 'session-123');
    });

    test('should set performance tracker and pass to models', () => {
      const mockTracker = { track: jest.fn() };

      simulator.setPerformanceTracker(mockTracker);

      expect(simulator.performanceTracker).toBe(mockTracker);
      expect(mockUserModel.setPerformanceTracker).toHaveBeenCalledWith(mockTracker);
      expect(mockAssistantModel.setPerformanceTracker).toHaveBeenCalledWith(mockTracker);
    });

    test('should handle models without setPerformanceTracker method', () => {
      delete mockUserModel.setPerformanceTracker;
      delete mockAssistantModel.setPerformanceTracker;

      const mockTracker = { track: jest.fn() };

      expect(() => {
        simulator.setPerformanceTracker(mockTracker);
      }).not.toThrow();

      expect(simulator.performanceTracker).toBe(mockTracker);
    });
  });

  describe('addUserMessage', () => {
    beforeEach(() => {
      simulator = new ConversationSimulator(mockMemory, 'session-123');
    });

    test('should add user message to conversation log and memory', async () => {
      const content = 'Hello, how are you?';

      await simulator.addUserMessage(content);

      expect(simulator.conversationLog).toHaveLength(1);
      expect(simulator.conversationLog[0]).toEqual({
        role: 'user',
        content: content
      });
      expect(mockMemory.addMessage).toHaveBeenCalledWith({
        role: 'user',
        content: content
      });
    });

    test('should store memory context when verbose logging is enabled', async () => {
      mockConfig.isVerboseLogging.mockReturnValue(true);
      simulator = new ConversationSimulator(mockMemory, 'session-123');

      await simulator.addUserMessage('Test message');

      expect(mockMemory.getMemoryContext).toHaveBeenCalled();
      expect(simulator.memoryContexts).toHaveLength(1);
      expect(simulator.memoryContexts[0]).toBe('Previous conversation context');
    });

    test('should not store memory context when verbose logging is disabled', async () => {
      mockConfig.isVerboseLogging.mockReturnValue(false);
      simulator = new ConversationSimulator(mockMemory, 'session-123');

      await simulator.addUserMessage('Test message');

      expect(simulator.memoryContexts).toHaveLength(0);
    });
  });

  describe('generateAssistantResponse', () => {
    beforeEach(() => {
      simulator = new ConversationSimulator(mockMemory, 'session-123');
      mockAssistantModel.generateResponse.mockResolvedValue('Assistant response');
    });

    test('should generate assistant response with memory context', async () => {
      simulator.conversationLog = [
        { role: 'user', content: 'Hello' }
      ];

      const response = await simulator.generateAssistantResponse();

      expect(response).toBe('Assistant response');
      expect(mockMemory.getMemoryContext).toHaveBeenCalled();
      expect(mockAssistantModel.generateResponse).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({ role: 'system' }),
          expect.objectContaining({ content: expect.stringContaining('Previous conversation context') }),
          expect.objectContaining({ role: 'user', content: 'Hello' })
        ])
      );
    });

    test('should add assistant response to conversation log and memory', async () => {
      const response = await simulator.generateAssistantResponse();

      expect(simulator.conversationLog).toHaveLength(1);
      expect(simulator.conversationLog[0]).toEqual({
        role: 'assistant',
        content: 'Assistant response'
      });
      expect(mockMemory.addMessage).toHaveBeenCalledWith({
        role: 'assistant',
        content: 'Assistant response'
      });
    });

    test('should handle empty conversation log', async () => {
      const response = await simulator.generateAssistantResponse();

      expect(response).toBe('Assistant response');
      expect(mockAssistantModel.generateResponse).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({ role: 'system' })
        ])
      );
    });
  });

  describe('generateUserResponse', () => {
    beforeEach(() => {
      simulator = new ConversationSimulator(mockMemory, 'session-123');
      mockUserModel.generateResponse.mockResolvedValue('User response');
    });

    test('should generate user response with memory context', async () => {
      simulator.conversationLog = [
        { role: 'assistant', content: 'How can I help you?' }
      ];

      const response = await simulator.generateUserResponse();

      expect(response).toBe('User response');
      expect(mockMemory.getMemoryContext).toHaveBeenCalled();
      expect(mockUserModel.generateResponse).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({ role: 'system' }),
          expect.objectContaining({ content: expect.stringContaining('Previous conversation context') })
        ])
      );
    });

    test('should detect and retry echo responses', async () => {
      simulator.conversationLog = [
        { role: 'assistant', content: 'Hello there!' }
      ];

      mockUserModel.generateResponse
        .mockResolvedValueOnce('Hello there!') // Echo response
        .mockResolvedValueOnce('Hi, how are you?'); // Good response

      const response = await simulator.generateUserResponse();

      expect(response).toBe('Hi, how are you?');
      expect(mockUserModel.generateResponse).toHaveBeenCalledTimes(2);
    });

    test('should detect and retry assistant-like responses', async () => {
      mockUserModel.generateResponse
        .mockResolvedValueOnce('How can I assist you today?') // Assistant-like
        .mockResolvedValueOnce('I need help with something'); // Good response

      const response = await simulator.generateUserResponse();

      expect(response).toBe('I need help with something');
      expect(mockUserModel.generateResponse).toHaveBeenCalledTimes(2);
    });

    test('should use fallback response after max retry attempts', async () => {
      mockUserModel.generateResponse.mockResolvedValue('How can I help you?'); // Always assistant-like

      const response = await simulator.generateUserResponse();

      expect(mockUserModel.generateResponse).toHaveBeenCalledTimes(4); // Initial + 3 retries
      expect(response).toMatch(/That's interesting|I'm not sure|Thanks for|I see|Hmm/);
    });

    test('should add user response to conversation log and memory', async () => {
      const response = await simulator.generateUserResponse();

      expect(simulator.conversationLog).toHaveLength(1);
      expect(simulator.conversationLog[0]).toEqual({
        role: 'user',
        content: 'User response'
      });
      expect(mockMemory.addMessage).toHaveBeenCalledWith({
        role: 'user',
        content: 'User response'
      });
    });
  });

  describe('Response Quality Detection', () => {
    beforeEach(() => {
      simulator = new ConversationSimulator(mockMemory, 'session-123');
    });

    describe('isEchoResponse', () => {
      test('should detect exact echo', () => {
        const result = simulator.isEchoResponse('Hello world', 'Hello world');
        expect(result).toBe(true);
      });

      test('should detect echo with different case', () => {
        const result = simulator.isEchoResponse('HELLO WORLD', 'hello world');
        expect(result).toBe(true);
      });

      test('should detect echo with prefixes', () => {
        const result = simulator.isEchoResponse('Assistant: Hello world', 'Hello world');
        expect(result).toBe(true);
      });

      test('should detect partial echo', () => {
        const result = simulator.isEchoResponse('This is a very long message that contains significant content', 'This is a very long message');
        expect(result).toBe(true);
      });

      test('should not detect non-echo responses', () => {
        const result = simulator.isEchoResponse('That sounds interesting', 'Hello world');
        expect(result).toBe(false);
      });

      test('should handle empty inputs', () => {
        expect(simulator.isEchoResponse('', 'test')).toBe(false);
        expect(simulator.isEchoResponse('test', '')).toBe(false);
        expect(simulator.isEchoResponse(null, 'test')).toBe(false);
      });
    });

    describe('looksLikeAssistantResponse', () => {
      test('should detect assistant-like phrases', () => {
        expect(simulator.looksLikeAssistantResponse('How can I assist you?')).toBe(true);
        expect(simulator.looksLikeAssistantResponse('How can I help you?')).toBe(true);
        expect(simulator.looksLikeAssistantResponse("I'm here to help")).toBe(true);
        expect(simulator.looksLikeAssistantResponse("I'm an AI assistant")).toBe(true);
        expect(simulator.looksLikeAssistantResponse('As an AI, I can help')).toBe(true);
      });

      test('should detect role prefixes', () => {
        expect(simulator.looksLikeAssistantResponse('User: Hello there')).toBe(true);
        expect(simulator.looksLikeAssistantResponse('Assistant: How are you?')).toBe(true);
        expect(simulator.looksLikeAssistantResponse('[AI]: I can help')).toBe(true);
      });

      test('should not detect normal user responses', () => {
        expect(simulator.looksLikeAssistantResponse('That sounds great!')).toBe(false);
        expect(simulator.looksLikeAssistantResponse('I need help with something')).toBe(false);
        expect(simulator.looksLikeAssistantResponse('What do you think about that?')).toBe(false);
      });

      test('should be case insensitive', () => {
        expect(simulator.looksLikeAssistantResponse('HOW CAN I ASSIST YOU?')).toBe(true);
        expect(simulator.looksLikeAssistantResponse('how can i help you?')).toBe(true);
      });
    });
  });

  describe('runConversation', () => {
    beforeEach(() => {
      simulator = new ConversationSimulator(mockMemory, 'session-123');
      mockAssistantModel.generateResponse.mockResolvedValue('Assistant response');
      mockUserModel.generateResponse.mockResolvedValue('User response');
    });

    test('should run complete conversation with fact injection', async () => {
      const facts = [
        { id: 1, fact: 'Paris is the capital of France', question: 'What is the capital of France?', answer: 'Paris' },
        { id: 2, fact: 'Water boils at 100°C', question: 'At what temperature does water boil?', answer: '100°C' }
      ];

      const result = await simulator.runConversation(facts, 2);

      expect(result.conversationLog.length).toBeGreaterThan(0);
      expect(result.usedFacts).toHaveLength(2);
      expect(result.usedFacts).toEqual(facts);

      // Verify facts were injected as user messages
      const userMessages = result.conversationLog.filter(msg => msg.role === 'user');
      expect(userMessages.some(msg => msg.content === facts[0].fact)).toBe(true);
      expect(userMessages.some(msg => msg.content === facts[1].fact)).toBe(true);
    });

    test('should handle single fact conversation', async () => {
      const facts = [
        { id: 1, fact: 'Test fact', question: 'Test question?', answer: 'Test answer' }
      ];

      const result = await simulator.runConversation(facts, 1);

      expect(result.conversationLog.length).toBeGreaterThan(0);
      expect(result.usedFacts).toHaveLength(1);
      expect(result.usedFacts[0]).toEqual(facts[0]);
    });

    test('should respect messagesBetweenFacts parameter', async () => {
      const facts = [
        { id: 1, fact: 'Fact 1', question: 'Q1?', answer: 'A1' },
        { id: 2, fact: 'Fact 2', question: 'Q2?', answer: 'A2' }
      ];

      const result = await simulator.runConversation(facts, 3);

      // Should have at least: fact1, assistant_response, user_response, user_response, user_response, fact2, assistant_response, etc.
      expect(result.conversationLog.length).toBeGreaterThan(6);
    });

    test('should integrate with performance tracker', async () => {
      const mockTracker = {
        startOperation: jest.fn().mockReturnValue('operation-id'),
        endOperation: jest.fn()
      };
      simulator.setPerformanceTracker(mockTracker);

      const facts = [
        { id: 1, fact: 'Test fact', question: 'Test question?', answer: 'Test answer' }
      ];

      await simulator.runConversation(facts, 1);

      expect(mockTracker.startOperation).toHaveBeenCalledWith('fact-injection', expect.any(Object));
      expect(mockTracker.endOperation).toHaveBeenCalledWith('operation-id', expect.any(Object));
    });

    test('should handle memory errors gracefully', async () => {
      mockMemory.addMessage.mockRejectedValue(new Error('Memory error'));

      const facts = [
        { id: 1, fact: 'Test fact', question: 'Test question?', answer: 'Test answer' }
      ];

      await expect(simulator.runConversation(facts, 1)).rejects.toThrow('Memory error');
    });

    test('should handle LLM API errors gracefully', async () => {
      mockAssistantModel.generateResponse.mockRejectedValue(new Error('API error'));

      const facts = [
        { id: 1, fact: 'Test fact', question: 'Test question?', answer: 'Test answer' }
      ];

      await expect(simulator.runConversation(facts, 1)).rejects.toThrow('API error');
    });
  });

  describe('Progress Tracking', () => {
    beforeEach(() => {
      simulator = new ConversationSimulator(mockMemory, 'session-123');
    });

    describe('createProgressBar', () => {
      test('should create progress bar for different percentages', () => {
        expect(simulator.createProgressBar(0)).toBe('[░░░░░░░░░░░░░░░░░░░░]   0%');
        expect(simulator.createProgressBar(50)).toBe('[██████████░░░░░░░░░░]  50%');
        expect(simulator.createProgressBar(100)).toBe('[████████████████████] 100%');
      });

      test('should handle edge cases', () => {
        expect(simulator.createProgressBar(1)).toBe('[░░░░░░░░░░░░░░░░░░░░]   1%');
        expect(simulator.createProgressBar(99)).toBe('[███████████████████░]  99%');
      });

      test('should round percentages correctly', () => {
        expect(simulator.createProgressBar(33)).toBe('[██████░░░░░░░░░░░░░░]  33%');
        expect(simulator.createProgressBar(67)).toBe('[█████████████░░░░░░░]  67%');
      });
    });
  });

  describe('Error Handling and Edge Cases', () => {
    beforeEach(() => {
      simulator = new ConversationSimulator(mockMemory, 'session-123');
    });

    test('should handle empty facts array', async () => {
      const result = await simulator.runConversation([], 1);

      expect(result.conversationLog).toHaveLength(0);
      expect(result.usedFacts).toHaveLength(0);
    });

    test('should handle very large messagesBetweenFacts', async () => {
      mockUserModel.generateResponse.mockResolvedValue('User response');
      mockAssistantModel.generateResponse.mockResolvedValue('Assistant response');

      const facts = [
        { id: 1, fact: 'Test fact', question: 'Test question?', answer: 'Test answer' }
      ];

      const result = await simulator.runConversation(facts, 100);

      expect(result.conversationLog.length).toBeGreaterThan(100);
      expect(result.usedFacts).toHaveLength(1);
    });

    test('should handle memory context retrieval errors', async () => {
      mockMemory.getMemoryContext.mockRejectedValue(new Error('Context error'));

      await expect(simulator.generateAssistantResponse()).rejects.toThrow('Context error');
    });

    test('should handle concurrent operations', async () => {
      const facts = [
        { id: 1, fact: 'Fact 1', question: 'Q1?', answer: 'A1' },
        { id: 2, fact: 'Fact 2', question: 'Q2?', answer: 'A2' }
      ];

      // Run multiple conversations concurrently
      const promises = [
        simulator.runConversation(facts, 1),
        simulator.runConversation(facts, 1)
      ];

      const results = await Promise.all(promises);

      expect(results).toHaveLength(2);
      results.forEach(result => {
        expect(result.conversationLog.length).toBeGreaterThan(0);
        expect(result.usedFacts.length).toBeGreaterThan(0);
      });
    });
  });

  describe('Verbose Logging Integration', () => {
    test('should store memory contexts when verbose logging is enabled', async () => {
      mockConfig.isVerboseLogging.mockReturnValue(true);
      simulator = new ConversationSimulator(mockMemory, 'session-123');
      mockAssistantModel.generateResponse.mockResolvedValue('Assistant response');

      await simulator.generateAssistantResponse();

      expect(mockMemory.getMemoryContext).toHaveBeenCalledTimes(2); // Once for generation, once for storage
      expect(simulator.memoryContexts).toHaveLength(1);
    });

    test('should not store memory contexts when verbose logging is disabled', async () => {
      mockConfig.isVerboseLogging.mockReturnValue(false);
      simulator = new ConversationSimulator(mockMemory, 'session-123');
      mockAssistantModel.generateResponse.mockResolvedValue('Assistant response');

      await simulator.generateAssistantResponse();

      expect(mockMemory.getMemoryContext).toHaveBeenCalledTimes(1); // Only for generation
      expect(simulator.memoryContexts).toHaveLength(0);
    });
  });
});