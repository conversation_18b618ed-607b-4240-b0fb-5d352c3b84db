/**
 * Tests for the centralized logging infrastructure
 */

const { 
  <PERSON><PERSON>, 
  SessionContext, 
  PerformanceTracker, 
  LOG_LEVELS, 
  defaultLogger 
} = require('../logger');

// Mock console methods
const originalConsole = { ...console };
beforeEach(() => {
  console.log = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
});

afterEach(() => {
  Object.assign(console, originalConsole);
});

describe('Logger', () => {
  describe('Basic logging functionality', () => {
    test('should log messages at appropriate levels', () => {
      const logger = new Logger({ level: 'DEBUG' });
      
      logger.debug('Debug message');
      logger.info('Info message');
      logger.warn('Warning message');
      logger.error('Error message');
      
      expect(console.log).toHaveBeenCalledTimes(2); // debug and info
      expect(console.warn).toHaveBeenCalledTimes(1);
      expect(console.error).toHaveBeenCalledTimes(1);
    });

    test('should respect log level filtering', () => {
      const logger = new Logger({ level: 'WARN' });
      
      logger.debug('Debug message');
      logger.info('Info message');
      logger.warn('Warning message');
      logger.error('Error message');
      
      expect(console.log).not.toHaveBeenCalled();
      expect(console.warn).toHaveBeenCalledTimes(1);
      expect(console.error).toHaveBeenCalledTimes(1);
    });

    test('should include context in log messages', () => {
      const logger = new Logger({ level: 'INFO', environment: 'development' });
      const context = { userId: '123', operation: 'test' };
      
      logger.info('Test message', context);
      
      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('Test message')
      );
      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('Context:')
      );
    });
  });

  describe('Environment-specific formatting', () => {
    test('should use JSON format in production', () => {
      const logger = new Logger({ 
        level: 'INFO', 
        environment: 'production' 
      });
      
      logger.info('Test message', { key: 'value' });
      
      const logCall = console.log.mock.calls[0][0];
      expect(() => JSON.parse(logCall)).not.toThrow();
      
      const parsed = JSON.parse(logCall);
      expect(parsed.level).toBe('INFO');
      expect(parsed.message).toBe('Test message');
      expect(parsed.context.key).toBe('value');
    });

    test('should use human-readable format in development', () => {
      const logger = new Logger({ 
        level: 'INFO', 
        environment: 'development',
        enableColors: false 
      });
      
      logger.info('Test message');
      
      const logCall = console.log.mock.calls[0][0];
      expect(logCall).toMatch(/\[\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z\] \[INFO\] Test message/);
    });

    test('should include colors in development when enabled', () => {
      const logger = new Logger({ 
        level: 'INFO', 
        environment: 'development',
        enableColors: true 
      });
      
      logger.info('Test message');
      
      const logCall = console.log.mock.calls[0][0];
      expect(logCall).toContain('\x1b[32m'); // Green color for INFO
      expect(logCall).toContain('\x1b[0m');  // Reset color
    });
  });

  describe('Performance tracking', () => {
    test('should track operation timing', async () => {
      const logger = new Logger({ level: 'DEBUG' });
      
      const timerId = logger.startTimer('test-operation');
      await new Promise(resolve => setTimeout(resolve, 10));
      logger.endTimer(timerId, 'test-operation');
      
      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('Starting operation: test-operation')
      );
      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('Completed operation: test-operation')
      );
      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('Performance:')
      );
    });

    test('should handle timeOperation wrapper', async () => {
      const logger = new Logger({ level: 'DEBUG' });
      
      const result = await logger.timeOperation('async-test', async () => {
        await new Promise(resolve => setTimeout(resolve, 10));
        return 'success';
      });
      
      expect(result).toBe('success');
      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('Starting operation: async-test')
      );
      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('Completed operation: async-test')
      );
    });

    test('should handle errors in timeOperation', async () => {
      const logger = new Logger({ level: 'INFO' });
      
      await expect(
        logger.timeOperation('failing-test', async () => {
          throw new Error('Test error');
        })
      ).rejects.toThrow('Test error');
      
      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('success')
      );
    });

    test('should disable performance tracking when configured', () => {
      const logger = new Logger({ 
        level: 'INFO',
        enablePerformanceTracking: false 
      });
      
      const timerId = logger.startTimer('test-operation');
      expect(timerId).toBeNull();
    });
  });

  describe('Session context', () => {
    test('should create and use session context', () => {
      const logger = new Logger({ level: 'INFO' });
      const session = logger.createSession('test-session');
      
      expect(session.sessionId).toBe('test-session');
      expect(logger.sessionContext).toBe(session);
      
      logger.info('Test message');
      
      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('[Session: test-ses]')
      );
    });

    test('should track operations in session context', () => {
      const logger = new Logger({ level: 'INFO' });
      const session = logger.createSession();
      
      logger.info('First message');
      logger.warn('Second message');
      
      const summary = session.getSummary();
      expect(summary.operationCount).toBe(2);
      expect(summary.sessionId).toBe(session.sessionId);
    });

    test('should set session metadata', () => {
      const session = new SessionContext();
      session.setMetadata('testType', 'unit');
      session.setMetadata('version', '1.0');
      
      const summary = session.getSummary();
      expect(summary.metadata.testType).toBe('unit');
      expect(summary.metadata.version).toBe('1.0');
    });
  });

  describe('Context serialization', () => {
    test('should handle large context objects', () => {
      const logger = new Logger({ 
        level: 'INFO',
        maxContextSize: 50 
      });
      
      const largeContext = {
        data: 'x'.repeat(100),
        more: 'data'
      };
      
      logger.info('Test message', largeContext);
      
      const logCall = console.log.mock.calls[0][0];
      expect(logCall).toContain('[truncated]');
    });

    test('should handle context serialization errors', () => {
      const logger = new Logger({ level: 'INFO' });
      
      const circularContext = {};
      circularContext.self = circularContext;
      
      logger.info('Test message', circularContext);
      
      const logCall = console.log.mock.calls[0][0];
      expect(logCall).toContain('Context serialization error');
    });

    test('should optimize context for performance', () => {
      const logger = new Logger({ 
        level: 'INFO',
        enableExpensiveOperations: false 
      });
      
      const complexContext = {};
      for (let i = 0; i < 10; i++) {
        complexContext[`key${i}`] = `value${i}`;
      }
      
      logger.info('Test message', complexContext);
      
      const logCall = console.log.mock.calls[0][0];
      expect(logCall).toContain('more)');
    });
  });

  describe('Child loggers', () => {
    test('should create child logger with additional context', () => {
      const parentLogger = new Logger({ level: 'INFO' });
      const childLogger = parentLogger.child({ component: 'test' });
      
      childLogger.info('Child message', { extra: 'data' });
      
      const logCall = console.log.mock.calls[0][0];
      expect(logCall).toContain('component');
      expect(logCall).toContain('extra');
    });

    test('should inherit parent logger settings', () => {
      const parentLogger = new Logger({ 
        level: 'WARN',
        environment: 'production' 
      });
      const childLogger = parentLogger.child({ component: 'test' });
      
      childLogger.info('Should not log');
      childLogger.warn('Should log');
      
      expect(console.log).not.toHaveBeenCalled();
      expect(console.warn).toHaveBeenCalledTimes(1);
    });
  });

  describe('Default logger', () => {
    test('should export a default logger instance', () => {
      expect(defaultLogger).toBeInstanceOf(Logger);
      expect(typeof defaultLogger.info).toBe('function');
    });
  });
});

describe('PerformanceTracker', () => {
  test('should track operation performance', () => {
    const tracker = new PerformanceTracker();
    
    const operationId = tracker.startOperation();
    const metrics = tracker.endOperation(operationId);
    
    expect(metrics).toBeDefined();
    expect(metrics.duration).toBeGreaterThanOrEqual(0);
    expect(metrics.memoryDelta).toBeDefined();
  });

  test('should handle invalid operation IDs', () => {
    const tracker = new PerformanceTracker();
    
    const metrics = tracker.endOperation('invalid-id');
    expect(metrics).toBeNull();
  });

  test('should use custom operation IDs', () => {
    const tracker = new PerformanceTracker();
    
    const customId = 'custom-operation-123';
    const returnedId = tracker.startOperation(customId);
    expect(returnedId).toBe(customId);
    
    const metrics = tracker.endOperation(customId);
    expect(metrics).toBeDefined();
  });
});

describe('SessionContext', () => {
  test('should generate unique session IDs', () => {
    const session1 = new SessionContext();
    const session2 = new SessionContext();
    
    expect(session1.sessionId).not.toBe(session2.sessionId);
  });

  test('should use provided session ID', () => {
    const customId = 'custom-session-123';
    const session = new SessionContext(customId);
    
    expect(session.sessionId).toBe(customId);
  });

  test('should track operations with timestamps', () => {
    const session = new SessionContext();
    
    session.addOperation('test-op', { data: 'test' });
    
    expect(session.operations).toHaveLength(1);
    expect(session.operations[0].operation).toBe('test-op');
    expect(session.operations[0].details.data).toBe('test');
    expect(session.operations[0].timestamp).toBeInstanceOf(Date);
  });

  test('should calculate session duration', () => {
    const session = new SessionContext();
    
    // Wait a bit to ensure duration > 0
    setTimeout(() => {
      const summary = session.getSummary();
      expect(summary.duration).toBeGreaterThanOrEqual(0);
    }, 10);
  });
});