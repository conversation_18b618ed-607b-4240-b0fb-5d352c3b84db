/**
 * Retry Utility with Exponential Backoff and Jitter
 * 
 * Provides configurable retry logic for handling transient failures
 * with exponential backoff, jitter, and circuit breaker patterns.
 */

const { ErrorFactory, CircuitBreakerError } = require('./errors');

/**
 * Default retry configuration
 */
const DEFAULT_RETRY_CONFIG = {
  maxAttempts: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 30000, // 30 seconds
  backoffMultiplier: 2,
  jitterFactor: 0.1, // 10% jitter
  timeoutMs: 60000, // 60 seconds total timeout
  retryableErrors: ['APIError', 'TimeoutError', 'RateLimitError']
};

/**
 * Circuit breaker states
 */
const CIRCUIT_STATES = {
  CLOSED: 'CLOSED',     // Normal operation
  OPEN: 'OPEN',         // Failing, reject requests
  HALF_OPEN: 'HALF_OPEN' // Testing if service recovered
};

/**
 * Circuit Breaker implementation
 */
class CircuitBreaker {
  constructor(options = {}) {
    this.failureThreshold = options.failureThreshold || 5;
    this.recoveryTimeout = options.recoveryTimeout || 60000; // 1 minute
    this.monitoringPeriod = options.monitoringPeriod || 10000; // 10 seconds
    
    this.state = CIRCUIT_STATES.CLOSED;
    this.failureCount = 0;
    this.lastFailureTime = null;
    this.successCount = 0;
    
    // Statistics
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      circuitOpenCount: 0
    };
  }

  /**
   * Check if the circuit breaker allows the request
   * @returns {boolean} True if request is allowed
   */
  canExecute() {
    this.stats.totalRequests++;

    switch (this.state) {
      case CIRCUIT_STATES.CLOSED:
        return true;
        
      case CIRCUIT_STATES.OPEN:
        if (Date.now() - this.lastFailureTime >= this.recoveryTimeout) {
          this.state = CIRCUIT_STATES.HALF_OPEN;
          this.successCount = 0;
          return true;
        }
        this.stats.circuitOpenCount++;
        return false;
        
      case CIRCUIT_STATES.HALF_OPEN:
        return true;
        
      default:
        return false;
    }
  }

  /**
   * Record a successful execution
   */
  recordSuccess() {
    this.stats.successfulRequests++;
    this.failureCount = 0;
    
    if (this.state === CIRCUIT_STATES.HALF_OPEN) {
      this.successCount++;
      if (this.successCount >= 2) { // Require 2 successes to close
        this.state = CIRCUIT_STATES.CLOSED;
      }
    }
  }

  /**
   * Record a failed execution
   */
  recordFailure() {
    this.stats.failedRequests++;
    this.failureCount++;
    this.lastFailureTime = Date.now();
    
    if (this.state === CIRCUIT_STATES.HALF_OPEN) {
      this.state = CIRCUIT_STATES.OPEN;
    } else if (this.failureCount >= this.failureThreshold) {
      this.state = CIRCUIT_STATES.OPEN;
    }
  }

  /**
   * Get circuit breaker statistics
   * @returns {Object} Statistics object
   */
  getStats() {
    return {
      ...this.stats,
      state: this.state,
      failureCount: this.failureCount,
      successRate: this.stats.totalRequests > 0 
        ? (this.stats.successfulRequests / this.stats.totalRequests) * 100 
        : 0
    };
  }

  /**
   * Reset circuit breaker to initial state
   */
  reset() {
    this.state = CIRCUIT_STATES.CLOSED;
    this.failureCount = 0;
    this.lastFailureTime = null;
    this.successCount = 0;
  }
}

/**
 * Retry utility class with exponential backoff and jitter
 */
class RetryUtility {
  constructor(config = {}) {
    this.config = { ...DEFAULT_RETRY_CONFIG, ...config };
    this.circuitBreakers = new Map(); // Per-operation circuit breakers
  }

  /**
   * Get or create a circuit breaker for an operation
   * @param {string} operationId - Unique identifier for the operation
   * @returns {CircuitBreaker} Circuit breaker instance
   */
  getCircuitBreaker(operationId) {
    if (!this.circuitBreakers.has(operationId)) {
      this.circuitBreakers.set(operationId, new CircuitBreaker({
        failureThreshold: this.config.circuitBreakerThreshold || 5,
        recoveryTimeout: this.config.circuitBreakerTimeout || 60000
      }));
    }
    return this.circuitBreakers.get(operationId);
  }

  /**
   * Calculate delay with exponential backoff and jitter
   * @param {number} attempt - Current attempt number (0-based)
   * @param {number} baseDelay - Base delay in milliseconds
   * @returns {number} Delay in milliseconds
   */
  calculateDelay(attempt, baseDelay = null) {
    const base = baseDelay || this.config.baseDelay;
    const exponentialDelay = base * Math.pow(this.config.backoffMultiplier, attempt);
    
    // Apply maximum delay limit
    const cappedDelay = Math.min(exponentialDelay, this.config.maxDelay);
    
    // Add jitter to prevent thundering herd
    const jitter = cappedDelay * this.config.jitterFactor * (Math.random() - 0.5);
    
    return Math.max(0, cappedDelay + jitter);
  }

  /**
   * Check if an error is retryable
   * @param {Error} error - Error to check
   * @returns {boolean} True if error is retryable
   */
  isRetryableError(error) {
    // Check if error has isRetryable method
    if (typeof error.isRetryable === 'function') {
      return error.isRetryable();
    }

    // Check error type against retryable list
    return this.config.retryableErrors.includes(error.constructor.name);
  }

  /**
   * Execute a function with retry logic
   * @param {Function} fn - Function to execute
   * @param {Object} options - Retry options
   * @returns {Promise} Promise that resolves with the function result
   */
  async executeWithRetry(fn, options = {}) {
    const config = { ...this.config, ...options };
    const operationId = options.operationId || 'default';
    const circuitBreaker = this.getCircuitBreaker(operationId);
    
    const startTime = Date.now();
    let lastError = null;

    for (let attempt = 0; attempt < config.maxAttempts; attempt++) {
      // Check circuit breaker
      if (!circuitBreaker.canExecute()) {
        const error = new CircuitBreakerError(
          `Circuit breaker is open for operation: ${operationId}`,
          { operationId, attempt, circuitBreakerStats: circuitBreaker.getStats() }
        );
        throw error;
      }

      // Check total timeout
      if (Date.now() - startTime >= config.timeoutMs) {
        const timeoutError = ErrorFactory.createError(
          new Error(`Operation timed out after ${config.timeoutMs}ms`),
          { operationId, attempt, totalTime: Date.now() - startTime }
        );
        circuitBreaker.recordFailure();
        throw timeoutError;
      }

      try {
        // Create a timeout promise for the individual function execution
        const timeoutPromise = new Promise((_, reject) => {
          const remainingTime = config.timeoutMs - (Date.now() - startTime);
          setTimeout(() => {
            reject(new Error(`Operation timed out after ${config.timeoutMs}ms`));
          }, Math.max(remainingTime, 0));
        });

        // Race between the function execution and timeout
        const result = await Promise.race([fn(), timeoutPromise]);
        circuitBreaker.recordSuccess();
        
        // Log successful retry if this wasn't the first attempt
        if (attempt > 0 && config.logger) {
          config.logger.info(`Operation succeeded after ${attempt + 1} attempts`, {
            operationId,
            attempt: attempt + 1,
            totalTime: Date.now() - startTime
          });
        }
        
        return result;
      } catch (error) {
        // Convert to structured error
        const structuredError = ErrorFactory.createError(error, {
          operationId,
          attempt: attempt + 1,
          totalAttempts: config.maxAttempts
        });

        lastError = structuredError;
        circuitBreaker.recordFailure();

        // Log the error
        if (config.logger) {
          config.logger.warn(`Attempt ${attempt + 1} failed`, {
            operationId,
            error: structuredError.message,
            attempt: attempt + 1,
            willRetry: attempt < config.maxAttempts - 1 && this.isRetryableError(structuredError)
          });
        }

        // Check if we should retry
        if (attempt === config.maxAttempts - 1 || !this.isRetryableError(structuredError)) {
          throw structuredError;
        }

        // Calculate delay for next attempt
        const delay = this.calculateDelay(attempt, structuredError.getRetryDelay?.());
        
        if (config.logger) {
          config.logger.debug(`Waiting ${delay}ms before retry`, {
            operationId,
            attempt: attempt + 1,
            delay
          });
        }

        await this.sleep(delay);
      }
    }

    // This should never be reached, but just in case
    throw lastError || new Error('Retry loop completed without result');
  }

  /**
   * Sleep for specified milliseconds
   * @param {number} ms - Milliseconds to sleep
   * @returns {Promise} Promise that resolves after the delay
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get statistics for all circuit breakers
   * @returns {Object} Statistics for all operations
   */
  getStats() {
    const stats = {};
    for (const [operationId, circuitBreaker] of this.circuitBreakers) {
      stats[operationId] = circuitBreaker.getStats();
    }
    return stats;
  }

  /**
   * Reset all circuit breakers
   */
  resetCircuitBreakers() {
    for (const circuitBreaker of this.circuitBreakers.values()) {
      circuitBreaker.reset();
    }
  }
}

/**
 * Create a retry utility with default configuration
 * @param {Object} config - Configuration options
 * @returns {RetryUtility} Configured retry utility
 */
function createRetryUtility(config = {}) {
  return new RetryUtility(config);
}

/**
 * Convenience function to execute with retry
 * @param {Function} fn - Function to execute
 * @param {Object} options - Retry options
 * @returns {Promise} Promise that resolves with the function result
 */
async function withRetry(fn, options = {}) {
  const retryUtility = new RetryUtility(options);
  return retryUtility.executeWithRetry(fn, options);
}

module.exports = {
  RetryUtility,
  CircuitBreaker,
  CIRCUIT_STATES,
  DEFAULT_RETRY_CONFIG,
  createRetryUtility,
  withRetry
};