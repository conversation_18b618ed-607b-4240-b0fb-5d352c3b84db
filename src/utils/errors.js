/**
 * Custom Error Classes for LLM Memory Test Application
 * 
 * Provides a comprehensive error classification system for different failure types
 * with context preservation and structured error information.
 */

/**
 * Base error class for all application errors
 */
class BaseError extends Error {
  constructor(message, context = {}) {
    super(message);
    this.name = this.constructor.name;
    this.context = context;
    this.timestamp = new Date().toISOString();
    
    // Capture stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }

  /**
   * Get formatted error message with context
   * @returns {string} Formatted error message
   */
  getFormattedMessage() {
    let formatted = `[${this.name}] ${this.message}`;
    
    if (Object.keys(this.context).length > 0) {
      formatted += `\nContext: ${JSON.stringify(this.context, null, 2)}`;
    }
    
    formatted += `\nTimestamp: ${this.timestamp}`;
    return formatted;
  }

  /**
   * Check if this error is retryable
   * @returns {boolean} True if the error is retryable
   */
  isRetryable() {
    return false; // Default: not retryable
  }

  /**
   * Get the retry delay in milliseconds
   * @returns {number} Delay in milliseconds
   */
  getRetryDelay() {
    return 1000; // Default: 1 second
  }
}

/**
 * Configuration-related errors
 */
class ConfigurationError extends BaseError {
  constructor(message, context = {}) {
    super(message, context);
    this.category = 'CONFIGURATION';
  }

  isRetryable() {
    return false; // Configuration errors are not retryable
  }
}

/**
 * API-related errors with retry capabilities
 */
class APIError extends BaseError {
  constructor(message, context = {}) {
    super(message, context);
    this.category = 'API';
    this.statusCode = context.statusCode || null;
    this.retryAfter = context.retryAfter || null;
  }

  isRetryable() {
    // Retry on network errors, timeouts, and certain HTTP status codes
    const retryableStatusCodes = [408, 429, 500, 502, 503, 504];
    
    if (this.statusCode) {
      return retryableStatusCodes.includes(this.statusCode);
    }
    
    // Retry on network-related errors
    const retryableMessages = [
      'ECONNRESET',
      'ENOTFOUND',
      'ECONNREFUSED',
      'ETIMEDOUT',
      'timeout',
      'network'
    ];
    
    return retryableMessages.some(msg => 
      this.message.toLowerCase().includes(msg.toLowerCase())
    );
  }

  getRetryDelay() {
    // Use retry-after header if available
    if (this.retryAfter) {
      return this.retryAfter * 1000;
    }
    
    // Different delays based on status code
    switch (this.statusCode) {
      case 429: // Rate limited
        return 5000; // 5 seconds
      case 503: // Service unavailable
        return 10000; // 10 seconds
      default:
        return 2000; // 2 seconds
    }
  }
}

/**
 * Rate limiting specific error
 */
class RateLimitError extends APIError {
  constructor(message, context = {}) {
    super(message, context);
    this.category = 'RATE_LIMIT';
  }

  isRetryable() {
    return true;
  }

  getRetryDelay() {
    return this.retryAfter ? this.retryAfter * 1000 : 30000; // 30 seconds default
  }
}

/**
 * Authentication and authorization errors
 */
class AuthenticationError extends APIError {
  constructor(message, context = {}) {
    super(message, context);
    this.category = 'AUTHENTICATION';
  }

  isRetryable() {
    return false; // Auth errors are not retryable
  }
}

/**
 * Data validation and format errors
 */
class ValidationError extends BaseError {
  constructor(message, context = {}) {
    super(message, context);
    this.category = 'VALIDATION';
    this.field = context.field || null;
    this.value = context.value || null;
  }

  isRetryable() {
    return false; // Validation errors are not retryable
  }
}

/**
 * File system and I/O errors
 */
class FileSystemError extends BaseError {
  constructor(message, context = {}) {
    super(message, context);
    this.category = 'FILESYSTEM';
    this.path = context.path || null;
    this.operation = context.operation || null;
  }

  isRetryable() {
    // Some file system errors might be retryable (temporary locks, etc.)
    const retryableMessages = ['EBUSY', 'EMFILE', 'ENFILE'];
    return retryableMessages.some(msg => 
      this.message.includes(msg)
    );
  }
}

/**
 * Memory operation errors
 */
class MemoryError extends BaseError {
  constructor(message, context = {}) {
    super(message, context);
    this.category = 'MEMORY';
    this.memoryType = context.memoryType || null;
    this.operation = context.operation || null;
  }

  isRetryable() {
    // Memory errors might be retryable if they're due to temporary issues
    const retryableOperations = ['summarize', 'extract_knowledge'];
    return retryableOperations.includes(this.operation);
  }
}

/**
 * Timeout errors
 */
class TimeoutError extends BaseError {
  constructor(message, context = {}) {
    super(message, context);
    this.category = 'TIMEOUT';
    this.timeout = context.timeout || null;
  }

  isRetryable() {
    return true;
  }

  getRetryDelay() {
    return 3000; // 3 seconds for timeout retries
  }
}

/**
 * Circuit breaker error (when circuit is open)
 */
class CircuitBreakerError extends BaseError {
  constructor(message, context = {}) {
    super(message, context);
    this.category = 'CIRCUIT_BREAKER';
  }

  isRetryable() {
    return false; // Circuit breaker errors should not be retried immediately
  }
}

/**
 * Error factory for creating appropriate error instances
 */
class ErrorFactory {
  /**
   * Create an error instance based on the error type and context
   * @param {Error|Object} error - Original error or error object
   * @param {Object} context - Additional context information
   * @returns {BaseError} Appropriate error instance
   */
  static createError(error, context = {}) {
    // If it's already one of our custom errors, return it as-is
    if (error instanceof BaseError) {
      return error;
    }

    const message = error.message || error.toString();
    const errorContext = { ...context };

    // Add original error information to context
    if (error.code) errorContext.code = error.code;
    if (error.status) errorContext.statusCode = error.status;
    if (error.statusCode) errorContext.statusCode = error.statusCode;

    // Classify error based on message, code, or status
    if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED' || 
        error.code === 'ETIMEDOUT' || error.code === 'ECONNRESET') {
      return new APIError(message, errorContext);
    }

    if (error.status === 401 || error.statusCode === 401 || 
        message.toLowerCase().includes('unauthorized') ||
        message.toLowerCase().includes('invalid api key')) {
      return new AuthenticationError(message, errorContext);
    }

    if (error.status === 429 || error.statusCode === 429 || 
        message.toLowerCase().includes('rate limit')) {
      return new RateLimitError(message, errorContext);
    }

    if ((error.status && error.status >= 400 && error.status < 600) ||
        (error.statusCode && error.statusCode >= 400 && error.statusCode < 600)) {
      return new APIError(message, errorContext);
    }

    if (message.toLowerCase().includes('timeout')) {
      return new TimeoutError(message, errorContext);
    }

    if (message.toLowerCase().includes('validation') || 
        message.toLowerCase().includes('invalid')) {
      return new ValidationError(message, errorContext);
    }

    if (error.code && error.code.startsWith('E') && 
        ['ENOENT', 'EACCES', 'EPERM', 'EBUSY'].includes(error.code)) {
      return new FileSystemError(message, errorContext);
    }

    // Check if it's a network-related error by message content
    if (message.toLowerCase().includes('network') ||
        message.toLowerCase().includes('connection') ||
        message.toLowerCase().includes('fetch')) {
      return new APIError(message, errorContext);
    }

    // Default to base error
    return new BaseError(message, errorContext);
  }
}

module.exports = {
  BaseError,
  ConfigurationError,
  APIError,
  RateLimitError,
  AuthenticationError,
  ValidationError,
  FileSystemError,
  MemoryError,
  TimeoutError,
  CircuitBreakerError,
  ErrorFactory
};