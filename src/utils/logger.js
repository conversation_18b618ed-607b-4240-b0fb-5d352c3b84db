/**
 * Centralized Logging Infrastructure
 * 
 * Provides comprehensive logging functionality with configurable log levels,
 * structured output, performance-aware logging, context tracking, and
 * environment-specific formatting.
 */

const crypto = require('crypto');

/**
 * Log levels with numeric priorities
 */
const LOG_LEVELS = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3
};

/**
 * Log level colors for development environment
 */
const LOG_COLORS = {
  DEBUG: '\x1b[36m', // Cyan
  INFO: '\x1b[32m',  // Green
  WARN: '\x1b[33m',  // Yellow
  ERROR: '\x1b[31m', // Red
  RESET: '\x1b[0m'   // Reset
};

/**
 * Performance tracking for expensive operations
 */
class PerformanceTracker {
  constructor() {
    this.operations = new Map();
  }

  /**
   * Start tracking an operation
   * @param {string} operationId - Unique operation identifier
   * @returns {string} Operation ID for reference
   */
  startOperation(operationId = null) {
    const id = operationId || crypto.randomUUID();
    this.operations.set(id, {
      startTime: process.hrtime.bigint(),
      startMemory: process.memoryUsage()
    });
    return id;
  }

  /**
   * End tracking an operation and return metrics
   * @param {string} operationId - Operation identifier
   * @returns {Object} Performance metrics
   */
  endOperation(operationId) {
    const operation = this.operations.get(operationId);
    if (!operation) {
      return null;
    }

    const endTime = process.hrtime.bigint();
    const endMemory = process.memoryUsage();
    
    const metrics = {
      duration: Number(endTime - operation.startTime) / 1000000, // Convert to milliseconds
      memoryDelta: {
        rss: endMemory.rss - operation.startMemory.rss,
        heapUsed: endMemory.heapUsed - operation.startMemory.heapUsed,
        heapTotal: endMemory.heapTotal - operation.startMemory.heapTotal
      }
    };

    this.operations.delete(operationId);
    return metrics;
  }
}

/**
 * Session context for tracking related operations
 */
class SessionContext {
  constructor(sessionId = null) {
    this.sessionId = sessionId || crypto.randomUUID();
    this.startTime = new Date();
    this.operations = [];
    this.metadata = {};
  }

  /**
   * Add operation to session context
   * @param {string} operation - Operation name
   * @param {Object} details - Operation details
   */
  addOperation(operation, details = {}) {
    this.operations.push({
      operation,
      timestamp: new Date(),
      details
    });
  }

  /**
   * Set session metadata
   * @param {string} key - Metadata key
   * @param {*} value - Metadata value
   */
  setMetadata(key, value) {
    this.metadata[key] = value;
  }

  /**
   * Get session summary
   * @returns {Object} Session summary
   */
  getSummary() {
    return {
      sessionId: this.sessionId,
      startTime: this.startTime,
      duration: Date.now() - this.startTime.getTime(),
      operationCount: this.operations.length,
      metadata: this.metadata
    };
  }
}

/**
 * Enhanced Logger class with comprehensive logging capabilities
 */
class Logger {
  constructor(options = {}) {
    this.level = LOG_LEVELS[options.level?.toUpperCase()] ?? LOG_LEVELS.INFO;
    this.environment = options.environment || process.env.NODE_ENV || 'development';
    this.enableColors = options.enableColors ?? (this.environment === 'development');
    this.enablePerformanceTracking = options.enablePerformanceTracking ?? true;
    this.sessionContext = options.sessionContext || null;
    this.performanceTracker = new PerformanceTracker();
    
    // Performance-aware logging settings
    this.maxContextSize = options.maxContextSize || 1000; // Max characters for context serialization
    this.enableExpensiveOperations = options.enableExpensiveOperations ?? (this.level <= LOG_LEVELS.DEBUG);
  }

  /**
   * Create a new session context
   * @param {string} sessionId - Optional session ID
   * @returns {SessionContext} New session context
   */
  createSession(sessionId = null) {
    this.sessionContext = new SessionContext(sessionId);
    return this.sessionContext;
  }

  /**
   * Set the current session context
   * @param {SessionContext} sessionContext - Session context to use
   */
  setSession(sessionContext) {
    this.sessionContext = sessionContext;
  }

  /**
   * Check if a log level should be output
   * @param {string} level - Log level to check
   * @returns {boolean} True if should log
   */
  shouldLog(level) {
    return LOG_LEVELS[level.toUpperCase()] >= this.level;
  }

  /**
   * Safely serialize context object with size limits
   * @param {Object} context - Context object to serialize
   * @returns {string} Serialized context
   */
  serializeContext(context) {
    if (!context || Object.keys(context).length === 0) {
      return '';
    }

    try {
      // Avoid expensive serialization in production unless debug level
      if (!this.enableExpensiveOperations && typeof context === 'object') {
        const keys = Object.keys(context);
        if (keys.length > 5) {
          return `{${keys.slice(0, 5).join(', ')}... (${keys.length - 5} more)}`;
        }
      }

      let serialized = JSON.stringify(context, null, this.environment === 'development' ? 2 : 0);
      
      // Truncate if too large
      if (serialized.length > this.maxContextSize) {
        serialized = serialized.substring(0, this.maxContextSize) + '... [truncated]';
      }
      
      return serialized;
    } catch (error) {
      return `[Context serialization error: ${error.message}]`;
    }
  }

  /**
   * Format log message based on environment
   * @param {string} level - Log level
   * @param {string} message - Log message
   * @param {Object} context - Additional context
   * @param {Object} performance - Performance metrics
   * @returns {string} Formatted message
   */
  formatMessage(level, message, context = {}, performance = null) {
    const timestamp = new Date().toISOString();
    
    if (this.environment === 'production') {
      // Structured JSON logging for production
      const logEntry = {
        timestamp,
        level,
        message,
        sessionId: this.sessionContext?.sessionId,
        ...(Object.keys(context).length > 0 && { context }),
        ...(performance && { performance })
      };
      return JSON.stringify(logEntry);
    } else {
      // Human-readable logging for development
      const color = this.enableColors ? LOG_COLORS[level] : '';
      const reset = this.enableColors ? LOG_COLORS.RESET : '';
      
      let formatted = `${color}[${timestamp}] [${level}]${reset} ${message}`;
      
      if (this.sessionContext) {
        formatted += ` [Session: ${this.sessionContext.sessionId.substring(0, 8)}]`;
      }
      
      const contextStr = this.serializeContext(context);
      if (contextStr) {
        formatted += `\n  Context: ${contextStr}`;
      }
      
      if (performance) {
        formatted += `\n  Performance: ${performance.duration.toFixed(2)}ms`;
        if (performance.memoryDelta.heapUsed !== 0) {
          formatted += `, Memory: ${(performance.memoryDelta.heapUsed / 1024 / 1024).toFixed(2)}MB`;
        }
      }
      
      return formatted;
    }
  }

  /**
   * Internal logging method
   * @param {string} level - Log level
   * @param {string} message - Log message
   * @param {Object} context - Additional context
   * @param {Object} performance - Performance metrics
   */
  _log(level, message, context = {}, performance = null) {
    if (!this.shouldLog(level)) {
      return;
    }

    // Add operation to session context if available
    if (this.sessionContext) {
      this.sessionContext.addOperation(`log_${level.toLowerCase()}`, {
        message: message.substring(0, 100), // Truncate for session tracking
        hasContext: Object.keys(context).length > 0
      });
    }

    const formattedMessage = this.formatMessage(level, message, context, performance);
    
    // Use appropriate console method
    switch (level) {
      case 'DEBUG':
      case 'INFO':
        console.log(formattedMessage);
        break;
      case 'WARN':
        console.warn(formattedMessage);
        break;
      case 'ERROR':
        console.error(formattedMessage);
        break;
    }
  }

  /**
   * Log debug message
   * @param {string} message - Message to log
   * @param {Object} context - Additional context
   */
  debug(message, context = {}) {
    this._log('DEBUG', message, context);
  }

  /**
   * Log info message
   * @param {string} message - Message to log
   * @param {Object} context - Additional context
   */
  info(message, context = {}) {
    this._log('INFO', message, context);
  }

  /**
   * Log warning message
   * @param {string} message - Message to log
   * @param {Object} context - Additional context
   */
  warn(message, context = {}) {
    this._log('WARN', message, context);
  }

  /**
   * Log error message
   * @param {string} message - Message to log
   * @param {Object} context - Additional context
   */
  error(message, context = {}) {
    this._log('ERROR', message, context);
  }

  /**
   * Start timing an operation
   * @param {string} operationName - Name of the operation
   * @param {Object} context - Additional context
   * @returns {string} Operation ID for ending the timer
   */
  startTimer(operationName, context = {}) {
    if (!this.enablePerformanceTracking) {
      return null;
    }

    const operationId = this.performanceTracker.startOperation();
    this.debug(`Starting operation: ${operationName}`, { 
      operationId, 
      ...context 
    });
    
    return operationId;
  }

  /**
   * End timing an operation and log the results
   * @param {string} operationId - Operation ID from startTimer
   * @param {string} operationName - Name of the operation
   * @param {Object} context - Additional context
   */
  endTimer(operationId, operationName, context = {}) {
    if (!this.enablePerformanceTracking || !operationId) {
      return;
    }

    const performance = this.performanceTracker.endOperation(operationId);
    if (performance) {
      this._log('INFO', `Completed operation: ${operationName}`, context, performance);
    }
  }

  /**
   * Log an operation with automatic timing
   * @param {string} operationName - Name of the operation
   * @param {Function} operation - Async operation to execute
   * @param {Object} context - Additional context
   * @returns {*} Result of the operation
   */
  async timeOperation(operationName, operation, context = {}) {
    const timerId = this.startTimer(operationName, context);
    
    try {
      const result = await operation();
      this.endTimer(timerId, operationName, { ...context, success: true });
      return result;
    } catch (error) {
      this.endTimer(timerId, operationName, { 
        ...context, 
        success: false, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Create a child logger with additional context
   * @param {Object} additionalContext - Context to add to all log messages
   * @returns {Logger} Child logger instance
   */
  child(additionalContext = {}) {
    const childLogger = new Logger({
      level: Object.keys(LOG_LEVELS).find(key => LOG_LEVELS[key] === this.level),
      environment: this.environment,
      enableColors: this.enableColors,
      enablePerformanceTracking: this.enablePerformanceTracking,
      sessionContext: this.sessionContext
    });

    // Override _log to include additional context
    const originalLog = childLogger._log.bind(childLogger);
    childLogger._log = (level, message, context = {}, performance = null) => {
      const mergedContext = { ...additionalContext, ...context };
      originalLog(level, message, mergedContext, performance);
    };

    return childLogger;
  }
}

/**
 * Create a default logger instance
 */
const defaultLogger = new Logger({
  level: process.env.LOG_LEVEL || 'INFO',
  environment: process.env.NODE_ENV || 'development'
});

module.exports = {
  Logger,
  SessionContext,
  PerformanceTracker,
  LOG_LEVELS,
  LOG_COLORS,
  defaultLogger
};