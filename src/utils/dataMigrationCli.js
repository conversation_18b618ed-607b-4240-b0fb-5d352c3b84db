/**
 * Data Migration CLI Utility
 * 
 * Command-line interface for migrating data files between schema versions.
 * Supports batch migration, backup creation, and validation of migrated data.
 */

const fs = require('fs').promises;
const path = require('path');
const { DataMigrationManager } = require('../validation/dataMigration');
const { defaultLogger } = require('./logger');

/**
 * Data Migration CLI class
 */
class DataMigrationCli {
  constructor() {
    this.migrationManager = new DataMigrationManager();
  }

  /**
   * Display help information
   */
  displayHelp() {
    console.log(`
Data Migration CLI - Schema Evolution Utility

Usage:
  node src/utils/dataMigrationCli.js <command> [options]

Commands:
  migrate <file> <dataType>     Migrate a single data file
  batch <directory> <dataType>  Migrate all files in a directory
  validate <file> <dataType>    Validate data file schema version
  info <dataType>               Show schema version information

Options:
  --target-version <version>    Target schema version (default: latest)
  --backup                      Create backup before migration (default: true)
  --no-backup                   Skip backup creation
  --output <path>               Output path for migrated file
  --dry-run                     Show what would be migrated without making changes

Data Types:
  testFacts                     Test facts JSON files
  testResults                   Test results data files
  conversationLog               Conversation log files

Examples:
  # Migrate a test facts file
  node src/utils/dataMigrationCli.js migrate data/test_facts_simple.json testFacts

  # Batch migrate all test results
  node src/utils/dataMigrationCli.js batch test_results/ testResults

  # Validate schema version
  node src/utils/dataMigrationCli.js validate data/test_facts_customer.json testFacts

  # Show schema information
  node src/utils/dataMigrationCli.js info testFacts
`);
  }

  /**
   * Parse command line arguments
   */
  parseArguments(args) {
    const options = {
      command: null,
      file: null,
      directory: null,
      dataType: null,
      targetVersion: null,
      backup: true,
      output: null,
      dryRun: false
    };

    for (let i = 2; i < args.length; i++) {
      const arg = args[i];
      
      if (arg === '--help' || arg === '-h') {
        this.displayHelp();
        process.exit(0);
      } else if (arg === '--target-version') {
        options.targetVersion = args[++i];
      } else if (arg === '--backup') {
        options.backup = true;
      } else if (arg === '--no-backup') {
        options.backup = false;
      } else if (arg === '--output') {
        options.output = args[++i];
      } else if (arg === '--dry-run') {
        options.dryRun = true;
      } else if (!options.command) {
        options.command = arg;
      } else if (options.command === 'migrate' && !options.file) {
        options.file = arg;
      } else if (options.command === 'batch' && !options.directory) {
        options.directory = arg;
      } else if (options.command === 'validate' && !options.file) {
        options.file = arg;
      } else if (!options.dataType) {
        options.dataType = arg;
      }
    }

    return options;
  }

  /**
   * Migrate a single file
   */
  async migrateFile(filePath, dataType, options = {}) {
    try {
      console.log(`\n🔄 Migrating file: ${filePath}`);
      console.log(`📊 Data type: ${dataType}`);

      // Check if file exists
      try {
        await fs.access(filePath);
      } catch (error) {
        throw new Error(`File not found: ${filePath}`);
      }

      // Detect current version
      const fileContent = await fs.readFile(filePath, 'utf8');
      const data = JSON.parse(fileContent);
      const currentVersion = this.migrationManager.detectSchemaVersion(dataType, data);
      const latestVersion = this.migrationManager.getLatestVersion(dataType);
      const targetVersion = options.targetVersion || latestVersion;

      console.log(`📋 Current version: ${currentVersion}`);
      console.log(`🎯 Target version: ${targetVersion}`);

      if (currentVersion === targetVersion) {
        console.log(`✅ File is already at target version ${targetVersion}`);
        return { success: true, alreadyUpToDate: true };
      }

      if (options.dryRun) {
        console.log(`🔍 DRY RUN: Would migrate from ${currentVersion} to ${targetVersion}`);
        return { success: true, dryRun: true };
      }

      // Perform migration
      const migrationResult = await this.migrationManager.migrateFile(filePath, dataType, {
        backupOriginal: options.backup,
        outputPath: options.output,
        targetVersion: targetVersion
      });

      console.log(`✅ Migration completed successfully`);
      console.log(`📁 Output file: ${migrationResult.filePath}`);
      console.log(`🔄 Migrations applied: ${migrationResult.migrationsApplied.join(' → ')}`);
      
      if (migrationResult.backupCreated) {
        console.log(`💾 Backup created`);
      }

      // Generate migration report
      const report = this.migrationManager.createMigrationReport(migrationResult);
      console.log(`📊 Data records: ${report.dataInfo.recordCount}`);
      console.log(`📏 File size: ${(report.dataInfo.sizeEstimate / 1024).toFixed(1)} KB`);

      if (report.recommendations.length > 0) {
        console.log(`\n💡 Recommendations:`);
        report.recommendations.forEach(rec => console.log(`   • ${rec}`));
      }

      return migrationResult;

    } catch (error) {
      console.error(`❌ Migration failed: ${error.message}`);
      defaultLogger.error('File migration failed', {
        filePath,
        dataType,
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * Batch migrate files in a directory
   */
  async batchMigrate(directory, dataType, options = {}) {
    try {
      console.log(`\n🔄 Batch migrating directory: ${directory}`);
      console.log(`📊 Data type: ${dataType}`);

      // Check if directory exists
      try {
        const stats = await fs.stat(directory);
        if (!stats.isDirectory()) {
          throw new Error(`Path is not a directory: ${directory}`);
        }
      } catch (error) {
        throw new Error(`Directory not found: ${directory}`);
      }

      // Find files to migrate
      const files = await fs.readdir(directory);
      const jsonFiles = files.filter(file => file.endsWith('.json'));

      if (jsonFiles.length === 0) {
        console.log(`⚠️  No JSON files found in ${directory}`);
        return { success: true, filesProcessed: 0 };
      }

      console.log(`📁 Found ${jsonFiles.length} JSON files`);

      const results = [];
      let successCount = 0;
      let errorCount = 0;
      let upToDateCount = 0;

      for (const file of jsonFiles) {
        const filePath = path.join(directory, file);
        console.log(`\n--- Processing: ${file} ---`);

        try {
          const result = await this.migrateFile(filePath, dataType, options);
          results.push({ file, result, success: true });
          
          if (result.alreadyUpToDate) {
            upToDateCount++;
          } else {
            successCount++;
          }
        } catch (error) {
          results.push({ file, error: error.message, success: false });
          errorCount++;
        }
      }

      // Display summary
      console.log(`\n📊 Batch Migration Summary`);
      console.log(`✅ Successfully migrated: ${successCount}`);
      console.log(`📋 Already up-to-date: ${upToDateCount}`);
      console.log(`❌ Failed: ${errorCount}`);
      console.log(`📁 Total processed: ${jsonFiles.length}`);

      if (errorCount > 0) {
        console.log(`\n❌ Failed files:`);
        results.filter(r => !r.success).forEach(r => {
          console.log(`   • ${r.file}: ${r.error}`);
        });
      }

      return {
        success: errorCount === 0,
        filesProcessed: jsonFiles.length,
        successCount,
        errorCount,
        upToDateCount,
        results
      };

    } catch (error) {
      console.error(`❌ Batch migration failed: ${error.message}`);
      defaultLogger.error('Batch migration failed', {
        directory,
        dataType,
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * Validate a file's schema version
   */
  async validateFile(filePath, dataType) {
    try {
      console.log(`\n🔍 Validating file: ${filePath}`);
      console.log(`📊 Data type: ${dataType}`);

      // Check if file exists
      try {
        await fs.access(filePath);
      } catch (error) {
        throw new Error(`File not found: ${filePath}`);
      }

      // Read and parse file
      const fileContent = await fs.readFile(filePath, 'utf8');
      const data = JSON.parse(fileContent);

      // Detect version
      const currentVersion = this.migrationManager.detectSchemaVersion(dataType, data);
      const latestVersion = this.migrationManager.getLatestVersion(dataType);

      console.log(`📋 Current version: ${currentVersion}`);
      console.log(`🎯 Latest version: ${latestVersion}`);

      if (currentVersion === latestVersion) {
        console.log(`✅ File is at the latest schema version`);
      } else {
        console.log(`⚠️  File needs migration from ${currentVersion} to ${latestVersion}`);
      }

      // Validate migrated data
      const validationResult = this.migrationManager.validateMigratedData(dataType, data, currentVersion);
      
      if (validationResult.isValid) {
        console.log(`✅ Data structure is valid for version ${currentVersion}`);
      } else {
        console.log(`❌ Data structure validation failed:`);
        validationResult.errors.forEach(error => {
          console.log(`   • ${error.message || error}`);
        });
      }

      if (validationResult.warnings.length > 0) {
        console.log(`⚠️  Validation warnings:`);
        validationResult.warnings.forEach(warning => {
          console.log(`   • ${warning}`);
        });
      }

      return {
        success: true,
        currentVersion,
        latestVersion,
        needsMigration: currentVersion !== latestVersion,
        isValid: validationResult.isValid,
        errors: validationResult.errors,
        warnings: validationResult.warnings
      };

    } catch (error) {
      console.error(`❌ Validation failed: ${error.message}`);
      defaultLogger.error('File validation failed', {
        filePath,
        dataType,
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * Show schema information for a data type
   */
  showSchemaInfo(dataType) {
    try {
      console.log(`\n📊 Schema Information for: ${dataType}`);

      const latestVersion = this.migrationManager.getLatestVersion(dataType);
      console.log(`🎯 Latest version: ${latestVersion}`);

      // Get schema versions
      const schemaKey = this.migrationManager.getSchemaKey(dataType);
      const { SCHEMA_VERSIONS } = require('../validation/dataMigration');
      const dataTypeVersions = SCHEMA_VERSIONS[schemaKey] || {};

      if (Object.keys(dataTypeVersions).length > 0) {
        console.log(`\n📋 Available versions:`);
        Object.entries(dataTypeVersions).forEach(([version, description]) => {
          const indicator = version === latestVersion ? '🎯' : '📄';
          console.log(`   ${indicator} ${version}: ${description}`);
        });
      }

      // Show available migrations
      const migrations = this.migrationManager.migrations[dataType] || {};
      if (Object.keys(migrations).length > 0) {
        console.log(`\n🔄 Available migrations:`);
        Object.keys(migrations).forEach(migrationKey => {
          const [from, to] = migrationKey.split('_to_');
          console.log(`   • ${from} → ${to}`);
        });
      }

      return { success: true };

    } catch (error) {
      console.error(`❌ Failed to show schema info: ${error.message}`);
      throw error;
    }
  }

  /**
   * Main CLI entry point
   */
  async run(args) {
    try {
      const options = this.parseArguments(args);

      if (!options.command) {
        this.displayHelp();
        return;
      }

      switch (options.command) {
        case 'migrate':
          if (!options.file || !options.dataType) {
            console.error('❌ Missing required arguments for migrate command');
            console.log('Usage: migrate <file> <dataType>');
            process.exit(1);
          }
          await this.migrateFile(options.file, options.dataType, options);
          break;

        case 'batch':
          if (!options.directory || !options.dataType) {
            console.error('❌ Missing required arguments for batch command');
            console.log('Usage: batch <directory> <dataType>');
            process.exit(1);
          }
          await this.batchMigrate(options.directory, options.dataType, options);
          break;

        case 'validate':
          if (!options.file || !options.dataType) {
            console.error('❌ Missing required arguments for validate command');
            console.log('Usage: validate <file> <dataType>');
            process.exit(1);
          }
          await this.validateFile(options.file, options.dataType);
          break;

        case 'info':
          if (!options.dataType) {
            console.error('❌ Missing required argument for info command');
            console.log('Usage: info <dataType>');
            process.exit(1);
          }
          this.showSchemaInfo(options.dataType);
          break;

        default:
          console.error(`❌ Unknown command: ${options.command}`);
          this.displayHelp();
          process.exit(1);
      }

    } catch (error) {
      console.error(`❌ CLI execution failed: ${error.message}`);
      defaultLogger.error('CLI execution failed', {
        args,
        error: error.message,
        stack: error.stack
      });
      process.exit(1);
    }
  }
}

// Run CLI if this file is executed directly
if (require.main === module) {
  const cli = new DataMigrationCli();
  cli.run(process.argv);
}

module.exports = DataMigrationCli;