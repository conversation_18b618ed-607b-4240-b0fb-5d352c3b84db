/**
 * Performance Tracker - Comprehensive Performance Monitoring System
 *
 * This class provides comprehensive performance monitoring and metrics collection
 * for the LLM Memory Test Application. It tracks execution times, API call statistics,
 * memory usage, system resource utilization, and provides detailed analytics
 * for performance optimization and monitoring.
 *
 * Features:
 * - Execution time measurement with high precision
 * - API call counting and timing with per-model statistics
 * - Memory usage monitoring and system resource tracking
 * - Metrics collection and aggregation utilities
 * - Performance comparison tools
 * - Statistical analysis and reporting
 * - Real-time monitoring capabilities
 * - Export functionality for analysis tools
 *
 * @class PerformanceTracker
 * 
 * @example
 * // Basic usage
 * const tracker = new PerformanceTracker('test-session');
 * tracker.startOperation('conversation-simulation');
 * // ... perform operations
 * tracker.endOperation('conversation-simulation');
 * const metrics = tracker.getMetrics();
 * 
 * @example
 * // API call tracking
 * tracker.recordApiCall('gpt-3.5-turbo', 1500, 250, true);
 * const apiStats = tracker.getApiStatistics();
 * 
 * @example
 * // Memory monitoring
 * tracker.startMemoryMonitoring();
 * // ... perform memory-intensive operations
 * const memoryStats = tracker.getMemoryStatistics();
 */

const os = require('os');
const process = require('process');
const { defaultLogger } = require('./logger');

class PerformanceTracker {
  /**
   * Creates a new PerformanceTracker instance
   * 
   * Initializes the performance tracking system with session identification,
   * metrics storage, and system monitoring capabilities. Sets up data structures
   * for tracking various performance aspects and configures monitoring intervals.
   * 
   * @param {string} sessionId - Unique session identifier for tracking
   * @param {Object} [options] - Optional configuration options
   * @param {boolean} [options.enableSystemMonitoring=true] - Enable system resource monitoring
   * @param {number} [options.systemMonitoringInterval=5000] - System monitoring interval in ms
   * @param {boolean} [options.enableMemoryMonitoring=true] - Enable memory usage monitoring
   * @param {boolean} [options.enableDetailedLogging=false] - Enable detailed performance logging
   * @throws {Error} Throws if sessionId is not provided
   * 
   * @example
   * const tracker = new PerformanceTracker('session-123');
   * 
   * @example
   * // With custom options
   * const tracker = new PerformanceTracker('session-123', {
   *   enableSystemMonitoring: true,
   *   systemMonitoringInterval: 10000,
   *   enableDetailedLogging: true
   * });
   */
  constructor(sessionId, options = {}) {
    if (!sessionId || typeof sessionId !== 'string') {
      throw new Error('Session ID must be a non-empty string');
    }

    this.sessionId = sessionId;
    this.options = {
      enableSystemMonitoring: true,
      systemMonitoringInterval: 5000,
      enableMemoryMonitoring: true,
      enableDetailedLogging: false,
      ...options
    };

    // Initialize tracking data structures
    this.operations = new Map(); // Active operations
    this.completedOperations = []; // Completed operations history
    this.apiCalls = []; // API call history
    this.memorySnapshots = []; // Memory usage snapshots
    this.systemSnapshots = []; // System resource snapshots

    // Performance metrics aggregation
    this.metrics = {
      session: {
        sessionId: this.sessionId,
        startTime: Date.now(),
        endTime: null,
        totalDuration: 0
      },
      operations: {
        total: 0,
        completed: 0,
        failed: 0,
        averageDuration: 0,
        longestOperation: null,
        shortestOperation: null
      },
      api: {
        totalCalls: 0,
        successfulCalls: 0,
        failedCalls: 0,
        totalTokens: 0,
        totalResponseTime: 0,
        averageResponseTime: 0,
        callsByModel: new Map(),
        tokensByModel: new Map(),
        responseTimesByModel: new Map()
      },
      memory: {
        peakUsage: 0,
        averageUsage: 0,
        currentUsage: 0,
        gcEvents: 0,
        heapUsed: 0,
        heapTotal: 0,
        external: 0
      },
      system: {
        peakCpuUsage: 0,
        averageCpuUsage: 0,
        currentCpuUsage: 0,
        totalMemory: os.totalmem(),
        freeMemory: os.freemem(),
        loadAverage: os.loadavg(),
        uptime: os.uptime()
      }
    };

    // System monitoring setup
    this.systemMonitoringInterval = null;
    this.isMonitoring = false;

    // Initialize system monitoring if enabled
    if (this.options.enableSystemMonitoring) {
      this.startSystemMonitoring();
    }

    // Initialize memory monitoring if enabled
    if (this.options.enableMemoryMonitoring) {
      this.startMemoryMonitoring();
    }

    defaultLogger.info('PerformanceTracker initialized', {
      sessionId: this.sessionId,
      options: this.options
    });
  }

  /**
   * Start tracking a named operation
   * 
   * Begins timing and resource monitoring for a specific operation.
   * Operations can be nested and are tracked independently with
   * unique identifiers and comprehensive context information.
   * 
   * @param {string} operationName - Name of the operation to track
   * @param {Object} [context] - Optional context information for the operation
   * @param {string} [context.type] - Operation type (e.g., 'api-call', 'memory-operation')
   * @param {string} [context.component] - Component performing the operation
   * @param {Object} [context.metadata] - Additional metadata for the operation
   * @returns {string} Unique operation ID for tracking
   * @throws {Error} Throws if operationName is not provided
   * 
   * @example
   * const opId = tracker.startOperation('conversation-simulation');
   * 
   * @example
   * // With context
   * const opId = tracker.startOperation('llm-api-call', {
   *   type: 'api-call',
   *   component: 'LLMModel',
   *   metadata: { model: 'gpt-3.5-turbo', tokens: 150 }
   * });
   */
  startOperation(operationName, context = {}) {
    if (!operationName || typeof operationName !== 'string') {
      throw new Error('Operation name must be a non-empty string');
    }

    const operationId = this._generateOperationId(operationName);
    const startTime = process.hrtime.bigint();
    const memoryUsage = process.memoryUsage();

    const operation = {
      id: operationId,
      name: operationName,
      startTime,
      startTimestamp: Date.now(),
      endTime: null,
      endTimestamp: null,
      duration: null,
      context: { ...context },
      memoryStart: memoryUsage,
      memoryEnd: null,
      memoryDelta: null,
      status: 'running'
    };

    this.operations.set(operationId, operation);
    this.metrics.operations.total++;

    if (this.options.enableDetailedLogging) {
      defaultLogger.debug('Operation started', {
        sessionId: this.sessionId,
        operationId,
        operationName,
        context,
        memoryUsage: this._formatMemoryUsage(memoryUsage)
      });
    }

    return operationId;
  }

  /**
   * End tracking for a named operation
   * 
   * Completes timing and resource monitoring for a specific operation,
   * calculates performance metrics, and moves the operation to completed
   * operations history for analysis and reporting.
   * 
   * @param {string} operationNameOrId - Operation name or ID to end
   * @param {Object} [result] - Optional result information
   * @param {boolean} [result.success=true] - Whether the operation succeeded
   * @param {string} [result.error] - Error message if operation failed
   * @param {Object} [result.metadata] - Additional result metadata
   * @returns {Object} Operation performance summary
   * @throws {Error} Throws if operation is not found or already completed
   * 
   * @example
   * const summary = tracker.endOperation('conversation-simulation');
   * console.log(`Operation took ${summary.duration}ms`);
   * 
   * @example
   * // With result information
   * const summary = tracker.endOperation(operationId, {
   *   success: true,
   *   metadata: { messagesGenerated: 25, factsProcessed: 5 }
   * });
   */
  endOperation(operationNameOrId, result = {}) {
    const operation = this._findOperation(operationNameOrId);
    
    if (!operation) {
      throw new Error(`Operation not found: ${operationNameOrId}`);
    }

    if (operation.status !== 'running') {
      throw new Error(`Operation ${operationNameOrId} is not running (status: ${operation.status})`);
    }

    const endTime = process.hrtime.bigint();
    const endTimestamp = Date.now();
    const memoryUsage = process.memoryUsage();
    const duration = Number(endTime - operation.startTime) / 1000000; // Convert to milliseconds

    // Update operation with completion data
    operation.endTime = endTime;
    operation.endTimestamp = endTimestamp;
    operation.duration = duration;
    operation.memoryEnd = memoryUsage;
    operation.memoryDelta = this._calculateMemoryDelta(operation.memoryStart, memoryUsage);
    operation.status = result.success !== false ? 'completed' : 'failed';
    operation.result = { ...result };

    // Update metrics
    if (operation.status === 'completed') {
      this.metrics.operations.completed++;
    } else {
      this.metrics.operations.failed++;
    }

    // Update operation duration statistics
    this._updateOperationStatistics(operation);

    // Move to completed operations and remove from active
    this.completedOperations.push({ ...operation });
    this.operations.delete(operation.id);

    if (this.options.enableDetailedLogging) {
      defaultLogger.debug('Operation completed', {
        sessionId: this.sessionId,
        operationId: operation.id,
        operationName: operation.name,
        duration: Math.round(duration),
        status: operation.status,
        memoryDelta: this._formatMemoryUsage(operation.memoryDelta),
        result: result.metadata || {}
      });
    }

    return {
      operationId: operation.id,
      operationName: operation.name,
      duration: Math.round(duration),
      status: operation.status,
      memoryDelta: operation.memoryDelta,
      result: operation.result
    };
  }

  /**
   * Record an API call with comprehensive metrics
   * 
   * Tracks API call performance including response time, token usage,
   * success/failure status, and model-specific statistics. Provides
   * detailed analytics for API usage optimization and monitoring.
   * 
   * @param {string} modelName - Name of the model used for the API call
   * @param {number} responseTime - Response time in milliseconds
   * @param {number} [tokenCount=0] - Number of tokens used in the call
   * @param {boolean} [success=true] - Whether the API call succeeded
   * @param {Object} [details] - Additional call details
   * @param {number} [details.promptTokens] - Tokens used in the prompt
   * @param {number} [details.completionTokens] - Tokens used in the completion
   * @param {string} [details.error] - Error message if call failed
   * @param {string} [details.operationType] - Type of operation (e.g., 'chat', 'completion')
   * @throws {Error} Throws if required parameters are invalid
   * 
   * @example
   * tracker.recordApiCall('gpt-3.5-turbo', 1500, 250, true);
   * 
   * @example
   * // With detailed information
   * tracker.recordApiCall('claude-3-sonnet', 2300, 180, true, {
   *   promptTokens: 120,
   *   completionTokens: 60,
   *   operationType: 'chat'
   * });
   * 
   * @example
   * // Failed API call
   * tracker.recordApiCall('gpt-4', 5000, 0, false, {
   *   error: 'Rate limit exceeded',
   *   operationType: 'chat'
   * });
   */
  recordApiCall(modelName, responseTime, tokenCount = 0, success = true, details = {}) {
    if (!modelName || typeof modelName !== 'string') {
      throw new Error('Model name must be a non-empty string');
    }

    if (typeof responseTime !== 'number' || responseTime < 0) {
      throw new Error('Response time must be a non-negative number');
    }

    if (typeof tokenCount !== 'number' || tokenCount < 0) {
      throw new Error('Token count must be a non-negative number');
    }

    const apiCall = {
      timestamp: Date.now(),
      modelName,
      responseTime,
      tokenCount,
      success,
      details: { ...details },
      sessionId: this.sessionId
    };

    this.apiCalls.push(apiCall);

    // Update global API metrics
    this.metrics.api.totalCalls++;
    if (success) {
      this.metrics.api.successfulCalls++;
    } else {
      this.metrics.api.failedCalls++;
    }

    this.metrics.api.totalTokens += tokenCount;
    this.metrics.api.totalResponseTime += responseTime;
    this.metrics.api.averageResponseTime = this.metrics.api.totalResponseTime / this.metrics.api.totalCalls;

    // Update per-model statistics
    this._updateModelStatistics(modelName, responseTime, tokenCount, success);

    if (this.options.enableDetailedLogging) {
      defaultLogger.debug('API call recorded', {
        sessionId: this.sessionId,
        modelName,
        responseTime: Math.round(responseTime),
        tokenCount,
        success,
        details: details
      });
    }
  }

  /**
   * Start system resource monitoring
   * 
   * Begins continuous monitoring of system resources including CPU usage,
   * memory utilization, load average, and other system metrics. Monitoring
   * runs at configurable intervals and provides real-time system health data.
   * 
   * @returns {void}
   * 
   * @example
   * tracker.startSystemMonitoring();
   * // System monitoring runs in background
   * 
   * @example
   * // Check if monitoring is active
   * if (tracker.isSystemMonitoring()) {
   *   console.log('System monitoring is active');
   * }
   */
  startSystemMonitoring() {
    if (this.isMonitoring) {
      defaultLogger.warn('System monitoring already active', { sessionId: this.sessionId });
      return;
    }

    this.isMonitoring = true;
    
    // Take initial snapshot
    this._takeSystemSnapshot();

    // Set up periodic monitoring
    this.systemMonitoringInterval = setInterval(() => {
      this._takeSystemSnapshot();
    }, this.options.systemMonitoringInterval);

    defaultLogger.info('System monitoring started', {
      sessionId: this.sessionId,
      interval: this.options.systemMonitoringInterval
    });
  }

  /**
   * Stop system resource monitoring
   * 
   * Stops continuous system resource monitoring and clears monitoring intervals.
   * Final system snapshot is taken before stopping to ensure complete data collection.
   * 
   * @returns {void}
   * 
   * @example
   * tracker.stopSystemMonitoring();
   */
  stopSystemMonitoring() {
    if (!this.isMonitoring) {
      return;
    }

    if (this.systemMonitoringInterval) {
      clearInterval(this.systemMonitoringInterval);
      this.systemMonitoringInterval = null;
    }

    // Take final snapshot
    this._takeSystemSnapshot();

    this.isMonitoring = false;

    defaultLogger.info('System monitoring stopped', { sessionId: this.sessionId });
  }

  /**
   * Start memory usage monitoring
   * 
   * Begins monitoring of Node.js process memory usage including heap usage,
   * external memory, and garbage collection events. Provides detailed memory
   * analytics for performance optimization and memory leak detection.
   * 
   * @returns {void}
   * 
   * @example
   * tracker.startMemoryMonitoring();
   */
  startMemoryMonitoring() {
    // Take initial memory snapshot
    this._takeMemorySnapshot();

    // Set up garbage collection monitoring if available
    if (global.gc) {
      const originalGc = global.gc;
      global.gc = () => {
        this.metrics.memory.gcEvents++;
        this._takeMemorySnapshot();
        return originalGc();
      };
    }

    defaultLogger.info('Memory monitoring started', { sessionId: this.sessionId });
  }

  /**
   * Get comprehensive performance metrics
   * 
   * Returns a complete snapshot of all performance metrics including
   * operation statistics, API call analytics, memory usage, and system
   * resource utilization. Provides both raw data and calculated statistics.
   * 
   * @returns {Object} Comprehensive performance metrics object
   * @returns {Object} returns.session - Session-level metrics and timing
   * @returns {Object} returns.operations - Operation performance statistics
   * @returns {Object} returns.api - API call metrics and per-model statistics
   * @returns {Object} returns.memory - Memory usage statistics and trends
   * @returns {Object} returns.system - System resource utilization metrics
   * 
   * @example
   * const metrics = tracker.getMetrics();
   * console.log(`Total operations: ${metrics.operations.total}`);
   * console.log(`API success rate: ${metrics.api.successRate}%`);
   * console.log(`Peak memory usage: ${metrics.memory.peakUsage} MB`);
   */
  getMetrics() {
    // Update session duration
    this.metrics.session.totalDuration = Date.now() - this.metrics.session.startTime;

    // Calculate derived metrics
    const derivedMetrics = {
      ...this.metrics,
      api: {
        ...this.metrics.api,
        successRate: this.metrics.api.totalCalls > 0 
          ? ((this.metrics.api.successfulCalls / this.metrics.api.totalCalls) * 100).toFixed(2)
          : 0,
        failureRate: this.metrics.api.totalCalls > 0 
          ? ((this.metrics.api.failedCalls / this.metrics.api.totalCalls) * 100).toFixed(2)
          : 0,
        averageTokensPerCall: this.metrics.api.totalCalls > 0 
          ? Math.round(this.metrics.api.totalTokens / this.metrics.api.totalCalls)
          : 0,
        callsPerMinute: this.metrics.session.totalDuration > 0 
          ? ((this.metrics.api.totalCalls / this.metrics.session.totalDuration) * 60000).toFixed(2)
          : 0
      },
      operations: {
        ...this.metrics.operations,
        successRate: this.metrics.operations.total > 0 
          ? ((this.metrics.operations.completed / this.metrics.operations.total) * 100).toFixed(2)
          : 0,
        failureRate: this.metrics.operations.total > 0 
          ? ((this.metrics.operations.failed / this.metrics.operations.total) * 100).toFixed(2)
          : 0
      }
    };

    return derivedMetrics;
  }

  /**
   * Get API call statistics with per-model breakdown
   * 
   * Returns detailed API call statistics including per-model performance,
   * success rates, token usage, and response time analytics. Provides
   * comprehensive data for API usage optimization and cost analysis.
   * 
   * @returns {Object} API statistics object with per-model breakdown
   * @returns {Object} returns.overall - Overall API call statistics
   * @returns {Object} returns.byModel - Per-model statistics breakdown
   * @returns {Array} returns.recentCalls - Recent API call history
   * 
   * @example
   * const apiStats = tracker.getApiStatistics();
   * console.log(`Overall success rate: ${apiStats.overall.successRate}%`);
   * 
   * // Per-model statistics
   * for (const [model, stats] of Object.entries(apiStats.byModel)) {
   *   console.log(`${model}: ${stats.calls} calls, ${stats.averageResponseTime}ms avg`);
   * }
   */
  getApiStatistics() {
    const byModel = {};

    // Build per-model statistics
    for (const [modelName, calls] of this.metrics.api.callsByModel) {
      const tokens = this.metrics.api.tokensByModel.get(modelName) || 0;
      const responseTimes = this.metrics.api.responseTimesByModel.get(modelName) || [];
      
      const successfulCalls = this.apiCalls
        .filter(call => call.modelName === modelName && call.success)
        .length;

      byModel[modelName] = {
        calls,
        successfulCalls,
        failedCalls: calls - successfulCalls,
        successRate: calls > 0 ? ((successfulCalls / calls) * 100).toFixed(2) : 0,
        totalTokens: tokens,
        averageTokensPerCall: calls > 0 ? Math.round(tokens / calls) : 0,
        averageResponseTime: responseTimes.length > 0 
          ? Math.round(responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length)
          : 0,
        minResponseTime: responseTimes.length > 0 ? Math.min(...responseTimes) : 0,
        maxResponseTime: responseTimes.length > 0 ? Math.max(...responseTimes) : 0
      };
    }

    return {
      overall: {
        totalCalls: this.metrics.api.totalCalls,
        successfulCalls: this.metrics.api.successfulCalls,
        failedCalls: this.metrics.api.failedCalls,
        successRate: this.metrics.api.totalCalls > 0 
          ? ((this.metrics.api.successfulCalls / this.metrics.api.totalCalls) * 100).toFixed(2)
          : 0,
        totalTokens: this.metrics.api.totalTokens,
        averageResponseTime: Math.round(this.metrics.api.averageResponseTime),
        totalResponseTime: Math.round(this.metrics.api.totalResponseTime)
      },
      byModel,
      recentCalls: this.apiCalls.slice(-10) // Last 10 calls
    };
  }

  /**
   * Get memory usage statistics and trends
   * 
   * Returns comprehensive memory usage statistics including current usage,
   * peak usage, trends over time, and garbage collection metrics. Provides
   * data for memory optimization and leak detection.
   * 
   * @returns {Object} Memory statistics object
   * @returns {Object} returns.current - Current memory usage breakdown
   * @returns {Object} returns.peak - Peak memory usage statistics
   * @returns {Object} returns.trends - Memory usage trends and patterns
   * @returns {Array} returns.snapshots - Recent memory snapshots
   * 
   * @example
   * const memoryStats = tracker.getMemoryStatistics();
   * console.log(`Current heap usage: ${memoryStats.current.heapUsed} MB`);
   * console.log(`Peak memory usage: ${memoryStats.peak.heapUsed} MB`);
   */
  getMemoryStatistics() {
    const currentMemory = process.memoryUsage();
    
    // Update current memory metrics
    this.metrics.memory.currentUsage = currentMemory.heapUsed;
    this.metrics.memory.heapUsed = currentMemory.heapUsed;
    this.metrics.memory.heapTotal = currentMemory.heapTotal;
    this.metrics.memory.external = currentMemory.external;

    // Calculate trends if we have snapshots
    const trends = this._calculateMemoryTrends();

    return {
      current: {
        heapUsed: Math.round(currentMemory.heapUsed / 1024 / 1024), // MB
        heapTotal: Math.round(currentMemory.heapTotal / 1024 / 1024), // MB
        external: Math.round(currentMemory.external / 1024 / 1024), // MB
        rss: Math.round(currentMemory.rss / 1024 / 1024), // MB
        heapUtilization: ((currentMemory.heapUsed / currentMemory.heapTotal) * 100).toFixed(2)
      },
      peak: {
        heapUsed: Math.round(this.metrics.memory.peakUsage / 1024 / 1024), // MB
        timestamp: this.metrics.memory.peakTimestamp
      },
      trends,
      gcEvents: this.metrics.memory.gcEvents,
      snapshots: this.memorySnapshots.slice(-10) // Last 10 snapshots
    };
  }

  /**
   * Get system resource statistics
   * 
   * Returns comprehensive system resource utilization statistics including
   * CPU usage, system memory, load average, and other system health metrics.
   * 
   * @returns {Object} System statistics object
   * @returns {Object} returns.cpu - CPU usage statistics
   * @returns {Object} returns.memory - System memory statistics
   * @returns {Object} returns.load - System load average
   * @returns {Object} returns.uptime - System uptime information
   * 
   * @example
   * const systemStats = tracker.getSystemStatistics();
   * console.log(`CPU usage: ${systemStats.cpu.current}%`);
   * console.log(`Free memory: ${systemStats.memory.free} MB`);
   */
  getSystemStatistics() {
    const currentStats = {
      cpu: {
        current: this.metrics.system.currentCpuUsage,
        peak: this.metrics.system.peakCpuUsage,
        average: this.metrics.system.averageCpuUsage
      },
      memory: {
        total: Math.round(this.metrics.system.totalMemory / 1024 / 1024), // MB
        free: Math.round(os.freemem() / 1024 / 1024), // MB
        used: Math.round((this.metrics.system.totalMemory - os.freemem()) / 1024 / 1024), // MB
        utilization: (((this.metrics.system.totalMemory - os.freemem()) / this.metrics.system.totalMemory) * 100).toFixed(2)
      },
      load: {
        current: os.loadavg(),
        cores: os.cpus().length
      },
      uptime: {
        system: Math.round(os.uptime()),
        process: Math.round(process.uptime())
      },
      snapshots: this.systemSnapshots.slice(-10) // Last 10 snapshots
    };

    return currentStats;
  }

  /**
   * Get operation performance statistics
   * 
   * Returns detailed statistics about operation performance including
   * timing analysis, success rates, and operation-specific metrics.
   * 
   * @returns {Object} Operation statistics object
   * @returns {Object} returns.summary - Overall operation summary
   * @returns {Array} returns.byName - Statistics grouped by operation name
   * @returns {Array} returns.recent - Recent completed operations
   * 
   * @example
   * const opStats = tracker.getOperationStatistics();
   * console.log(`Average operation time: ${opStats.summary.averageDuration}ms`);
   */
  getOperationStatistics() {
    const byName = {};
    
    // Group operations by name
    for (const operation of this.completedOperations) {
      if (!byName[operation.name]) {
        byName[operation.name] = {
          name: operation.name,
          count: 0,
          totalDuration: 0,
          averageDuration: 0,
          minDuration: Infinity,
          maxDuration: 0,
          successCount: 0,
          failureCount: 0,
          successRate: 0
        };
      }

      const stats = byName[operation.name];
      stats.count++;
      stats.totalDuration += operation.duration;
      stats.minDuration = Math.min(stats.minDuration, operation.duration);
      stats.maxDuration = Math.max(stats.maxDuration, operation.duration);
      
      if (operation.status === 'completed') {
        stats.successCount++;
      } else {
        stats.failureCount++;
      }
    }

    // Calculate derived statistics
    for (const stats of Object.values(byName)) {
      stats.averageDuration = Math.round(stats.totalDuration / stats.count);
      stats.successRate = ((stats.successCount / stats.count) * 100).toFixed(2);
      stats.minDuration = stats.minDuration === Infinity ? 0 : Math.round(stats.minDuration);
      stats.maxDuration = Math.round(stats.maxDuration);
    }

    return {
      summary: {
        totalOperations: this.metrics.operations.total,
        completedOperations: this.metrics.operations.completed,
        failedOperations: this.metrics.operations.failed,
        averageDuration: Math.round(this.metrics.operations.averageDuration),
        longestOperation: this.metrics.operations.longestOperation,
        shortestOperation: this.metrics.operations.shortestOperation
      },
      byName: Object.values(byName),
      recent: this.completedOperations.slice(-10) // Last 10 operations
    };
  }

  /**
   * Reset all performance metrics and tracking data
   * 
   * Clears all collected performance data and resets metrics to initial state.
   * Useful for starting fresh measurements or clearing data between test runs.
   * 
   * @param {boolean} [keepSystemMonitoring=true] - Whether to keep system monitoring active
   * @returns {void}
   * 
   * @example
   * tracker.reset();
   * // All metrics are now reset to initial state
   * 
   * @example
   * // Reset but stop system monitoring
   * tracker.reset(false);
   */
  reset(keepSystemMonitoring = true) {
    // Clear data structures
    this.operations.clear();
    this.completedOperations = [];
    this.apiCalls = [];
    this.memorySnapshots = [];
    this.systemSnapshots = [];

    // Reset metrics
    const startTime = Date.now();
    this.metrics = {
      session: {
        sessionId: this.sessionId,
        startTime,
        endTime: null,
        totalDuration: 0
      },
      operations: {
        total: 0,
        completed: 0,
        failed: 0,
        averageDuration: 0,
        longestOperation: null,
        shortestOperation: null
      },
      api: {
        totalCalls: 0,
        successfulCalls: 0,
        failedCalls: 0,
        totalTokens: 0,
        totalResponseTime: 0,
        averageResponseTime: 0,
        callsByModel: new Map(),
        tokensByModel: new Map(),
        responseTimesByModel: new Map()
      },
      memory: {
        peakUsage: 0,
        averageUsage: 0,
        currentUsage: 0,
        gcEvents: 0,
        heapUsed: 0,
        heapTotal: 0,
        external: 0
      },
      system: {
        peakCpuUsage: 0,
        averageCpuUsage: 0,
        currentCpuUsage: 0,
        totalMemory: os.totalmem(),
        freeMemory: os.freemem(),
        loadAverage: os.loadavg(),
        uptime: os.uptime()
      }
    };

    // Handle system monitoring
    if (!keepSystemMonitoring && this.isMonitoring) {
      this.stopSystemMonitoring();
    }

    defaultLogger.info('PerformanceTracker reset', {
      sessionId: this.sessionId,
      keepSystemMonitoring
    });
  }

  /**
   * Export performance data for external analysis
   * 
   * Exports comprehensive performance data in various formats for external
   * analysis tools, reporting systems, or long-term storage.
   * 
   * @param {string} [format='json'] - Export format ('json', 'csv', 'summary')
   * @returns {string|Object} Exported data in requested format
   * 
   * @example
   * const jsonData = tracker.exportData('json');
   * fs.writeFileSync('performance-data.json', jsonData);
   * 
   * @example
   * const csvData = tracker.exportData('csv');
   * fs.writeFileSync('performance-data.csv', csvData);
   */
  exportData(format = 'json') {
    const data = {
      sessionId: this.sessionId,
      exportTimestamp: new Date().toISOString(),
      metrics: this.getMetrics(),
      operations: this.completedOperations,
      apiCalls: this.apiCalls,
      memorySnapshots: this.memorySnapshots,
      systemSnapshots: this.systemSnapshots
    };

    switch (format.toLowerCase()) {
      case 'json':
        return JSON.stringify(data, null, 2);
      
      case 'csv':
        return this._exportToCsv(data);
      
      case 'summary':
        return this._exportSummary(data);
      
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  }

  /**
   * Check if system monitoring is currently active
   * 
   * @returns {boolean} True if system monitoring is active
   */
  isSystemMonitoring() {
    return this.isMonitoring;
  }

  /**
   * Cleanup and finalize performance tracking
   * 
   * Stops all monitoring, takes final snapshots, and prepares the tracker
   * for disposal. Should be called when performance tracking is complete.
   * 
   * @returns {void}
   */
  finalize() {
    // Stop system monitoring
    this.stopSystemMonitoring();

    // Take final snapshots
    this._takeMemorySnapshot();

    // Mark session as ended
    this.metrics.session.endTime = Date.now();
    this.metrics.session.totalDuration = this.metrics.session.endTime - this.metrics.session.startTime;

    defaultLogger.info('PerformanceTracker finalized', {
      sessionId: this.sessionId,
      totalDuration: this.metrics.session.totalDuration,
      totalOperations: this.metrics.operations.total,
      totalApiCalls: this.metrics.api.totalCalls
    });
  }

  // Private helper methods

  /**
   * Generate a unique operation ID
   * @private
   */
  _generateOperationId(operationName) {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `${operationName}_${timestamp}_${random}`;
  }

  /**
   * Find an operation by name or ID
   * @private
   */
  _findOperation(nameOrId) {
    // Try to find by ID first
    if (this.operations.has(nameOrId)) {
      return this.operations.get(nameOrId);
    }

    // Try to find by name (get the most recent one)
    for (const [id, operation] of this.operations) {
      if (operation.name === nameOrId) {
        return operation;
      }
    }

    return null;
  }

  /**
   * Update operation statistics
   * @private
   */
  _updateOperationStatistics(operation) {
    const duration = operation.duration;

    // Update average duration
    if (this.metrics.operations.completed === 1) {
      this.metrics.operations.averageDuration = duration;
    } else {
      this.metrics.operations.averageDuration = 
        (this.metrics.operations.averageDuration * (this.metrics.operations.completed - 1) + duration) / 
        this.metrics.operations.completed;
    }

    // Update longest operation
    if (!this.metrics.operations.longestOperation || duration > this.metrics.operations.longestOperation.duration) {
      this.metrics.operations.longestOperation = {
        name: operation.name,
        duration: Math.round(duration),
        timestamp: operation.endTimestamp
      };
    }

    // Update shortest operation
    if (!this.metrics.operations.shortestOperation || duration < this.metrics.operations.shortestOperation.duration) {
      this.metrics.operations.shortestOperation = {
        name: operation.name,
        duration: Math.round(duration),
        timestamp: operation.endTimestamp
      };
    }
  }

  /**
   * Update per-model API statistics
   * @private
   */
  _updateModelStatistics(modelName, responseTime, tokenCount, success) {
    // Update call count
    const currentCalls = this.metrics.api.callsByModel.get(modelName) || 0;
    this.metrics.api.callsByModel.set(modelName, currentCalls + 1);

    // Update token count
    const currentTokens = this.metrics.api.tokensByModel.get(modelName) || 0;
    this.metrics.api.tokensByModel.set(modelName, currentTokens + tokenCount);

    // Update response times
    const responseTimes = this.metrics.api.responseTimesByModel.get(modelName) || [];
    responseTimes.push(responseTime);
    this.metrics.api.responseTimesByModel.set(modelName, responseTimes);
  }

  /**
   * Take a system resource snapshot
   * @private
   */
  _takeSystemSnapshot() {
    const cpus = os.cpus();
    const loadAvg = os.loadavg();
    
    // Calculate CPU usage (simplified)
    let totalIdle = 0;
    let totalTick = 0;
    
    for (const cpu of cpus) {
      for (const type in cpu.times) {
        totalTick += cpu.times[type];
      }
      totalIdle += cpu.times.idle;
    }
    
    const idle = totalIdle / cpus.length;
    const total = totalTick / cpus.length;
    const cpuUsage = 100 - ~~(100 * idle / total);

    const snapshot = {
      timestamp: Date.now(),
      cpu: {
        usage: cpuUsage,
        cores: cpus.length,
        loadAverage: loadAvg
      },
      memory: {
        total: os.totalmem(),
        free: os.freemem(),
        used: os.totalmem() - os.freemem()
      },
      uptime: os.uptime()
    };

    this.systemSnapshots.push(snapshot);

    // Update metrics
    this.metrics.system.currentCpuUsage = cpuUsage;
    this.metrics.system.peakCpuUsage = Math.max(this.metrics.system.peakCpuUsage, cpuUsage);
    this.metrics.system.freeMemory = os.freemem();
    this.metrics.system.loadAverage = loadAvg;

    // Calculate average CPU usage
    if (this.systemSnapshots.length > 1) {
      const totalCpuUsage = this.systemSnapshots.reduce((sum, snap) => sum + snap.cpu.usage, 0);
      this.metrics.system.averageCpuUsage = totalCpuUsage / this.systemSnapshots.length;
    }

    // Keep only recent snapshots to prevent memory growth
    if (this.systemSnapshots.length > 100) {
      this.systemSnapshots = this.systemSnapshots.slice(-50);
    }
  }

  /**
   * Take a memory usage snapshot
   * @private
   */
  _takeMemorySnapshot() {
    const memoryUsage = process.memoryUsage();
    
    const snapshot = {
      timestamp: Date.now(),
      heapUsed: memoryUsage.heapUsed,
      heapTotal: memoryUsage.heapTotal,
      external: memoryUsage.external,
      rss: memoryUsage.rss
    };

    this.memorySnapshots.push(snapshot);

    // Update peak memory usage
    if (memoryUsage.heapUsed > this.metrics.memory.peakUsage) {
      this.metrics.memory.peakUsage = memoryUsage.heapUsed;
      this.metrics.memory.peakTimestamp = Date.now();
    }

    // Calculate average memory usage
    if (this.memorySnapshots.length > 1) {
      const totalHeapUsed = this.memorySnapshots.reduce((sum, snap) => sum + snap.heapUsed, 0);
      this.metrics.memory.averageUsage = totalHeapUsed / this.memorySnapshots.length;
    }

    // Keep only recent snapshots to prevent memory growth
    if (this.memorySnapshots.length > 100) {
      this.memorySnapshots = this.memorySnapshots.slice(-50);
    }
  }

  /**
   * Calculate memory delta between two memory usage objects
   * @private
   */
  _calculateMemoryDelta(startMemory, endMemory) {
    return {
      heapUsed: endMemory.heapUsed - startMemory.heapUsed,
      heapTotal: endMemory.heapTotal - startMemory.heapTotal,
      external: endMemory.external - startMemory.external,
      rss: endMemory.rss - startMemory.rss
    };
  }

  /**
   * Format memory usage for logging
   * @private
   */
  _formatMemoryUsage(memoryUsage) {
    if (!memoryUsage) return 'N/A';
    
    return {
      heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`,
      heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`,
      external: `${Math.round(memoryUsage.external / 1024 / 1024)}MB`,
      rss: `${Math.round(memoryUsage.rss / 1024 / 1024)}MB`
    };
  }

  /**
   * Calculate memory usage trends
   * @private
   */
  _calculateMemoryTrends() {
    if (this.memorySnapshots.length < 2) {
      return { trend: 'insufficient_data', change: 0 };
    }

    const recent = this.memorySnapshots.slice(-10);
    const first = recent[0];
    const last = recent[recent.length - 1];
    
    const change = last.heapUsed - first.heapUsed;
    const changePercent = (change / first.heapUsed) * 100;
    
    let trend = 'stable';
    if (changePercent > 10) trend = 'increasing';
    else if (changePercent < -10) trend = 'decreasing';
    
    return {
      trend,
      change: Math.round(change / 1024 / 1024), // MB
      changePercent: changePercent.toFixed(2)
    };
  }

  /**
   * Export data to CSV format
   * @private
   */
  _exportToCsv(data) {
    const lines = [];
    
    // API calls CSV
    lines.push('API Calls');
    lines.push('Timestamp,Model,Response Time (ms),Tokens,Success,Error');
    
    for (const call of data.apiCalls) {
      lines.push([
        new Date(call.timestamp).toISOString(),
        call.modelName,
        call.responseTime,
        call.tokenCount,
        call.success,
        call.details.error || ''
      ].join(','));
    }
    
    lines.push('');
    
    // Operations CSV
    lines.push('Operations');
    lines.push('Name,Duration (ms),Status,Start Time,End Time');
    
    for (const op of data.operations) {
      lines.push([
        op.name,
        Math.round(op.duration),
        op.status,
        new Date(op.startTimestamp).toISOString(),
        new Date(op.endTimestamp).toISOString()
      ].join(','));
    }
    
    return lines.join('\n');
  }

  /**
   * Export summary data
   * @private
   */
  _exportSummary(data) {
    const metrics = data.metrics;
    
    return `Performance Summary - Session ${data.sessionId}
Export Time: ${data.exportTimestamp}

SESSION OVERVIEW:
- Duration: ${Math.round(metrics.session.totalDuration / 1000)}s
- Total Operations: ${metrics.operations.total}
- Total API Calls: ${metrics.api.totalCalls}

OPERATIONS:
- Completed: ${metrics.operations.completed}
- Failed: ${metrics.operations.failed}
- Success Rate: ${metrics.operations.successRate}%
- Average Duration: ${Math.round(metrics.operations.averageDuration)}ms

API CALLS:
- Successful: ${metrics.api.successfulCalls}
- Failed: ${metrics.api.failedCalls}
- Success Rate: ${metrics.api.successRate}%
- Total Tokens: ${metrics.api.totalTokens}
- Average Response Time: ${Math.round(metrics.api.averageResponseTime)}ms

MEMORY:
- Peak Usage: ${Math.round(metrics.memory.peakUsage / 1024 / 1024)}MB
- Current Usage: ${Math.round(metrics.memory.currentUsage / 1024 / 1024)}MB
- GC Events: ${metrics.memory.gcEvents}

SYSTEM:
- Peak CPU: ${metrics.system.peakCpuUsage}%
- Average CPU: ${metrics.system.averageCpuUsage.toFixed(2)}%
- System Memory: ${Math.round(metrics.system.totalMemory / 1024 / 1024)}MB
- Free Memory: ${Math.round(metrics.system.freeMemory / 1024 / 1024)}MB
`;
  }
}

module.exports = PerformanceTracker;