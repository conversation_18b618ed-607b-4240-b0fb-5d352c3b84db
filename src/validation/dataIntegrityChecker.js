/**
 * Data Integrity and Format Checking Utilities
 * 
 * This module provides utilities for checking data integrity, format validation,
 * and data migration support for the LLM Memory Test Application.
 */

const fs = require('fs').promises;
const path = require('path');
const SchemaValidator = require('./schemaValidator');

/**
 * Data integrity error class
 */
class DataIntegrityError extends Error {
  constructor(message, details = {}) {
    super(message);
    this.name = 'DataIntegrityError';
    this.details = details;
  }
}

/**
 * Data Integrity Checker class
 */
class DataIntegrityChecker {
  constructor() {
    this.validator = new SchemaValidator();
  }

  /**
   * Validate test fact file format and integrity on load
   * @param {string} filePath - Path to the test facts file
   * @returns {Promise<Object>} Validation result with integrity checks
   */
  async validateTestFactsFileIntegrity(filePath) {
    try {
      // Basic file existence and format validation
      const validationResult = await this.readAndValidateTestFactsFile(filePath);
      
      if (!validationResult.isValid) {
        return {
          ...validationResult,
          issues: validationResult.errors || []
        };
      }

      const facts = validationResult.data;
      const integrityChecks = {
        duplicateIds: this.checkDuplicateIds(facts),
        sequentialIds: this.checkSequentialIds(facts),
        contentIntegrity: this.checkContentIntegrity(facts),
        crossReferences: this.checkCrossReferences(facts),
        dataConsistency: this.checkDataConsistency(facts),
        complexityDistribution: this.checkComplexityDistribution(facts)
      };

      // Add integrity warnings to the result and check for critical issues
      const integrityWarnings = [];
      const integrityIssues = [];
      let hasIntegrityErrors = false;

      Object.entries(integrityChecks).forEach(([check, result]) => {
        if (result.issues.length > 0) {
          const issuesWithPrefix = result.issues.map(issue => `${check}: ${issue}`);

          // Duplicate IDs are critical errors
          if (check === 'duplicateIds') {
            integrityIssues.push(...issuesWithPrefix);
            hasIntegrityErrors = true;
          } else {
            integrityWarnings.push(...issuesWithPrefix);
          }
        }
      });

      // Calculate statistics
      const statistics = {
        factCount: facts.length,
        complexityDistribution: {
          simple: integrityChecks.complexityDistribution?.distribution?.simple?.count || 0,
          complex: integrityChecks.complexityDistribution?.distribution?.complex?.count || 0
        }
      };

      return {
        ...validationResult,
        isValid: validationResult.isValid && !hasIntegrityErrors,
        issues: [...(validationResult.issues || []), ...integrityIssues],
        integrityChecks,
        statistics,
        warnings: [...validationResult.warnings, ...integrityWarnings]
      };

    } catch (error) {
      throw new DataIntegrityError(`Failed to validate test facts file integrity: ${error.message}`, {
        filePath,
        originalError: error
      });
    }
  }

  /**
   * Check conversation log integrity during simulation
   * @param {Array} conversationLog - Conversation log entries
   * @param {Object} expectedFlow - Expected conversation flow parameters
   * @returns {Object} Integrity check result
   */
  validateConversationLogIntegrity(conversationLog, expectedFlow = {}) {
    const integrityResult = {
      isValid: true,
      issues: [],
      warnings: [],
      statistics: {
        messageCount: conversationLog.length,
        totalMessages: conversationLog.length,
        userMessages: 0,
        assistantMessages: 0,
        roleDistribution: {
          user: 0,
          assistant: 0
        },
        averageMessageLength: 0,
        conversationDuration: null
      }
    };

    try {
      // Check for empty conversation log
      if (!conversationLog || conversationLog.length === 0) {
        integrityResult.statistics.messageCount = 0;
        integrityResult.statistics.totalMessages = 0;
        integrityResult.warnings.push('Conversation log is empty - no messages to validate');
        return integrityResult;
      }

      // Basic schema validation
      const schemaResult = this.validator.validateConversationLog(conversationLog);
      if (!schemaResult.isValid) {
        integrityResult.isValid = false;
        integrityResult.issues.push(...schemaResult.errors.map(e => e.message));
      }

      // Calculate statistics
      let totalLength = 0;
      let firstTimestamp = null;
      let lastTimestamp = null;

      for (const entry of conversationLog) {
        if (entry.role === 'user') {
          integrityResult.statistics.userMessages++;
          integrityResult.statistics.roleDistribution.user++;
        } else if (entry.role === 'assistant') {
          integrityResult.statistics.assistantMessages++;
          integrityResult.statistics.roleDistribution.assistant++;
        }

        totalLength += entry.content.length;

        if (entry.timestamp) {
          const timestamp = new Date(entry.timestamp);
          if (!firstTimestamp || timestamp < firstTimestamp) {
            firstTimestamp = timestamp;
          }
          if (!lastTimestamp || timestamp > lastTimestamp) {
            lastTimestamp = timestamp;
          }
        }
      }

      integrityResult.statistics.averageMessageLength = conversationLog.length > 0 ? totalLength / conversationLog.length : 0;
      
      if (firstTimestamp && lastTimestamp) {
        integrityResult.statistics.conversationDuration = lastTimestamp - firstTimestamp;
      }

      // Check conversation flow integrity
      this.checkConversationFlow(conversationLog, integrityResult, expectedFlow);

      // Check for potential data corruption
      this.checkConversationDataCorruption(conversationLog, integrityResult);

      // Check timing consistency
      this.checkConversationTiming(conversationLog, integrityResult);

    } catch (error) {
      integrityResult.isValid = false;
      integrityResult.issues.push(`Integrity check failed: ${error.message}`);
    }

    return integrityResult;
  }

  /**
   * Validate result output format before saving
   * @param {Object} testResults - Test results object
   * @param {string} outputPath - Intended output path
   * @returns {Object} Format validation result
   */
  validateResultOutputFormat(testResults, outputPath) {
    const formatResult = {
      isValid: true,
      issues: [],
      warnings: [],
      recommendations: [],
      statistics: {
        requiredFields: 0,
        optionalFields: 0
      }
    };

    try {
      // Check for null/undefined data
      if (!testResults || typeof testResults !== 'object') {
        formatResult.isValid = false;
        formatResult.issues.push('Test results data is missing or not an object');
        return formatResult;
      }

      // Custom validation for flat result format
      this.validateFlatResultFormat(testResults, formatResult);

      // Additional custom validations
      this.addCustomValidationWarnings(testResults, formatResult);

      // Check output path validity
      const pathValidation = this.validateOutputPath(outputPath);
      if (!pathValidation.isValid) {
        formatResult.isValid = false;
        formatResult.issues.push(...pathValidation.issues);
      }

      // Check data consistency
      this.checkResultConsistency(testResults, formatResult);

      // Generate format recommendations
      formatResult.recommendations = this.generateFormatRecommendations(testResults, outputPath);

    } catch (error) {
      formatResult.isValid = false;
      formatResult.issues.push(`Format validation failed: ${error.message}`);
    }

    return formatResult;
  }

  /**
   * Check for duplicate IDs in test facts
   * @param {Array} facts - Array of test facts
   * @returns {Object} Check result
   */
  checkDuplicateIds(facts) {
    const result = { isValid: true, issues: [], duplicates: [] };
    const idCounts = new Map();

    // Count occurrences of each ID
    for (const fact of facts) {
      const count = idCounts.get(fact.id) || 0;
      idCounts.set(fact.id, count + 1);
    }

    // Find duplicates
    for (const [id, count] of idCounts) {
      if (count > 1) {
        result.isValid = false;
        result.duplicates.push({ id, count });
      }
    }

    if (result.duplicates.length > 0) {
      const duplicateIds = result.duplicates.map(d => d.id).join(', ');
      result.issues.push(`Duplicate IDs found: ${duplicateIds}`);
    }

    return result;
  }

  /**
   * Check if IDs are sequential (warning, not error)
   * @param {Array} facts - Array of test facts
   * @returns {Object} Check result
   */
  checkSequentialIds(facts) {
    const result = { isValid: true, issues: [] };
    const ids = facts.map(f => f.id).sort((a, b) => a - b);
    
    let expectedId = ids[0];
    for (const id of ids) {
      if (id !== expectedId) {
        result.issues.push(`Non-sequential ID detected: expected ${expectedId}, found ${id}`);
      }
      expectedId++;
    }

    return result;
  }

  /**
   * Check content integrity (encoding, special characters, etc.)
   * @param {Array} facts - Array of test facts
   * @returns {Object} Check result
   */
  checkContentIntegrity(facts) {
    const result = { isValid: true, issues: [] };

    for (const fact of facts) {
      // Check for potential encoding issues
      if (this.hasEncodingIssues(fact.fact)) {
        result.issues.push(`Potential encoding issue in fact ${fact.id}`);
      }

      // Check for suspicious patterns
      if (this.hasSuspiciousPatterns(fact.fact)) {
        result.issues.push(`Suspicious content pattern in fact ${fact.id}`);
      }

      // Check question-answer alignment
      if (!this.isQuestionAnswerAligned(fact.question, fact.answer, fact.fact)) {
        result.issues.push(`Question-answer misalignment in fact ${fact.id}`);
      }
    }

    return result;
  }

  /**
   * Check cross-references between facts
   * @param {Array} facts - Array of test facts
   * @returns {Object} Check result
   */
  checkCrossReferences(facts) {
    const result = { isValid: true, issues: [] };

    // Check for conflicting information
    const conflicts = this.findConflictingFacts(facts);
    if (conflicts.length > 0) {
      result.issues.push(...conflicts.map(c => `Conflicting facts: ${c.fact1} vs ${c.fact2} - ${c.reason}`));
    }

    // Check for redundant information
    const redundancies = this.findRedundantFacts(facts);
    if (redundancies.length > 0) {
      result.issues.push(...redundancies.map(r => `Redundant facts: ${r.fact1} and ${r.fact2}`));
    }

    return result;
  }

  /**
   * Check data consistency across fields
   * @param {Array} facts - Array of test facts
   * @returns {Object} Check result
   */
  checkDataConsistency(facts) {
    const result = { isValid: true, issues: [] };

    for (const fact of facts) {
      // Check complexity assignment consistency
      if (!this.isComplexityConsistent(fact)) {
        result.issues.push(`Inconsistent complexity assignment for fact ${fact.id}`);
      }

      // Check answer length vs complexity
      if (!this.isAnswerLengthAppropriate(fact)) {
        result.issues.push(`Answer length inappropriate for complexity in fact ${fact.id}`);
      }
    }

    return result;
  }

  /**
   * Check conversation flow integrity
   * @param {Array} conversationLog - Conversation log
   * @param {Object} result - Result object to update
   * @param {Object} expectedFlow - Expected flow parameters
   */
  checkConversationFlow(conversationLog, result, expectedFlow) {
    let consecutiveCount = 1;
    let lastRole = null;

    for (let i = 0; i < conversationLog.length; i++) {
      const entry = conversationLog[i];

      // Check for consecutive messages from same role
      if (lastRole === entry.role) {
        consecutiveCount++;
        if (consecutiveCount >= 3) {
          result.warnings.push(`Found ${consecutiveCount} consecutive ${entry.role} messages starting at message ${i - consecutiveCount + 2}`);
        }
      } else {
        consecutiveCount = 1;
        lastRole = entry.role;
      }

      // Check cycle progression (if cycles are present)
      if (entry.cycle !== undefined) {
        const expectedCycle = i + 1;
        if (entry.cycle !== expectedCycle) {
          result.issues.push(`Cycle mismatch at index ${i}: expected ${expectedCycle}, got ${entry.cycle}`);
          result.isValid = false;
        }
      }
    }

    // Check expected message count
    if (expectedFlow.expectedMessages && conversationLog.length !== expectedFlow.expectedMessages) {
      result.warnings.push(`Message count mismatch: expected ${expectedFlow.expectedMessages}, got ${conversationLog.length}`);
    }
  }

  /**
   * Check for potential data corruption in conversation
   * @param {Array} conversationLog - Conversation log
   * @param {Object} result - Result object to update
   */
  checkConversationDataCorruption(conversationLog, result) {
    for (let i = 0; i < conversationLog.length; i++) {
      const entry = conversationLog[i];

      // Check for truncated messages
      if (this.isTruncatedMessage(entry.content)) {
        result.warnings.push(`Potentially truncated message at cycle ${entry.cycle}`);
      }

      // Check for repeated content
      if (i > 0 && entry.content === conversationLog[i - 1].content) {
        result.warnings.push(`Repeated content at cycle ${entry.cycle}`);
      }

      // Check for encoding issues
      if (this.hasEncodingIssues(entry.content)) {
        result.issues.push(`Encoding issue detected at cycle ${entry.cycle}`);
        result.isValid = false;
      }
    }
  }

  /**
   * Check conversation timing consistency
   * @param {Array} conversationLog - Conversation log
   * @param {Object} result - Result object to update
   */
  checkConversationTiming(conversationLog, result) {
    for (let i = 1; i < conversationLog.length; i++) {
      const current = conversationLog[i];
      const previous = conversationLog[i - 1];

      if (current.timestamp && previous.timestamp) {
        const currentTime = new Date(current.timestamp);
        const previousTime = new Date(previous.timestamp);

        // Check for backwards time
        if (currentTime < previousTime) {
          result.warnings.push(`Message ${i} has timestamp before previous message (${currentTime.toISOString()} < ${previousTime.toISOString()})`);
        }

        // Check for unrealistic time gaps
        const timeDiff = currentTime - previousTime;
        if (timeDiff > 300000) { // 5 minutes
          result.warnings.push(`Large time gap (${Math.round(timeDiff / 1000)}s) between cycles ${previous.cycle} and ${current.cycle}`);
        }
      }
    }
  }

  /**
   * Validate output path
   * @param {string} outputPath - Output file path
   * @returns {Object} Validation result
   */
  validateOutputPath(outputPath) {
    const result = { isValid: true, issues: [] };

    try {
      // Check path format
      const parsedPath = path.parse(outputPath);
      
      if (!parsedPath.ext) {
        result.issues.push('Output path should include file extension');
        result.isValid = false;
      }

      if (parsedPath.name.length === 0) {
        result.issues.push('Output path should include filename');
        result.isValid = false;
      }

      // Check for invalid characters
      const invalidChars = /[<>:"|?*]/;
      if (invalidChars.test(outputPath)) {
        result.issues.push('Output path contains invalid characters');
        result.isValid = false;
      }

      // Check path length (Windows limitation)
      if (outputPath.length > 260) {
        result.issues.push('Output path is too long (>260 characters)');
        result.isValid = false;
      }

    } catch (error) {
      result.issues.push(`Invalid output path: ${error.message}`);
      result.isValid = false;
    }

    return result;
  }

  /**
   * Check result completeness
   * @param {Object} testResults - Test results object
   * @param {Object} result - Result object to update
   */
  checkResultCompleteness(testResults, result) {
    const required = ['metadata', 'scores', 'evaluationResults', 'conversationLog'];
    const missing = [];

    for (const field of required) {
      if (!testResults[field]) {
        missing.push(field);
        result.issues.push(`Missing required field: ${field}`);
        result.isValid = false;
      }
    }

    // Add summary message if there are missing fields
    if (missing.length > 0) {
      result.issues.push(`Missing required fields: ${missing.join(', ')}`);
    }

    // Check evaluation results completeness
    if (testResults.evaluationResults) {
      const factIds = new Set();
      for (const evalResult of testResults.evaluationResults) {
        if (factIds.has(evalResult.factId)) {
          result.warnings.push(`Duplicate evaluation for fact ${evalResult.factId}`);
        }
        factIds.add(evalResult.factId);
      }
    }
  }

  /**
   * Check result consistency
   * @param {Object} testResults - Test results object
   * @param {Object} result - Result object to update
   */
  checkResultConsistency(testResults, result) {
    if (!testResults.metadata || !testResults.evaluationResults) {
      return;
    }

    // Check fact count consistency
    const expectedFacts = testResults.metadata.factsTested;
    const actualResults = testResults.evaluationResults.length;
    
    if (expectedFacts !== actualResults) {
      result.warnings.push(`Fact count inconsistency: metadata says ${expectedFacts}, got ${actualResults} results`);
    }

    // Check score calculation consistency
    if (testResults.scores && testResults.evaluationResults.length > 0) {
      const scores = testResults.evaluationResults.map(r => r.score);
      const calculatedOverall = (scores.reduce((sum, score) => sum + score, 0) / scores.length) * 10;
      
      if (Math.abs(calculatedOverall - testResults.scores.overall) > 0.1) {
        result.warnings.push(`Score calculation inconsistency: calculated ${calculatedOverall.toFixed(2)}%, reported ${testResults.scores.overall}%`);
      }
    }
  }

  /**
   * Generate format recommendations
   * @param {Object} testResults - Test results object
   * @param {string} outputPath - Output path
   * @returns {Array} Recommendations
   */
  generateFormatRecommendations(testResults, outputPath) {
    const recommendations = [];

    // File format recommendations
    const ext = path.extname(outputPath).toLowerCase();
    if (ext === '.json') {
      recommendations.push('Consider using .md format for better readability');
    }

    // Data structure recommendations
    if (!testResults.performanceMetrics) {
      recommendations.push('Include performance metrics for comprehensive analysis');
    }

    if (!testResults.memoryContexts) {
      recommendations.push('Include memory contexts for debugging and analysis');
    }

    // Metadata recommendations
    if (testResults.metadata && !testResults.metadata.sessionId) {
      recommendations.push('Include session ID for result tracking');
    }

    return recommendations;
  }

  /**
   * Helper methods for content analysis
   */

  hasEncodingIssues(text) {
    // Check for common encoding issues
    return /[\uFFFD\u0000-\u0008\u000B\u000C\u000E-\u001F]/.test(text);
  }

  hasSuspiciousPatterns(text) {
    // Check for patterns that might indicate data corruption
    const suspiciousPatterns = [
      /(.)\1{10,}/, // Repeated character 10+ times
      /^[A-Z\s]{50,}$/, // All caps text over 50 chars (reduced threshold)
      /\b(test|example|placeholder|lorem|ipsum)\b/i // Placeholder text
    ];
    
    return suspiciousPatterns.some(pattern => pattern.test(text));
  }

  isQuestionAnswerAligned(question, answer, fact) {
    // Basic alignment check - this could be enhanced with NLP
    const questionWords = question.toLowerCase().split(/\W+/).filter(w => w.length > 2);
    const answerWords = answer.toLowerCase().split(/\W+/).filter(w => w.length > 2);
    const factWords = fact.toLowerCase().split(/\W+/).filter(w => w.length > 2);
    
    // Check if answer words appear in the fact
    const answerInFact = answerWords.some(word => factWords.includes(word));
    
    return answerInFact;
  }

  findConflictingFacts(facts) {
    // Simple conflict detection - could be enhanced
    const conflicts = [];
    
    for (let i = 0; i < facts.length; i++) {
      for (let j = i + 1; j < facts.length; j++) {
        const fact1 = facts[i];
        const fact2 = facts[j];
        
        // Check for direct contradictions (simplified)
        if (this.areFactsConflicting(fact1, fact2)) {
          conflicts.push({
            fact1: fact1.id,
            fact2: fact2.id,
            reason: 'Potential contradiction detected'
          });
        }
      }
    }
    
    return conflicts;
  }

  findRedundantFacts(facts) {
    // Simple redundancy detection
    const redundancies = [];
    
    for (let i = 0; i < facts.length; i++) {
      for (let j = i + 1; j < facts.length; j++) {
        const fact1 = facts[i];
        const fact2 = facts[j];
        
        if (this.areFactsSimilar(fact1, fact2)) {
          redundancies.push({
            fact1: fact1.id,
            fact2: fact2.id
          });
        }
      }
    }
    
    return redundancies;
  }

  areFactsConflicting(fact1, fact2) {
    // Simplified conflict detection
    const conflictPatterns = [
      { pattern1: /my name is (\w+)/i, pattern2: /my name is (\w+)/i },
      { pattern1: /i am (\d+) years old/i, pattern2: /i am (\d+) years old/i },
      { pattern1: /i live in (\w+)/i, pattern2: /i live in (\w+)/i }
    ];
    
    for (const { pattern1, pattern2 } of conflictPatterns) {
      const match1 = fact1.fact.match(pattern1);
      const match2 = fact2.fact.match(pattern2);
      
      if (match1 && match2 && match1[1] !== match2[1]) {
        return true;
      }
    }
    
    return false;
  }

  areFactsSimilar(fact1, fact2) {
    // Simple similarity check based on word overlap
    const words1 = new Set(fact1.fact.toLowerCase().split(/\W+/).filter(w => w.length > 2));
    const words2 = new Set(fact2.fact.toLowerCase().split(/\W+/).filter(w => w.length > 2));
    
    const intersection = new Set([...words1].filter(x => words2.has(x)));
    const union = new Set([...words1, ...words2]);
    
    const similarity = intersection.size / union.size;
    
    // Also check for semantic similarity with key terms
    const keyTerms1 = fact1.fact.toLowerCase().match(/\b(work|software|engineer|employed|job|career)\b/g) || [];
    const keyTerms2 = fact2.fact.toLowerCase().match(/\b(work|software|engineer|employed|job|career)\b/g) || [];
    const hasCommonKeyTerms = keyTerms1.some(term => keyTerms2.includes(term));
    
    return similarity > 0.3 || (hasCommonKeyTerms && similarity > 0.2); // More flexible threshold
  }

  isComplexityConsistent(fact) {
    // Simple heuristics for complexity consistency
    const factLength = fact.fact.length;
    const answerLength = fact.answer.length;
    const questionComplexity = (fact.question.match(/\b(what|when|where|who)\b/gi) || []).length;
    
    if (fact.complexity === 'simple') {
      return factLength < 200 && answerLength < 50 && questionComplexity <= 2;
    } else {
      return factLength >= 100 || answerLength >= 20 || questionComplexity > 1;
    }
  }

  isAnswerLengthAppropriate(fact) {
    const answerLength = fact.answer.length;
    
    if (fact.complexity === 'simple') {
      return answerLength <= 100; // Simple facts should have concise answers
    } else {
      return answerLength >= 10; // Complex facts should have substantial answers
    }
  }

  isTruncatedMessage(content) {
    // Check for signs of truncation
    const truncationSigns = [
      /\.\.\.$/, // Ends with ellipsis
      /\[truncated\]/i,
      /\[cut off\]/i,
      /\bmaxim(um|al) length/i
    ];

    return truncationSigns.some(pattern => pattern.test(content));
  }

  /**
   * Read and validate test facts file
   * @param {string} filePath - Path to the test facts file
   * @returns {Promise<Object>} Validation result
   */
  async readAndValidateTestFactsFile(filePath) {
    try {
      // Check file existence
      await fs.access(filePath);

      // Read file content
      const fileContent = await fs.readFile(filePath, 'utf8');

      // Parse JSON
      let data;
      try {
        data = JSON.parse(fileContent);
      } catch (parseError) {
        return {
          isValid: false,
          errors: [`Invalid JSON format: ${parseError.message}`],
          warnings: [],
          data: null
        };
      }

      // Validate schema
      const schemaResult = this.validator.validateTestFacts(data);

      const errors = schemaResult.errors || [];

      // Add summary message for missing required fields
      if (!schemaResult.isValid && errors.some(error => error.includes('must have required property'))) {
        errors.push('Missing required fields detected in test facts file');
      }

      return {
        isValid: schemaResult.isValid,
        errors: errors,
        warnings: schemaResult.warnings || [],
        data: schemaResult.isValid ? data : null
      };

    } catch (error) {
      return {
        isValid: false,
        errors: [`File access error: ${error.message}`],
        warnings: [],
        data: null
      };
    }
  }

  /**
   * Calculate text similarity using Jaccard similarity
   * @param {string} text1 - First text
   * @param {string} text2 - Second text
   * @returns {number} Similarity score between 0 and 1
   */
  calculateTextSimilarity(text1, text2) {
    // Handle empty texts
    if (!text1 || !text2) {
      return 0;
    }

    if (text1 === text2) {
      return 1;
    }

    // Normalize and tokenize texts
    const normalize = (text) => text.toLowerCase().replace(/[^\w\s]/g, '').split(/\s+/).filter(w => w.length > 0);

    const tokens1 = new Set(normalize(text1));
    const tokens2 = new Set(normalize(text2));

    // Calculate Jaccard similarity
    const intersection = new Set([...tokens1].filter(x => tokens2.has(x)));
    const union = new Set([...tokens1, ...tokens2]);

    if (union.size === 0) {
      return 0;
    }

    return intersection.size / union.size;
  }

  /**
   * Check complexity distribution in test facts
   * @param {Array} facts - Array of test facts
   * @param {Object} rules - Distribution rules
   * @returns {Object} Distribution check result
   */
  checkComplexityDistribution(facts, rules = {}) {
    const {
      minComplexityDistribution = 0.2,
      maxComplexityDistribution = 0.8
    } = rules;

    const result = {
      isValid: true,
      issues: [],
      warnings: [],
      distribution: {
        simple: { count: 0, ratio: 0 },
        complex: { count: 0, ratio: 0 }
      }
    };

    // Count complexity types
    for (const fact of facts) {
      if (fact.complexity === 'simple') {
        result.distribution.simple.count++;
      } else if (fact.complexity === 'complex') {
        result.distribution.complex.count++;
      }
    }

    const total = facts.length;
    if (total === 0) {
      result.isValid = false;
      result.warnings.push('No facts to analyze');
      return result;
    }

    // Calculate ratios as percentages
    result.distribution.simple.ratio = (result.distribution.simple.count / total) * 100;
    result.distribution.complex.ratio = (result.distribution.complex.count / total) * 100;

    // Check distribution balance
    const simpleRatio = result.distribution.simple.count / total;
    const complexRatio = result.distribution.complex.count / total;

    if (simpleRatio <= minComplexityDistribution) {
      result.isValid = false;
      result.warnings.push(`Low simple fact ratio: ${(simpleRatio * 100).toFixed(1)}% (minimum: ${(minComplexityDistribution * 100)}%)`);
    }

    if (complexRatio <= minComplexityDistribution) {
      result.isValid = false;
      result.warnings.push(`Low complex fact ratio: ${(complexRatio * 100).toFixed(1)}% (minimum: ${(minComplexityDistribution * 100)}%)`);
    }

    if (simpleRatio > maxComplexityDistribution) {
      result.warnings.push(`High simple fact ratio: ${(simpleRatio * 100).toFixed(1)}% (maximum: ${(maxComplexityDistribution * 100)}%)`);
    }

    if (complexRatio > maxComplexityDistribution) {
      result.warnings.push(`High complex fact ratio: ${(complexRatio * 100).toFixed(1)}% (maximum: ${(maxComplexityDistribution * 100)}%)`);
    }

    return result;
  }

  /**
   * Validate flat result format (used by validateResultOutputFormat)
   * @param {Object} testResults - Test results object
   * @param {Object} formatResult - Format result object to update
   */
  validateFlatResultFormat(testResults, formatResult) {
    const requiredFields = ['sessionId', 'memoryType', 'evaluationResults', 'conversationLog'];
    const optionalFields = ['overallScore', 'testFactsFile', 'testFactsCount', 'performanceMetrics'];

    let requiredCount = 0;
    let optionalCount = 0;
    const missing = [];

    // Check required fields
    for (const field of requiredFields) {
      if (testResults[field]) {
        requiredCount++;
      } else {
        missing.push(field);
        formatResult.isValid = false;
        formatResult.issues.push(`Missing required field: ${field}`);
      }
    }

    // Add summary message if there are missing fields
    if (missing.length > 0) {
      formatResult.issues.push(`Missing required fields: ${missing.join(', ')}`);
    }

    // Count optional fields that are present
    for (const field of optionalFields) {
      if (testResults[field] !== undefined) {
        optionalCount++;
      }
    }

    formatResult.statistics.requiredFields = requiredFields.length;
    formatResult.statistics.optionalFields = optionalCount;

    // Validate evaluation results structure
    if (testResults.evaluationResults && Array.isArray(testResults.evaluationResults)) {
      for (const result of testResults.evaluationResults) {
        if (!result.factId || !result.fact || !result.question || result.score === undefined) {
          formatResult.warnings.push(`Incomplete evaluation result for fact ${result.factId || 'unknown'}`);
        }
      }
    }

    // Validate conversation log structure
    if (testResults.conversationLog && Array.isArray(testResults.conversationLog)) {
      for (const message of testResults.conversationLog) {
        if (!message.role || !message.content) {
          formatResult.warnings.push('Incomplete conversation log entry');
        }
      }
    }
  }

  /**
   * Add custom validation warnings for specific cases
   * @param {Object} testResults - Test results object
   * @param {Object} formatResult - Format result object to update
   */
  addCustomValidationWarnings(testResults, formatResult) {
    const validMemoryTypes = ['simple', 'summary', 'summary_with_knowledge'];

    // Check memory type (can be at root level or under metadata)
    const memoryType = testResults.memoryType || (testResults.metadata && testResults.metadata.memoryType);
    if (memoryType && !validMemoryTypes.includes(memoryType)) {
      formatResult.warnings.push(`Memory type "${memoryType}" is not a recognized type. Valid types: ${validMemoryTypes.join(', ')}`);
    }

    // Check session ID format (can be at root level or under metadata)
    const sessionId = testResults.sessionId || (testResults.metadata && testResults.metadata.sessionId);
    if (sessionId) {
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(sessionId)) {
        formatResult.warnings.push(`Session ID "${sessionId}" is not a valid UUID format`);
      }
    }

    // Check score ranges
    if (testResults.overallScore !== undefined) {
      if (testResults.overallScore < 0 || testResults.overallScore > 100) {
        formatResult.warnings.push(`Overall score ${testResults.overallScore} is outside expected range (0-100)`);
      }
    }

    // Check evaluation results scores
    if (testResults.evaluationResults && Array.isArray(testResults.evaluationResults)) {
      for (const result of testResults.evaluationResults) {
        if (result.score !== undefined && (result.score < 0 || result.score > 10)) {
          formatResult.warnings.push(`Evaluation score ${result.score} for fact ${result.factId} is outside expected range (0-10)`);
        }
      }
    }
  }
}

module.exports = {
  DataIntegrityChecker,
  DataIntegrityError
};