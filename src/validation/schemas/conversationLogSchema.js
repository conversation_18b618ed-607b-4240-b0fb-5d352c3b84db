/**
 * Conversation Log JSON Schema Definition
 *
 * This schema validates conversation logs generated during LLM memory testing.
 * Conversation logs contain sequences of messages between user and assistant
 * roles, along with metadata about timing, context, and conversation flow.
 *
 * Schema validates:
 * - Message structure and sequencing
 * - Role assignments (user/assistant)
 * - Timestamp ordering and validity
 * - Content format and length constraints
 * - Conversation flow and continuity
 * - Metadata and context information
 *
 * @fileoverview JSON Schema for conversation log validation
 * @module conversationLogSchema
 */

module.exports = {
  $id: 'https://llm-memory-test.com/schemas/conversation-log.json',
  $schema: 'http://json-schema.org/draft-07/schema#',
  title: 'Conversation Log Schema',
  description: 'Schema for validating conversation logs in LLM memory testing',
  version: '1.0.0',
  
  type: 'array',
  minItems: 1,
  maxItems: 10000,
  
  items: {
    type: 'object',
    required: ['role', 'content', 'timestamp'],
    additionalProperties: false,
    
    properties: {
      role: {
        type: 'string',
        enum: ['user', 'assistant', 'system'],
        description: 'Role of the message sender'
      },
      
      content: {
        type: 'string',
        minLength: 0,
        maxLength: 10000,
        description: 'Content of the message'
      },
      
      timestamp: {
        type: 'number',
        format: 'timestamp',
        description: 'Unix timestamp when the message was created'
      },
      
      messageId: {
        type: 'string',
        format: 'uuid',
        description: 'Unique identifier for the message'
      },
      
      cycle: {
        type: 'integer',
        minimum: 1,
        maximum: 10000,
        description: 'Conversation cycle number'
      },
      
      metadata: {
        type: 'object',
        additionalProperties: true,
        description: 'Additional metadata for the message',
        properties: {
          model: {
            type: 'string',
            format: 'model-name',
            description: 'Model used to generate this message (for assistant messages)'
          },
          
          responseTime: {
            type: 'number',
            minimum: 0,
            maximum: 300000,
            description: 'Time taken to generate the response in milliseconds'
          },
          
          tokenCount: {
            type: 'integer',
            minimum: 0,
            maximum: 100000,
            description: 'Number of tokens in the message'
          },
          
          temperature: {
            type: 'number',
            minimum: 0,
            maximum: 2,
            description: 'Temperature used for generation'
          },
          
          isFactInjection: {
            type: 'boolean',
            description: 'Whether this message contains an injected fact'
          },
          
          factId: {
            type: 'integer',
            minimum: 1,
            description: 'ID of the fact injected in this message'
          },
          
          memoryContext: {
            type: 'string',
            maxLength: 50000,
            description: 'Memory context available when generating this message'
          },
          
          error: {
            type: 'object',
            properties: {
              code: {
                type: 'string',
                minLength: 1,
                maxLength: 100
              },
              message: {
                type: 'string',
                minLength: 1,
                maxLength: 1000
              },
              recoverable: {
                type: 'boolean'
              }
            },
            required: ['code', 'message'],
            description: 'Error information if message generation failed'
          }
        }
      },
      
      context: {
        type: 'object',
        additionalProperties: false,
        description: 'Contextual information about the message',
        properties: {
          conversationTurn: {
            type: 'integer',
            minimum: 1,
            description: 'Turn number in the conversation'
          },
          
          messagesSinceLastFact: {
            type: 'integer',
            minimum: 0,
            description: 'Number of messages since the last fact injection'
          },
          
          totalFactsInjected: {
            type: 'integer',
            minimum: 0,
            description: 'Total number of facts injected so far'
          },
          
          conversationPhase: {
            type: 'string',
            enum: ['initialization', 'fact_injection', 'conversation', 'evaluation', 'completion'],
            description: 'Current phase of the conversation'
          }
        }
      }
    }
  },
  
  // Additional validation rules at the array level
  allOf: [
    {
      // Ensure timestamp ordering (messages should be in chronological order)
      description: 'Messages should be in chronological order',
      if: {
        type: 'array',
        minItems: 2
      },
      then: {
        // Custom validation would be needed here - this is a simplified version
        // In practice, this would require custom validation logic
        type: 'array'
      }
    },
    {
      // Ensure conversation flow (no more than 3 consecutive messages from same role)
      description: 'No more than 3 consecutive messages from the same role',
      type: 'array'
    },
    {
      // Ensure message ID uniqueness if provided
      description: 'Message IDs must be unique when provided',
      type: 'array'
    }
  ],
  
  examples: [
    [
      {
        role: 'user',
        content: 'Hello, my name is John Smith.',
        timestamp: 1703123456789,
        messageId: '550e8400-e29b-41d4-a716-446655440001',
        cycle: 1,
        metadata: {
          isFactInjection: true,
          factId: 1,
          tokenCount: 8
        },
        context: {
          conversationTurn: 1,
          messagesSinceLastFact: 0,
          totalFactsInjected: 1,
          conversationPhase: 'fact_injection'
        }
      },
      {
        role: 'assistant',
        content: 'Hello John! Nice to meet you. How are you doing today?',
        timestamp: 1703123458234,
        messageId: '550e8400-e29b-41d4-a716-446655440002',
        cycle: 2,
        metadata: {
          model: 'gpt-3.5-turbo',
          responseTime: 1245,
          tokenCount: 12,
          temperature: 0.7,
          isFactInjection: false,
          memoryContext: 'Recent conversation:\nuser: Hello, my name is John Smith.'
        },
        context: {
          conversationTurn: 2,
          messagesSinceLastFact: 1,
          totalFactsInjected: 1,
          conversationPhase: 'conversation'
        }
      },
      {
        role: 'user',
        content: 'I\'m doing well, thank you for asking!',
        timestamp: 1703123460000,
        messageId: '550e8400-e29b-41d4-a716-446655440003',
        cycle: 3,
        metadata: {
          isFactInjection: false,
          tokenCount: 9
        },
        context: {
          conversationTurn: 3,
          messagesSinceLastFact: 2,
          totalFactsInjected: 1,
          conversationPhase: 'conversation'
        }
      }
    ]
  ],
  
  errorMessage: {
    type: 'Conversation log must be an array of message objects',
    minItems: 'Conversation log must contain at least one message',
    maxItems: 'Conversation log is too long (maximum 10000 messages)',
    items: {
      type: 'Each message must be an object',
      required: 'Message must have role, content, and timestamp fields',
      additionalProperties: 'Message contains unknown properties',
      properties: {
        role: 'Role must be "user", "assistant", or "system"',
        content: 'Content must be a string (max 10000 characters)',
        timestamp: 'Timestamp must be a valid Unix timestamp',
        messageId: 'Message ID must be a valid UUID',
        cycle: 'Cycle must be a positive integer (max 10000)',
        metadata: {
          type: 'Metadata must be an object',
          properties: {
            model: 'Model must be a valid model name',
            responseTime: 'Response time must be a positive number (max 300000ms)',
            tokenCount: 'Token count must be a non-negative integer (max 100000)',
            temperature: 'Temperature must be between 0 and 2',
            isFactInjection: 'isFactInjection must be a boolean',
            factId: 'Fact ID must be a positive integer',
            memoryContext: 'Memory context must be a string (max 50000 characters)',
            error: {
              type: 'Error must be an object',
              required: 'Error must have code and message fields',
              properties: {
                code: 'Error code must be a non-empty string (max 100 characters)',
                message: 'Error message must be a non-empty string (max 1000 characters)',
                recoverable: 'Recoverable must be a boolean'
              }
            }
          }
        },
        context: {
          type: 'Context must be an object',
          properties: {
            conversationTurn: 'Conversation turn must be a positive integer',
            messagesSinceLastFact: 'Messages since last fact must be a non-negative integer',
            totalFactsInjected: 'Total facts injected must be a non-negative integer',
            conversationPhase: 'Conversation phase must be a valid phase value'
          }
        }
      }
    }
  }
};