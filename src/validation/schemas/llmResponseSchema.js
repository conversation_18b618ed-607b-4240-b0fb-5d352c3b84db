/**
 * LLM Response JSON Schema Definition
 *
 * This schema validates LLM API responses and generated content used throughout
 * the LLM Memory Test Application. It ensures that responses from language models
 * contain the required fields and follow expected formats for proper processing.
 *
 * Schema validates:
 * - Response content and structure
 * - Metadata including model information and timing
 * - Token usage and cost information
 * - Error handling and status codes
 * - Response quality indicators
 *
 * @fileoverview JSON Schema for LLM response validation
 * @module llmResponseSchema
 */

module.exports = {
  $id: 'https://llm-memory-test.com/schemas/llm-response.json',
  $schema: 'http://json-schema.org/draft-07/schema#',
  title: 'LLM Response Schema',
  description: 'Schema for validating LLM API responses and generated content',
  version: '1.0.0',
  
  type: 'object',
  required: ['content', 'metadata'],
  additionalProperties: false,
  
  properties: {
    content: {
      type: 'string',
      minLength: 0,
      maxLength: 10000,
      description: 'The generated text content from the LLM'
    },
    
    metadata: {
      type: 'object',
      required: ['model', 'timestamp'],
      additionalProperties: true,
      description: 'Metadata about the LLM response',
      
      properties: {
        model: {
          type: 'string',
          format: 'model-name',
          minLength: 1,
          maxLength: 200,
          description: 'Name/identifier of the LLM model used'
        },
        
        timestamp: {
          type: 'number',
          format: 'timestamp',
          description: 'Unix timestamp when the response was generated'
        },
        
        requestId: {
          type: 'string',
          format: 'uuid',
          description: 'Unique identifier for the API request'
        },
        
        provider: {
          type: 'string',
          enum: ['openai', 'openrouter', 'anthropic', 'google', 'local', 'mock'],
          description: 'API provider used for the request'
        },
        
        responseTime: {
          type: 'number',
          minimum: 0,
          maximum: 300000,
          description: 'Response time in milliseconds'
        },
        
        tokenUsage: {
          type: 'object',
          additionalProperties: false,
          description: 'Token usage information',
          properties: {
            promptTokens: {
              type: 'integer',
              minimum: 0,
              maximum: 1000000,
              description: 'Number of tokens in the prompt'
            },
            completionTokens: {
              type: 'integer',
              minimum: 0,
              maximum: 1000000,
              description: 'Number of tokens in the completion'
            },
            totalTokens: {
              type: 'integer',
              minimum: 0,
              maximum: 2000000,
              description: 'Total number of tokens used'
            }
          }
        },
        
        cost: {
          type: 'object',
          additionalProperties: false,
          description: 'Cost information for the API call',
          properties: {
            promptCost: {
              type: 'number',
              minimum: 0,
              description: 'Cost for prompt tokens'
            },
            completionCost: {
              type: 'number',
              minimum: 0,
              description: 'Cost for completion tokens'
            },
            totalCost: {
              type: 'number',
              minimum: 0,
              description: 'Total cost for the API call'
            },
            currency: {
              type: 'string',
              pattern: '^[A-Z]{3}$',
              default: 'USD',
              description: 'Currency code (ISO 4217)'
            }
          }
        },
        
        parameters: {
          type: 'object',
          additionalProperties: true,
          description: 'Parameters used for the LLM request',
          properties: {
            temperature: {
              type: 'number',
              minimum: 0,
              maximum: 2,
              description: 'Temperature parameter for randomness'
            },
            maxTokens: {
              type: 'integer',
              minimum: 1,
              maximum: 100000,
              description: 'Maximum tokens to generate'
            },
            topP: {
              type: 'number',
              minimum: 0,
              maximum: 1,
              description: 'Top-p parameter for nucleus sampling'
            },
            frequencyPenalty: {
              type: 'number',
              minimum: -2,
              maximum: 2,
              description: 'Frequency penalty parameter'
            },
            presencePenalty: {
              type: 'number',
              minimum: -2,
              maximum: 2,
              description: 'Presence penalty parameter'
            }
          }
        },
        
        quality: {
          type: 'object',
          additionalProperties: false,
          description: 'Response quality indicators',
          properties: {
            coherence: {
              type: 'number',
              format: 'score',
              minimum: 0,
              maximum: 10,
              description: 'Coherence score (0-10)'
            },
            relevance: {
              type: 'number',
              format: 'score',
              minimum: 0,
              maximum: 10,
              description: 'Relevance score (0-10)'
            },
            completeness: {
              type: 'number',
              format: 'score',
              minimum: 0,
              maximum: 10,
              description: 'Completeness score (0-10)'
            },
            confidence: {
              type: 'number',
              format: 'percentage',
              minimum: 0,
              maximum: 100,
              description: 'Confidence percentage (0-100)'
            }
          }
        }
      }
    },
    
    error: {
      type: 'object',
      additionalProperties: false,
      description: 'Error information if the request failed',
      properties: {
        code: {
          type: 'string',
          minLength: 1,
          maxLength: 100,
          description: 'Error code'
        },
        message: {
          type: 'string',
          minLength: 1,
          maxLength: 1000,
          description: 'Error message'
        },
        type: {
          type: 'string',
          enum: ['api_error', 'network_error', 'timeout_error', 'rate_limit_error', 'authentication_error', 'validation_error'],
          description: 'Type of error encountered'
        },
        retryable: {
          type: 'boolean',
          description: 'Whether the error is retryable'
        },
        retryAfter: {
          type: 'integer',
          minimum: 0,
          maximum: 3600,
          description: 'Seconds to wait before retrying'
        }
      },
      required: ['code', 'message', 'type']
    },
    
    status: {
      type: 'string',
      enum: ['success', 'error', 'timeout', 'rate_limited', 'partial'],
      default: 'success',
      description: 'Status of the LLM response'
    },
    
    finishReason: {
      type: 'string',
      enum: ['stop', 'length', 'content_filter', 'function_call', 'timeout'],
      description: 'Reason why the generation finished'
    },
    
    alternatives: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          content: {
            type: 'string',
            maxLength: 10000
          },
          finishReason: {
            type: 'string',
            enum: ['stop', 'length', 'content_filter']
          }
        },
        required: ['content']
      },
      maxItems: 10,
      description: 'Alternative responses if multiple were generated'
    }
  },
  
  // Conditional validation rules
  allOf: [
    {
      // If error exists, status should not be success
      if: {
        properties: {
          error: { type: 'object' }
        },
        required: ['error']
      },
      then: {
        properties: {
          status: {
            not: { const: 'success' }
          }
        }
      }
    },
    {
      // If status is success, content should not be empty (unless it's a function call)
      if: {
        properties: {
          status: { const: 'success' },
          finishReason: { not: { const: 'function_call' } }
        }
      },
      then: {
        properties: {
          content: {
            minLength: 1
          }
        }
      }
    },
    {
      // Token usage validation - simplified without $data references
      if: {
        properties: {
          metadata: {
            properties: {
              tokenUsage: {
                properties: {
                  promptTokens: { type: 'integer' },
                  completionTokens: { type: 'integer' }
                },
                required: ['promptTokens', 'completionTokens']
              }
            }
          }
        }
      },
      then: {
        properties: {
          metadata: {
            properties: {
              tokenUsage: {
                properties: {
                  totalTokens: {
                    type: 'integer',
                    minimum: 0,
                    maximum: 2000000
                  }
                }
              }
            }
          }
        }
      }
    }
  ],
  
  examples: [
    {
      content: "Hello! I'm an AI assistant. How can I help you today?",
      metadata: {
        model: "gpt-3.5-turbo",
        timestamp: 1703123456789,
        requestId: "req_123e4567-e89b-12d3-a456-426614174000",
        provider: "openai",
        responseTime: 1250,
        tokenUsage: {
          promptTokens: 15,
          completionTokens: 12,
          totalTokens: 27
        },
        cost: {
          promptCost: 0.000015,
          completionCost: 0.000024,
          totalCost: 0.000039,
          currency: "USD"
        },
        parameters: {
          temperature: 0.7,
          maxTokens: 150,
          topP: 1.0
        },
        quality: {
          coherence: 9.2,
          relevance: 8.8,
          completeness: 8.5,
          confidence: 92.3
        }
      },
      status: "success",
      finishReason: "stop"
    },
    {
      content: "",
      metadata: {
        model: "claude-3-sonnet",
        timestamp: 1703123456789,
        provider: "anthropic"
      },
      error: {
        code: "rate_limit_exceeded",
        message: "Rate limit exceeded. Please try again later.",
        type: "rate_limit_error",
        retryable: true,
        retryAfter: 60
      },
      status: "rate_limited"
    }
  ],
  
  errorMessage: {
    type: 'LLM response must be an object',
    required: 'LLM response must have content and metadata fields',
    additionalProperties: 'LLM response contains unknown properties',
    properties: {
      content: 'Content must be a string (max 10000 characters)',
      metadata: {
        type: 'Metadata must be an object',
        required: 'Metadata must have model and timestamp fields',
        properties: {
          model: 'Model must be a valid model name',
          timestamp: 'Timestamp must be a valid Unix timestamp',
          responseTime: 'Response time must be a positive number (max 300000ms)',
          tokenUsage: 'Token usage must be a valid object with numeric values',
          cost: 'Cost must be a valid object with numeric values',
          parameters: 'Parameters must be a valid object',
          quality: 'Quality scores must be valid numbers in appropriate ranges'
        }
      },
      error: {
        type: 'Error must be an object',
        required: 'Error must have code, message, and type fields',
        properties: {
          code: 'Error code must be a non-empty string',
          message: 'Error message must be a non-empty string',
          type: 'Error type must be a valid error type',
          retryAfter: 'Retry after must be a positive integer (max 3600 seconds)'
        }
      },
      status: 'Status must be a valid status value',
      finishReason: 'Finish reason must be a valid reason',
      alternatives: 'Alternatives must be an array of response objects (max 10)'
    }
  }
};