/**
 * Test Facts JSON Schema Definition
 *
 * This schema validates test fact files used in the LLM Memory Test Application.
 * Test facts are structured data objects that contain information to be injected
 * into conversations and later tested for memory retention.
 *
 * Schema validates:
 * - Array structure with fact objects
 * - Required fields: id, fact, question, answer, complexity
 * - Data types and format constraints
 * - Complexity levels (simple/complex)
 * - Content length constraints
 * - ID uniqueness and sequencing
 *
 * @fileoverview JSON Schema for test facts validation
 * @module testFactsSchema
 */

module.exports = {
  $id: 'https://llm-memory-test.com/schemas/test-facts.json',
  $schema: 'http://json-schema.org/draft-07/schema#',
  title: 'Test Facts Schema',
  description: 'Schema for validating test fact files used in LLM memory testing',
  version: '1.0.0',
  
  type: 'array',
  minItems: 1,
  maxItems: 1000,
  
  items: {
    type: 'object',
    required: ['id', 'fact', 'question', 'answer', 'complexity'],
    additionalProperties: false,
    
    properties: {
      id: {
        type: 'integer',
        minimum: 1,
        maximum: 10000,
        description: 'Unique identifier for the test fact'
      },
      
      fact: {
        type: 'string',
        minLength: 1,
        maxLength: 2000,
        description: 'The factual statement to be injected into the conversation',
        pattern: '^(?!\\s*$).+', // Not just whitespace
      },
      
      question: {
        type: 'string',
        minLength: 1,
        maxLength: 500,
        description: 'Question to test retention of the fact',
        pattern: '^(?!\\s*$).+', // Not just whitespace
      },
      
      answer: {
        type: 'string',
        minLength: 1,
        maxLength: 1000,
        description: 'Expected answer to the question',
        pattern: '^(?!\\s*$).+', // Not just whitespace
      },
      
      complexity: {
        type: 'string',
        format: 'complexity-level',
        enum: ['simple', 'complex'],
        description: 'Complexity level of the fact for categorized evaluation'
      },
      
      category: {
        type: 'string',
        minLength: 1,
        maxLength: 100,
        description: 'Optional category for grouping related facts',
        pattern: '^[a-zA-Z0-9\\s\\-_]+$'
      },
      
      tags: {
        type: 'array',
        items: {
          type: 'string',
          minLength: 1,
          maxLength: 50,
          pattern: '^[a-zA-Z0-9\\-_]+$'
        },
        maxItems: 10,
        uniqueItems: true,
        description: 'Optional tags for fact classification'
      },
      
      metadata: {
        type: 'object',
        additionalProperties: true,
        description: 'Optional metadata for additional fact information',
        properties: {
          source: {
            type: 'string',
            maxLength: 200,
            description: 'Source of the fact information'
          },
          difficulty: {
            type: 'number',
            minimum: 1,
            maximum: 10,
            description: 'Difficulty rating from 1-10'
          },
          expectedRetentionTime: {
            type: 'integer',
            minimum: 1,
            maximum: 86400,
            description: 'Expected retention time in seconds'
          }
        }
      }
    }
  },
  
  // Additional validation rules at the array level
  allOf: [
    {
      // Ensure ID uniqueness
      not: {
        anyOf: [
          {
            type: 'array',
            contains: {
              type: 'object',
              properties: {
                id: { type: 'integer' }
              }
            },
            minContains: 2,
            maxContains: 2,
            items: {
              if: {
                type: 'object',
                properties: {
                  id: { type: 'integer' }
                }
              },
              then: {
                properties: {
                  id: { const: { $data: '1/id' } }
                }
              }
            }
          }
        ]
      },
      errorMessage: 'All fact IDs must be unique'
    }
  ],
  
  examples: [
    [
      {
        id: 1,
        fact: "My name is John Smith.",
        question: "What is my name?",
        answer: "John Smith",
        complexity: "simple",
        category: "personal",
        tags: ["name", "identity"],
        metadata: {
          source: "user_profile",
          difficulty: 2,
          expectedRetentionTime: 300
        }
      },
      {
        id: 2,
        fact: "I have a master's degree in Computer Science from MIT, which I completed in 2015 with a focus on artificial intelligence and machine learning.",
        question: "What is my educational background?",
        answer: "Master's degree in Computer Science from MIT in 2015, focused on AI and machine learning",
        complexity: "complex",
        category: "education",
        tags: ["education", "degree", "university"],
        metadata: {
          source: "academic_records",
          difficulty: 7,
          expectedRetentionTime: 600
        }
      }
    ]
  ],
  
  errorMessage: {
    type: 'Test facts must be an array of fact objects',
    minItems: 'At least one test fact is required',
    maxItems: 'Too many test facts (maximum 1000)',
    items: {
      type: 'Each test fact must be an object',
      required: 'Test fact must have id, fact, question, answer, and complexity fields',
      additionalProperties: 'Test fact contains unknown properties',
      properties: {
        id: 'Fact ID must be a positive integer between 1 and 10000',
        fact: 'Fact statement must be a non-empty string (max 2000 characters)',
        question: 'Question must be a non-empty string (max 500 characters)',
        answer: 'Answer must be a non-empty string (max 1000 characters)',
        complexity: 'Complexity must be either "simple" or "complex"',
        category: 'Category must be alphanumeric string (max 100 characters)',
        tags: 'Tags must be an array of unique alphanumeric strings (max 10 tags)',
        metadata: 'Metadata must be an object with valid properties'
      }
    }
  }
};