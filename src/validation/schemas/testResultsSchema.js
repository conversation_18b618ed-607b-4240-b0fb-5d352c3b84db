/**
 * Test Results JSON Schema Definition
 *
 * This schema validates test result data generated by the LLM Memory Test Application.
 * Test results contain comprehensive information about memory retention evaluation,
 * including scores, detailed evaluation results, performance metrics, and metadata.
 *
 * Schema validates:
 * - Overall and categorized scores
 * - Individual evaluation results
 * - Performance metrics and statistics
 * - Test configuration and metadata
 * - Conversation logs and memory contexts
 * - API usage and system resource data
 *
 * @fileoverview JSON Schema for test results validation
 * @module testResultsSchema
 */

module.exports = {
  $id: 'https://llm-memory-test.com/schemas/test-results.json',
  $schema: 'http://json-schema.org/draft-07/schema#',
  title: 'Test Results Schema',
  description: 'Schema for validating LLM memory test results and evaluation data',
  version: '1.0.0',
  
  type: 'object',
  required: ['metadata', 'scores', 'evaluationResults'],
  additionalProperties: false,
  
  properties: {
    metadata: {
      type: 'object',
      required: ['sessionId', 'timestamp', 'memoryType', 'testScenario'],
      additionalProperties: true,
      description: 'Test execution metadata and configuration',
      
      properties: {
        sessionId: {
          type: 'string',
          format: 'uuid',
          description: 'Unique session identifier for the test run'
        },
        
        timestamp: {
          type: 'number',
          format: 'timestamp',
          description: 'Unix timestamp when the test was executed'
        },
        
        memoryType: {
          type: 'string',
          enum: ['simple', 'summary', 'summary_with_knowledge'],
          description: 'Type of memory implementation tested'
        },
        
        testScenario: {
          type: 'string',
          minLength: 1,
          maxLength: 100,
          pattern: '^[a-zA-Z0-9_\\-]+$',
          description: 'Test scenario identifier'
        },
        
        testFactsCount: {
          type: 'integer',
          minimum: 1,
          maximum: 1000,
          description: 'Number of facts tested'
        },
        
        messagesBetweenFacts: {
          type: 'integer',
          minimum: 1,
          maximum: 50,
          description: 'Number of messages between fact injections'
        },
        
        models: {
          type: 'object',
          additionalProperties: false,
          description: 'Models used in the test',
          properties: {
            user: {
              type: 'string',
              format: 'model-name',
              description: 'User model identifier'
            },
            assistant: {
              type: 'string',
              format: 'model-name',
              description: 'Assistant model identifier'
            },
            evaluator: {
              type: 'string',
              format: 'model-name',
              description: 'Evaluator model identifier'
            },
            summary: {
              type: 'string',
              format: 'model-name',
              description: 'Summary model identifier'
            },
            knowledgeExtraction: {
              type: 'string',
              format: 'model-name',
              description: 'Knowledge extraction model identifier'
            }
          }
        },
        
        configuration: {
          type: 'object',
          additionalProperties: true,
          description: 'Test configuration parameters',
          properties: {
            memoryContextWindow: {
              type: 'integer',
              minimum: 1,
              maximum: 100
            },
            summaryThreshold: {
              type: 'integer',
              minimum: 1,
              maximum: 1000
            },
            enableKnowledgeExtraction: {
              type: 'boolean'
            }
          }
        }
      }
    },
    
    scores: {
      type: 'object',
      required: ['overall'],
      additionalProperties: false,
      description: 'Test performance scores',
      
      properties: {
        overall: {
          type: 'number',
          format: 'percentage',
          minimum: 0,
          maximum: 100,
          description: 'Overall memory retention score as percentage'
        },
        
        simple: {
          type: 'number',
          format: 'percentage',
          minimum: 0,
          maximum: 100,
          description: 'Score for simple facts as percentage'
        },
        
        complex: {
          type: 'number',
          format: 'percentage',
          minimum: 0,
          maximum: 100,
          description: 'Score for complex facts as percentage'
        },
        
        byCategory: {
          type: 'object',
          additionalProperties: {
            type: 'number',
            format: 'percentage',
            minimum: 0,
            maximum: 100
          },
          description: 'Scores grouped by fact category'
        }
      }
    },
    
    evaluationResults: {
      type: 'array',
      minItems: 1,
      maxItems: 1000,
      description: 'Detailed evaluation results for each tested fact',
      
      items: {
        type: 'object',
        required: ['factId', 'fact', 'question', 'expectedAnswer', 'actualResponse', 'score'],
        additionalProperties: false,
        
        properties: {
          factId: {
            type: 'integer',
            minimum: 1,
            description: 'Unique identifier of the tested fact'
          },
          
          fact: {
            type: 'string',
            minLength: 1,
            maxLength: 2000,
            description: 'The original fact statement'
          },
          
          question: {
            type: 'string',
            minLength: 1,
            maxLength: 500,
            description: 'Question asked to test fact retention'
          },
          
          expectedAnswer: {
            type: 'string',
            minLength: 1,
            maxLength: 1000,
            description: 'Expected answer to the question'
          },
          
          actualResponse: {
            type: 'string',
            minLength: 0,
            maxLength: 5000,
            description: 'Actual response from the memory system'
          },
          
          score: {
            type: 'number',
            format: 'score',
            minimum: 0,
            maximum: 10,
            description: 'Evaluation score (0-10)'
          },
          
          complexity: {
            type: 'string',
            format: 'complexity-level',
            enum: ['simple', 'complex'],
            description: 'Complexity level of the fact'
          },
          
          category: {
            type: 'string',
            minLength: 1,
            maxLength: 100,
            description: 'Category of the fact'
          },
          
          memoryContext: {
            type: 'string',
            maxLength: 50000,
            description: 'Memory context available during evaluation'
          },
          
          evaluationMetadata: {
            type: 'object',
            additionalProperties: true,
            description: 'Additional evaluation metadata',
            properties: {
              evaluationTime: {
                type: 'number',
                minimum: 0,
                description: 'Time taken for evaluation in milliseconds'
              },
              
              confidence: {
                type: 'number',
                format: 'percentage',
                minimum: 0,
                maximum: 100,
                description: 'Confidence in the evaluation score'
              },
              
              reasoning: {
                type: 'string',
                maxLength: 2000,
                description: 'Reasoning behind the evaluation score'
              },
              
              partialCredit: {
                type: 'boolean',
                description: 'Whether partial credit was awarded'
              }
            }
          }
        }
      }
    },
    
    conversationLog: {
      type: 'array',
      items: {
        type: 'object',
        required: ['role', 'content', 'timestamp'],
        properties: {
          role: {
            type: 'string',
            enum: ['user', 'assistant', 'system']
          },
          content: {
            type: 'string',
            maxLength: 10000
          },
          timestamp: {
            type: 'number',
            format: 'timestamp'
          },
          cycle: {
            type: 'integer',
            minimum: 1
          }
        }
      },
      description: 'Complete conversation log from the test'
    },
    
    memoryContexts: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          factId: {
            type: 'integer',
            minimum: 1
          },
          context: {
            type: 'string',
            maxLength: 50000
          },
          timestamp: {
            type: 'number',
            format: 'timestamp'
          }
        }
      },
      description: 'Memory contexts captured during the test'
    },
    
    performanceMetrics: {
      type: 'object',
      additionalProperties: true,
      description: 'Performance metrics and statistics',
      properties: {
        session: {
          type: 'object',
          properties: {
            totalDuration: {
              type: 'number',
              minimum: 0,
              description: 'Total test duration in milliseconds'
            },
            startTime: {
              type: 'number',
              format: 'timestamp'
            },
            endTime: {
              type: 'number',
              format: 'timestamp'
            }
          }
        },
        
        api: {
          type: 'object',
          properties: {
            totalCalls: {
              type: 'integer',
              minimum: 0,
              description: 'Total number of API calls made'
            },
            successfulCalls: {
              type: 'integer',
              minimum: 0,
              description: 'Number of successful API calls'
            },
            failedCalls: {
              type: 'integer',
              minimum: 0,
              description: 'Number of failed API calls'
            },
            totalTokens: {
              type: 'integer',
              minimum: 0,
              description: 'Total tokens used across all calls'
            },
            averageResponseTime: {
              type: 'number',
              minimum: 0,
              description: 'Average API response time in milliseconds'
            },
            successRate: {
              type: 'number',
              format: 'percentage',
              minimum: 0,
              maximum: 100,
              description: 'API call success rate as percentage'
            }
          }
        },
        
        operations: {
          type: 'object',
          properties: {
            total: {
              type: 'integer',
              minimum: 0,
              description: 'Total number of operations performed'
            },
            successful: {
              type: 'integer',
              minimum: 0,
              description: 'Number of successful operations'
            },
            failed: {
              type: 'integer',
              minimum: 0,
              description: 'Number of failed operations'
            }
          }
        }
      }
    },
    
    systemStats: {
      type: 'object',
      additionalProperties: true,
      description: 'System resource usage statistics',
      properties: {
        memory: {
          type: 'object',
          properties: {
            peak: {
              type: 'number',
              minimum: 0,
              description: 'Peak memory usage in MB'
            },
            average: {
              type: 'number',
              minimum: 0,
              description: 'Average memory usage in MB'
            },
            utilization: {
              type: 'number',
              format: 'percentage',
              minimum: 0,
              maximum: 100,
              description: 'Memory utilization percentage'
            }
          }
        },
        
        cpu: {
          type: 'object',
          properties: {
            average: {
              type: 'number',
              format: 'percentage',
              minimum: 0,
              maximum: 100,
              description: 'Average CPU usage percentage'
            },
            peak: {
              type: 'number',
              format: 'percentage',
              minimum: 0,
              maximum: 100,
              description: 'Peak CPU usage percentage'
            }
          }
        }
      }
    },
    
    analysis: {
      type: 'object',
      additionalProperties: true,
      description: 'Analysis and insights from the test results',
      properties: {
        trends: {
          type: 'object',
          additionalProperties: true,
          description: 'Performance trends identified'
        },
        
        insights: {
          type: 'array',
          items: {
            type: 'string',
            maxLength: 500
          },
          description: 'Key insights from the test'
        },
        
        recommendations: {
          type: 'array',
          items: {
            type: 'string',
            maxLength: 500
          },
          description: 'Recommendations for improvement'
        }
      }
    }
  },
  
  examples: [
    {
      metadata: {
        sessionId: '550e8400-e29b-41d4-a716-446655440000',
        timestamp: 1703123456789,
        memoryType: 'summary',
        testScenario: 'customer',
        testFactsCount: 5,
        messagesBetweenFacts: 3,
        models: {
          user: 'gpt-3.5-turbo',
          assistant: 'claude-3-sonnet',
          evaluator: 'gpt-4',
          summary: 'gpt-3.5-turbo',
          knowledgeExtraction: 'gpt-4'
        },
        configuration: {
          memoryContextWindow: 10,
          summaryThreshold: 20,
          enableKnowledgeExtraction: true
        }
      },
      scores: {
        overall: 78.5,
        simple: 85.2,
        complex: 71.8,
        byCategory: {
          personal: 82.0,
          technical: 75.0
        }
      },
      evaluationResults: [
        {
          factId: 1,
          fact: 'My name is John Smith.',
          question: 'What is my name?',
          expectedAnswer: 'John Smith',
          actualResponse: 'Your name is John Smith.',
          score: 9.5,
          complexity: 'simple',
          category: 'personal',
          memoryContext: 'Recent conversation:\nuser: My name is John Smith.\nassistant: Nice to meet you, John!',
          evaluationMetadata: {
            evaluationTime: 1250,
            confidence: 95.5,
            reasoning: 'Perfect recall of the name with correct context',
            partialCredit: false
          }
        }
      ],
      conversationLog: [
        {
          role: 'user',
          content: 'My name is John Smith.',
          timestamp: 1703123456789,
          cycle: 1
        },
        {
          role: 'assistant',
          content: 'Nice to meet you, John!',
          timestamp: 1703123458000,
          cycle: 2
        }
      ],
      performanceMetrics: {
        session: {
          totalDuration: 45000,
          startTime: 1703123456789,
          endTime: 1703123501789
        },
        api: {
          totalCalls: 15,
          successfulCalls: 14,
          failedCalls: 1,
          totalTokens: 2500,
          averageResponseTime: 1200,
          successRate: 93.3
        },
        operations: {
          total: 25,
          successful: 24,
          failed: 1
        }
      },
      systemStats: {
        memory: {
          peak: 128.5,
          average: 95.2,
          utilization: 12.8
        },
        cpu: {
          average: 15.5,
          peak: 45.2
        }
      },
      analysis: {
        trends: {
          scoreDeclineOverTime: false,
          memoryEfficiency: 'high'
        },
        insights: [
          'Strong performance on simple facts',
          'Memory system handled context well'
        ],
        recommendations: [
          'Consider testing with more complex scenarios',
          'Monitor performance with larger fact sets'
        ]
      }
    }
  ],
  
  errorMessage: {
    type: 'Test results must be an object',
    required: 'Test results must have metadata, scores, and evaluationResults fields',
    additionalProperties: 'Test results contain unknown properties',
    properties: {
      metadata: {
        type: 'Metadata must be an object',
        required: 'Metadata must have sessionId, timestamp, memoryType, and testScenario fields',
        properties: {
          sessionId: 'Session ID must be a valid UUID',
          timestamp: 'Timestamp must be a valid Unix timestamp',
          memoryType: 'Memory type must be "simple", "summary", or "summary_with_knowledge"',
          testScenario: 'Test scenario must be a valid identifier',
          testFactsCount: 'Test facts count must be between 1 and 1000',
          messagesBetweenFacts: 'Messages between facts must be between 1 and 50'
        }
      },
      scores: {
        type: 'Scores must be an object',
        required: 'Scores must have an overall field',
        properties: {
          overall: 'Overall score must be a percentage (0-100)',
          simple: 'Simple score must be a percentage (0-100)',
          complex: 'Complex score must be a percentage (0-100)',
          byCategory: 'Category scores must be percentages (0-100)'
        }
      },
      evaluationResults: {
        type: 'Evaluation results must be an array',
        minItems: 'At least one evaluation result is required',
        maxItems: 'Too many evaluation results (maximum 1000)',
        items: {
          type: 'Each evaluation result must be an object',
          required: 'Evaluation result must have factId, fact, question, expectedAnswer, actualResponse, and score fields',
          properties: {
            factId: 'Fact ID must be a positive integer',
            fact: 'Fact must be a non-empty string (max 2000 characters)',
            question: 'Question must be a non-empty string (max 500 characters)',
            expectedAnswer: 'Expected answer must be a non-empty string (max 1000 characters)',
            actualResponse: 'Actual response must be a string (max 5000 characters)',
            score: 'Score must be between 0 and 10',
            complexity: 'Complexity must be "simple" or "complex"',
            memoryContext: 'Memory context must be a string (max 50000 characters)'
          }
        }
      },
      performanceMetrics: 'Performance metrics must be a valid object with numeric values',
      systemStats: 'System stats must be a valid object with resource usage data'
    }
  }
};