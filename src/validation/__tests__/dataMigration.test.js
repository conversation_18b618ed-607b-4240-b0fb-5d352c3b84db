/**
 * Tests for DataMigrationManager
 */

const { DataMigrationManager, MigrationError, SCHEMA_VERSIONS } = require('../dataMigration');
const fs = require('fs').promises;
const path = require('path');

// Mock the logger to avoid console output during tests
jest.mock('../../utils/logger', () => ({
  defaultLogger: {
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
  }
}));

describe('DataMigrationManager', () => {
  let migrationManager;

  beforeEach(() => {
    migrationManager = new DataMigrationManager();
  });

  describe('detectSchemaVersion', () => {
    it('should detect test facts version 1.0.0', () => {
      const testFacts = [
        {
          id: 1,
          fact: 'Test fact',
          question: 'Test question?',
          answer: 'Test answer',
          complexity: 'simple'
        }
      ];

      const version = migrationManager.detectSchemaVersion('testFacts', testFacts);
      expect(version).toBe('1.0.0');
    });

    it('should detect test facts version 1.1.0', () => {
      const testFacts = [
        {
          id: 1,
          fact: 'Test fact',
          question: 'Test question?',
          answer: 'Test answer',
          complexity: 'simple',
          category: 'general',
          tags: ['test']
        }
      ];

      const version = migrationManager.detectSchemaVersion('testFacts', testFacts);
      expect(version).toBe('1.1.0');
    });

    it('should detect test facts version 1.2.0', () => {
      const testFacts = [
        {
          id: 1,
          fact: 'Test fact',
          question: 'Test question?',
          answer: 'Test answer',
          complexity: 'simple',
          category: 'general',
          tags: ['test'],
          metadata: {
            created: '2024-01-01T00:00:00Z',
            version: '1.2.0'
          }
        }
      ];

      const version = migrationManager.detectSchemaVersion('testFacts', testFacts);
      expect(version).toBe('1.2.0');
    });

    it('should detect test results version 1.0.0', () => {
      const testResults = {
        sessionId: 'test-session',
        scores: { overall: 85 },
        evaluationResults: []
      };

      const version = migrationManager.detectSchemaVersion('testResults', testResults);
      expect(version).toBe('1.0.0');
    });

    it('should detect test results version 1.1.0', () => {
      const testResults = {
        sessionId: 'test-session',
        scores: { overall: 85 },
        evaluationResults: [],
        performanceMetrics: {
          testExecution: { duration: 1000 }
        }
      };

      const version = migrationManager.detectSchemaVersion('testResults', testResults);
      expect(version).toBe('1.1.0');
    });

    it('should detect conversation log version 1.0.0', () => {
      const conversationLog = [
        {
          role: 'user',
          content: 'Hello'
        }
      ];

      const version = migrationManager.detectSchemaVersion('conversationLog', conversationLog);
      expect(version).toBe('1.0.0');
    });

    it('should detect conversation log version 1.1.0', () => {
      const conversationLog = [
        {
          role: 'user',
          content: 'Hello',
          timestamp: '2024-01-01T00:00:00Z',
          metadata: { test: true }
        }
      ];

      const version = migrationManager.detectSchemaVersion('conversationLog', conversationLog);
      expect(version).toBe('1.1.0');
    });

    it('should throw error for unknown data type', () => {
      expect(() => {
        migrationManager.detectSchemaVersion('unknownType', {});
      }).toThrow(MigrationError);
    });
  });

  describe('getLatestVersion', () => {
    it('should return latest version for test facts', () => {
      const version = migrationManager.getLatestVersion('testFacts');
      expect(version).toBe('1.2.0');
    });

    it('should return latest version for test results', () => {
      const version = migrationManager.getLatestVersion('testResults');
      expect(version).toBe('1.3.0');
    });

    it('should return latest version for conversation log', () => {
      const version = migrationManager.getLatestVersion('conversationLog');
      expect(version).toBe('1.2.0');
    });

    it('should return default version for unknown type', () => {
      const version = migrationManager.getLatestVersion('unknownType');
      expect(version).toBe('1.0.0');
    });
  });

  describe('findMigrationPath', () => {
    it('should find migration path from 1.0.0 to 1.2.0', () => {
      const path = migrationManager.findMigrationPath('testFacts', '1.0.0', '1.2.0');
      expect(path).toEqual(['1.0.0_to_1.1.0', '1.1.0_to_1.2.0']);
    });

    it('should return empty path for same versions', () => {
      const path = migrationManager.findMigrationPath('testFacts', '1.2.0', '1.2.0');
      expect(path).toEqual([]);
    });

    it('should return null for impossible migration', () => {
      const path = migrationManager.findMigrationPath('testFacts', '2.0.0', '1.0.0');
      expect(path).toBeNull();
    });
  });

  describe('migrateToLatest', () => {
    it('should migrate test facts from 1.0.0 to latest', () => {
      const testFacts = [
        {
          id: 1,
          fact: 'Test fact',
          question: 'Test question?',
          answer: 'Test answer',
          complexity: 'simple'
        }
      ];

      const result = migrationManager.migrateToLatest('testFacts', testFacts);

      expect(result.success).toBe(true);
      expect(result.fromVersion).toBe('1.0.0');
      expect(result.toVersion).toBe('1.2.0');
      expect(result.data[0].category).toBeDefined();
      expect(result.data[0].tags).toBeDefined();
      expect(result.data[0].metadata).toBeDefined();
    });

    it('should return unchanged data if already at latest version', () => {
      const testFacts = [
        {
          id: 1,
          fact: 'Test fact',
          question: 'Test question?',
          answer: 'Test answer',
          complexity: 'simple',
          category: 'general',
          tags: ['test'],
          metadata: {
            created: '2024-01-01T00:00:00Z',
            version: '1.2.0'
          }
        }
      ];

      const result = migrationManager.migrateToLatest('testFacts', testFacts);

      expect(result.success).toBe(true);
      expect(result.fromVersion).toBe('1.2.0');
      expect(result.toVersion).toBe('1.2.0');
      expect(result.migrationsApplied).toHaveLength(0);
    });

    it('should migrate test results from 1.0.0 to latest', () => {
      const testResults = {
        sessionId: 'test-session',
        scores: { overall: 85 },
        evaluationResults: []
      };

      const result = migrationManager.migrateToLatest('testResults', testResults);

      expect(result.success).toBe(true);
      expect(result.fromVersion).toBe('1.0.0');
      expect(result.toVersion).toBe('1.3.0');
      expect(result.data.performanceMetrics).toBeDefined();
      expect(result.data.memoryContexts).toBeDefined();
      expect(result.data.analysis).toBeDefined();
    });

    it('should migrate conversation log from 1.0.0 to latest', () => {
      const conversationLog = [
        {
          role: 'user',
          content: 'Hello'
        }
      ];

      const result = migrationManager.migrateToLatest('conversationLog', conversationLog);

      expect(result.success).toBe(true);
      expect(result.fromVersion).toBe('1.0.0');
      expect(result.toVersion).toBe('1.2.0');
      expect(result.data[0].timestamp).toBeDefined();
      expect(result.data[0].metadata).toBeDefined();
      expect(result.data[0].factId).toBeDefined();
    });
  });

  describe('migrateFile', () => {
    it('should migrate a test facts file', async () => {
      const testFacts = [
        {
          id: 1,
          fact: 'Test fact',
          question: 'Test question?',
          answer: 'Test answer',
          complexity: 'simple'
        }
      ];

      const tempFilePath = path.join(__dirname, 'temp_migrate_test.json');
      await fs.writeFile(tempFilePath, JSON.stringify(testFacts, null, 2));

      try {
        const result = await migrationManager.migrateFile(tempFilePath, 'testFacts', {
          backupOriginal: false
        });

        expect(result.success).toBe(true);
        expect(result.fromVersion).toBe('1.0.0');
        expect(result.toVersion).toBe('1.2.0');

        // Verify the file was updated
        const updatedContent = await fs.readFile(tempFilePath, 'utf8');
        const updatedData = JSON.parse(updatedContent);
        expect(updatedData[0].category).toBeDefined();
        expect(updatedData[0].metadata).toBeDefined();

      } finally {
        await fs.unlink(tempFilePath).catch(() => {});
      }
    });

    it('should create backup when requested', async () => {
      const testFacts = [
        {
          id: 1,
          fact: 'Test fact',
          question: 'Test question?',
          answer: 'Test answer',
          complexity: 'simple'
        }
      ];

      const tempFilePath = path.join(__dirname, 'temp_backup_test.json');
      await fs.writeFile(tempFilePath, JSON.stringify(testFacts, null, 2));

      try {
        const result = await migrationManager.migrateFile(tempFilePath, 'testFacts', {
          backupOriginal: true
        });

        expect(result.success).toBe(true);
        expect(result.backupCreated).toBe(true);

        // Check if backup file exists
        const files = await fs.readdir(__dirname);
        const backupFiles = files.filter(file => file.startsWith('temp_backup_test.json.backup.'));
        expect(backupFiles.length).toBeGreaterThan(0);

        // Clean up backup files
        for (const backupFile of backupFiles) {
          await fs.unlink(path.join(__dirname, backupFile)).catch(() => {});
        }

      } finally {
        await fs.unlink(tempFilePath).catch(() => {});
      }
    });

    it('should handle non-existent files', async () => {
      await expect(
        migrationManager.migrateFile('/non/existent/file.json', 'testFacts')
      ).rejects.toThrow(MigrationError);
    });

    it('should handle invalid JSON files', async () => {
      const tempFilePath = path.join(__dirname, 'temp_invalid.json');
      await fs.writeFile(tempFilePath, '{ invalid json }');

      try {
        await expect(
          migrationManager.migrateFile(tempFilePath, 'testFacts')
        ).rejects.toThrow(MigrationError);
      } finally {
        await fs.unlink(tempFilePath).catch(() => {});
      }
    });
  });

  describe('validateMigratedData', () => {
    it('should validate valid test facts data', () => {
      const validTestFacts = [
        {
          id: 1,
          fact: 'Test fact',
          question: 'Test question?',
          answer: 'Test answer',
          complexity: 'simple'
        }
      ];

      const result = migrationManager.validateMigratedData('testFacts', validTestFacts, '1.0.0');

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.version).toBe('1.0.0');
    });

    it('should detect invalid test facts data', () => {
      const invalidTestFacts = [
        {
          id: 1,
          // Missing required fields
        }
      ];

      const result = migrationManager.validateMigratedData('testFacts', invalidTestFacts, '1.0.0');

      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should handle unknown data types', () => {
      const result = migrationManager.validateMigratedData('unknownType', {}, '1.0.0');

      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.message.includes('Unknown data type'))).toBe(true);
    });
  });

  describe('createMigrationReport', () => {
    it('should create a comprehensive migration report', () => {
      const migrationResult = {
        success: true,
        fromVersion: '1.0.0',
        toVersion: '1.2.0',
        migrationsApplied: ['1.0.0_to_1.1.0', '1.1.0_to_1.2.0'],
        data: [{ id: 1, fact: 'test' }]
      };

      const report = migrationManager.createMigrationReport(migrationResult);

      expect(report.success).toBe(true);
      expect(report.migration.fromVersion).toBe('1.0.0');
      expect(report.migration.toVersion).toBe('1.2.0');
      expect(report.migration.migrationsApplied).toHaveLength(2);
      expect(report.dataInfo.recordCount).toBe(1);
      expect(report.recommendations).toBeDefined();
      expect(Array.isArray(report.recommendations)).toBe(true);
    });

    it('should include appropriate recommendations', () => {
      const migrationResult = {
        success: true,
        fromVersion: '1.0.0',
        toVersion: '1.2.0',
        migrationsApplied: ['1.0.0_to_1.1.0', '1.1.0_to_1.2.0'],
        data: [{ id: 1, fact: 'test' }]
      };

      const report = migrationManager.createMigrationReport(migrationResult);

      expect(report.recommendations.some(rec => rec.includes('Multiple migrations'))).toBe(true);
      expect(report.recommendations.some(rec => rec.includes('early version'))).toBe(true);
    });
  });

  describe('migration functions', () => {
    describe('test facts migrations', () => {
      it('should migrate from 1.0.0 to 1.1.0', () => {
        const testFacts = [
          {
            id: 1,
            fact: 'I work as a software engineer',
            question: 'What is my profession?',
            answer: 'Software engineer',
            complexity: 'simple'
          }
        ];

        const migrated = migrationManager.migrateTestFacts_1_0_to_1_1(testFacts);

        expect(migrated[0].category).toBeDefined();
        expect(migrated[0].tags).toBeDefined();
        expect(Array.isArray(migrated[0].tags)).toBe(true);
      });

      it('should migrate from 1.1.0 to 1.2.0', () => {
        const testFacts = [
          {
            id: 1,
            fact: 'Test fact',
            question: 'Test question?',
            answer: 'Test answer',
            complexity: 'simple',
            category: 'general',
            tags: ['test']
          }
        ];

        const migrated = migrationManager.migrateTestFacts_1_1_to_1_2(testFacts);

        expect(migrated[0].metadata).toBeDefined();
        expect(migrated[0].metadata.version).toBe('1.2.0');
        expect(migrated[0].validation).toBeDefined();
      });
    });

    describe('test results migrations', () => {
      it('should migrate from 1.0.0 to 1.1.0', () => {
        const testResults = {
          sessionId: 'test-session',
          scores: { overall: 85 },
          evaluationResults: [{ factId: 1, score: 8 }]
        };

        const migrated = migrationManager.migrateTestResults_1_0_to_1_1(testResults);

        expect(migrated.performanceMetrics).toBeDefined();
        expect(migrated.performanceMetrics.testExecution).toBeDefined();
        expect(migrated.performanceMetrics.apiUsage).toBeDefined();
      });

      it('should migrate from 1.1.0 to 1.2.0', () => {
        const testResults = {
          sessionId: 'test-session',
          scores: { overall: 85 },
          evaluationResults: [{ factId: 1, score: 8 }],
          performanceMetrics: { testExecution: { duration: 1000 } }
        };

        const migrated = migrationManager.migrateTestResults_1_1_to_1_2(testResults);

        expect(migrated.memoryContexts).toBeDefined();
        expect(migrated.analysis).toBeDefined();
        expect(Array.isArray(migrated.memoryContexts)).toBe(true);
      });

      it('should migrate from 1.2.0 to 1.3.0', () => {
        const testResults = {
          sessionId: 'test-session',
          scores: { overall: 85 },
          evaluationResults: [],
          performanceMetrics: {},
          memoryContexts: [],
          analysis: {},
          metadata: { test: true }
        };

        const migrated = migrationManager.migrateTestResults_1_2_to_1_3(testResults);

        expect(migrated.metadata.migrationHistory).toBeDefined();
        expect(migrated.metadata.dataQuality).toBeDefined();
        expect(Array.isArray(migrated.metadata.migrationHistory)).toBe(true);
      });
    });

    describe('conversation log migrations', () => {
      it('should migrate from 1.0.0 to 1.1.0', () => {
        const conversationLog = [
          { role: 'user', content: 'Hello' },
          { role: 'assistant', content: 'Hi there!' }
        ];

        const migrated = migrationManager.migrateConversationLog_1_0_to_1_1(conversationLog);

        expect(migrated[0].timestamp).toBeDefined();
        expect(migrated[0].metadata).toBeDefined();
        expect(migrated[0].metadata.migrated).toBe(true);
      });

      it('should migrate from 1.1.0 to 1.2.0', () => {
        const conversationLog = [
          {
            role: 'user',
            content: 'Hello',
            timestamp: '2024-01-01T00:00:00Z',
            metadata: { test: true }
          }
        ];

        const migrated = migrationManager.migrateConversationLog_1_1_to_1_2(conversationLog);

        expect(migrated[0].factId).toBeDefined();
        expect(migrated[0].context).toBeDefined();
      });
    });
  });

  describe('helper methods', () => {
    it('should infer category correctly', () => {
      expect(migrationManager.inferCategory({ fact: 'My name is John' })).toBe('personal');
      expect(migrationManager.inferCategory({ fact: 'I work at Google' })).toBe('professional');
      expect(migrationManager.inferCategory({ fact: 'I like pizza' })).toBe('preference');
      expect(migrationManager.inferCategory({ fact: 'I live in New York' })).toBe('location');
      expect(migrationManager.inferCategory({ fact: 'Random fact' })).toBe('general');
    });

    it('should infer tags correctly', () => {
      const tags1 = migrationManager.inferTags({ fact: 'I am 25 years old' });
      expect(tags1).toContain('age');

      const tags2 = migrationManager.inferTags({ fact: 'My name is John' });
      expect(tags2).toContain('identity');

      const tags3 = migrationManager.inferTags({ fact: 'I work as an engineer' });
      expect(tags3).toContain('career');

      const tags4 = migrationManager.inferTags({ fact: 'I went to Harvard University' });
      expect(tags4).toContain('education');
    });
  });
});

describe('SCHEMA_VERSIONS', () => {
  it('should have defined schema versions', () => {
    expect(SCHEMA_VERSIONS).toBeDefined();
    expect(SCHEMA_VERSIONS.TEST_FACTS).toBeDefined();
    expect(SCHEMA_VERSIONS.TEST_RESULTS).toBeDefined();
    expect(SCHEMA_VERSIONS.CONVERSATION_LOG).toBeDefined();
  });

  it('should have version descriptions', () => {
    expect(SCHEMA_VERSIONS.TEST_FACTS['1.0.0']).toBeDefined();
    expect(SCHEMA_VERSIONS.TEST_RESULTS['1.0.0']).toBeDefined();
    expect(SCHEMA_VERSIONS.CONVERSATION_LOG['1.0.0']).toBeDefined();
  });
});

describe('MigrationError', () => {
  it('should create error with message and details', () => {
    const error = new MigrationError('Test error', { detail: 'test' });

    expect(error.name).toBe('MigrationError');
    expect(error.message).toBe('Test error');
    expect(error.details).toEqual({ detail: 'test' });
  });

  it('should create error with just message', () => {
    const error = new MigrationError('Test error');

    expect(error.name).toBe('MigrationError');
    expect(error.message).toBe('Test error');
    expect(error.details).toEqual({});
  });
});