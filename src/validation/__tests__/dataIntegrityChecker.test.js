/**
 * Tests for DataIntegrityChecker
 */

const { DataIntegrityChecker } = require('../dataIntegrityChecker');
const fs = require('fs').promises;
const path = require('path');

// Mock the logger to avoid console output during tests
jest.mock('../../utils/logger', () => ({
  defaultLogger: {
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
  }
}));

describe('DataIntegrityChecker', () => {
  let integrityChecker;

  beforeEach(() => {
    integrityChecker = new DataIntegrityChecker();
  });

  describe('validateTestFactsFileIntegrity', () => {
    it('should validate a valid test facts file', async () => {
      const validTestFacts = [
        {
          id: 1,
          fact: "This is a test fact",
          question: "What is this?",
          answer: "A test fact",
          complexity: "simple"
        },
        {
          id: 2,
          fact: "This is a more complex test fact with additional details and information",
          question: "What kind of fact is this?",
          answer: "A complex test fact",
          complexity: "complex"
        }
      ];

      // Create a temporary test file
      const tempFilePath = path.join(__dirname, 'temp_test_facts.json');
      await fs.writeFile(tempFilePath, JSON.stringify(validTestFacts, null, 2));

      try {
        const result = await integrityChecker.validateTestFactsFileIntegrity(tempFilePath);

        expect(result.isValid).toBe(true);
        expect(result.issues).toHaveLength(0);
        expect(result.statistics.factCount).toBe(2);
        expect(result.statistics.complexityDistribution.simple).toBeDefined();
        expect(result.statistics.complexityDistribution.complex).toBeDefined();
      } finally {
        // Clean up
        await fs.unlink(tempFilePath).catch(() => {});
      }
    });

    it('should detect duplicate IDs', async () => {
      const invalidTestFacts = [
        {
          id: 1,
          fact: "First fact",
          question: "What is this?",
          answer: "First",
          complexity: "simple"
        },
        {
          id: 1, // Duplicate ID
          fact: "Second fact",
          question: "What is this?",
          answer: "Second",
          complexity: "simple"
        }
      ];

      const tempFilePath = path.join(__dirname, 'temp_duplicate_ids.json');
      await fs.writeFile(tempFilePath, JSON.stringify(invalidTestFacts, null, 2));

      try {
        const result = await integrityChecker.validateTestFactsFileIntegrity(tempFilePath);

        expect(result.isValid).toBe(false);
        expect(result.issues.some(issue => issue.includes('Duplicate ID'))).toBe(true);
      } finally {
        await fs.unlink(tempFilePath).catch(() => {});
      }
    });

    it('should detect missing required fields', async () => {
      const invalidTestFacts = [
        {
          id: 1,
          fact: "Test fact",
          // Missing question, answer, complexity
        }
      ];

      const tempFilePath = path.join(__dirname, 'temp_missing_fields.json');
      await fs.writeFile(tempFilePath, JSON.stringify(invalidTestFacts, null, 2));

      try {
        const result = await integrityChecker.validateTestFactsFileIntegrity(tempFilePath);

        expect(result.isValid).toBe(false);
        expect(result.issues.some(issue => issue.includes('Missing required fields'))).toBe(true);
      } finally {
        await fs.unlink(tempFilePath).catch(() => {});
      }
    });

    it('should handle invalid JSON files', async () => {
      const tempFilePath = path.join(__dirname, 'temp_invalid_json.json');
      await fs.writeFile(tempFilePath, '{ invalid json }');

      try {
        const result = await integrityChecker.validateTestFactsFileIntegrity(tempFilePath);

        expect(result.isValid).toBe(false);
        expect(result.issues.some(issue => issue.includes('Invalid JSON format'))).toBe(true);
      } finally {
        await fs.unlink(tempFilePath).catch(() => {});
      }
    });

    it('should handle non-existent files', async () => {
      const result = await integrityChecker.validateTestFactsFileIntegrity('/non/existent/file.json');

      expect(result.isValid).toBe(false);
      expect(result.issues.some(issue => issue.includes('File access error'))).toBe(true);
    });
  });

  describe('validateConversationLogIntegrity', () => {
    it('should validate a valid conversation log', async () => {
      const validConversationLog = [
        {
          role: 'user',
          content: 'Hello, how are you?',
          timestamp: Date.now()
        },
        {
          role: 'assistant',
          content: 'I am doing well, thank you for asking!',
          timestamp: Date.now() + 1000
        },
        {
          role: 'user',
          content: 'That is great to hear.',
          timestamp: Date.now() + 2000
        }
      ];

      const result = await integrityChecker.validateConversationLogIntegrity(validConversationLog);

      expect(result.isValid).toBe(true);
      expect(result.issues).toHaveLength(0);
      expect(result.statistics.messageCount).toBe(3);
      expect(result.statistics.roleDistribution.user).toBeDefined();
      expect(result.statistics.roleDistribution.assistant).toBeDefined();
    });

    it('should detect consecutive messages from same role', async () => {
      const invalidConversationLog = [
        {
          role: 'user',
          content: 'First message',
          timestamp: Date.now()
        },
        {
          role: 'user',
          content: 'Second message',
          timestamp: Date.now() + 1000
        },
        {
          role: 'user',
          content: 'Third message',
          timestamp: Date.now() + 2000
        },
        {
          role: 'user',
          content: 'Fourth message', // Too many consecutive user messages
          timestamp: Date.now() + 3000
        }
      ];

      const result = await integrityChecker.validateConversationLogIntegrity(invalidConversationLog);

      expect(result.warnings.some(warning => warning.includes('consecutive user messages'))).toBe(true);
    });

    it('should detect timestamp ordering issues', async () => {
      const invalidConversationLog = [
        {
          role: 'user',
          content: 'First message',
          timestamp: Date.now() + 2000 // Later timestamp
        },
        {
          role: 'assistant',
          content: 'Second message',
          timestamp: Date.now() // Earlier timestamp
        }
      ];

      const result = await integrityChecker.validateConversationLogIntegrity(invalidConversationLog);

      expect(result.warnings.some(warning => warning.includes('timestamp before previous message'))).toBe(true);
    });

    it('should handle empty conversation logs', async () => {
      const result = await integrityChecker.validateConversationLogIntegrity([]);

      expect(result.warnings.some(warning => warning.includes('Conversation log is empty'))).toBe(true);
    });
  });

  describe('validateResultOutputFormat', () => {
    it('should validate a valid result output', () => {
      const validResultData = {
        sessionId: '12345678-1234-1234-1234-123456789012',
        memoryType: 'simple',
        overallScore: 85.5,
        evaluationResults: [
          {
            factId: 1,
            fact: 'Test fact',
            question: 'Test question?',
            score: 8.5
          }
        ],
        testFactsFile: 'simple',
        testFactsCount: 1,
        conversationLog: [
          {
            role: 'user',
            content: 'Hello'
          }
        ],
        performanceMetrics: {
          session: { totalDuration: 1000 },
          operations: { total: 5 },
          api: { totalCalls: 3 }
        }
      };

      const result = integrityChecker.validateResultOutputFormat(validResultData, 'test_results.md');

      expect(result.isValid).toBe(true);
      expect(result.issues).toHaveLength(0);
      expect(result.statistics.requiredFields).toBe(4);
      expect(result.statistics.optionalFields).toBeGreaterThan(0);
    });

    it('should detect missing required fields', () => {
      const invalidResultData = {
        sessionId: '12345678-1234-1234-1234-123456789012',
        // Missing memoryType, overallScore, evaluationResults
      };

      const result = integrityChecker.validateResultOutputFormat(invalidResultData, 'test_results.md');

      expect(result.isValid).toBe(false);
      expect(result.issues.some(issue => issue.includes('Missing required fields'))).toBe(true);
    });

    it('should validate score ranges', () => {
      const invalidResultData = {
        sessionId: '12345678-1234-1234-1234-123456789012',
        memoryType: 'simple',
        overallScore: 150, // Invalid score > 100
        evaluationResults: [
          {
            factId: 1,
            fact: 'Test fact',
            question: 'Test question?',
            score: 15 // Invalid score > 10
          }
        ]
      };

      const result = integrityChecker.validateResultOutputFormat(invalidResultData, 'test_results.md');

      expect(result.warnings.some(warning => warning.includes('outside expected range'))).toBe(true);
    });

    it('should validate UUID format', () => {
      const invalidResultData = {
        sessionId: 'invalid-uuid',
        memoryType: 'simple',
        overallScore: 85,
        evaluationResults: []
      };

      const result = integrityChecker.validateResultOutputFormat(invalidResultData, 'test_results.md');

      expect(result.warnings.some(warning => warning.includes('valid UUID'))).toBe(true);
    });

    it('should validate memory type', () => {
      const invalidResultData = {
        sessionId: '12345678-1234-1234-1234-123456789012',
        memoryType: 'invalid_type',
        overallScore: 85,
        evaluationResults: []
      };

      const result = integrityChecker.validateResultOutputFormat(invalidResultData, 'test_results.md');

      expect(result.warnings.some(warning => warning.includes('not a recognized type'))).toBe(true);
    });

    it('should handle null or undefined data', () => {
      const result = integrityChecker.validateResultOutputFormat(null, 'test_results.md');

      expect(result.isValid).toBe(false);
      expect(result.issues.some(issue => issue.includes('missing or not an object'))).toBe(true);
    });
  });

  describe('checkDuplicateIds', () => {
    it('should detect no duplicates in valid data', () => {
      const validData = [
        { id: 1, fact: 'First' },
        { id: 2, fact: 'Second' },
        { id: 3, fact: 'Third' }
      ];

      const result = integrityChecker.checkDuplicateIds(validData);

      expect(result.isValid).toBe(true);
      expect(result.duplicates).toHaveLength(0);
    });

    it('should detect duplicate IDs', () => {
      const invalidData = [
        { id: 1, fact: 'First' },
        { id: 2, fact: 'Second' },
        { id: 1, fact: 'Duplicate' }
      ];

      const result = integrityChecker.checkDuplicateIds(invalidData);

      expect(result.isValid).toBe(false);
      expect(result.duplicates).toHaveLength(1);
      expect(result.duplicates[0].id).toBe(1);
      expect(result.duplicates[0].count).toBe(2);
    });
  });

  describe('checkComplexityDistribution', () => {
    it('should validate balanced complexity distribution', () => {
      const balancedData = [
        { complexity: 'simple' },
        { complexity: 'simple' },
        { complexity: 'complex' },
        { complexity: 'complex' }
      ];

      const rules = {
        minComplexityDistribution: 0.2,
        maxComplexityDistribution: 0.8
      };

      const result = integrityChecker.checkComplexityDistribution(balancedData, rules);

      expect(result.isValid).toBe(true);
      expect(result.distribution.simple.ratio).toBe(50);
      expect(result.distribution.complex.ratio).toBe(50);
    });

    it('should detect unbalanced complexity distribution', () => {
      const unbalancedData = [
        { complexity: 'simple' },
        { complexity: 'complex' },
        { complexity: 'complex' },
        { complexity: 'complex' },
        { complexity: 'complex' }
      ];

      const rules = {
        minComplexityDistribution: 0.2,
        maxComplexityDistribution: 0.8
      };

      const result = integrityChecker.checkComplexityDistribution(unbalancedData, rules);

      expect(result.isValid).toBe(false);
      expect(result.warnings.some(warning => warning.includes('Low simple fact ratio'))).toBe(true);
    });
  });

  describe('calculateTextSimilarity', () => {
    it('should calculate similarity between identical texts', () => {
      const text1 = 'This is a test';
      const text2 = 'This is a test';

      const similarity = integrityChecker.calculateTextSimilarity(text1, text2);

      expect(similarity).toBe(1);
    });

    it('should calculate similarity between different texts', () => {
      const text1 = 'This is a test';
      const text2 = 'This is completely different';

      const similarity = integrityChecker.calculateTextSimilarity(text1, text2);

      expect(similarity).toBeGreaterThan(0);
      expect(similarity).toBeLessThan(1);
    });

    it('should handle empty texts', () => {
      const similarity1 = integrityChecker.calculateTextSimilarity('', 'test');
      const similarity2 = integrityChecker.calculateTextSimilarity('test', '');
      const similarity3 = integrityChecker.calculateTextSimilarity('', '');

      expect(similarity1).toBe(0);
      expect(similarity2).toBe(0);
      expect(similarity3).toBe(0);
    });
  });
});