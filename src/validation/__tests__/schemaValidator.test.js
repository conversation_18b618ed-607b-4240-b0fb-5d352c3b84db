/**
 * Schema Validator Test Suite
 *
 * Comprehensive tests for the SchemaValidator class covering all validation
 * scenarios, error handling, and business logic warnings. Tests ensure that
 * the validator correctly identifies valid and invalid data structures and
 * provides helpful error messages and guidance.
 *
 * @fileoverview Tests for SchemaValidator class
 */

const SchemaValidator = require('../schemaValidator');

describe('SchemaValidator', () => {
  let validator;

  beforeEach(() => {
    validator = new SchemaValidator();
  });

  describe('Constructor and Initialization', () => {
    test('should initialize with compiled schemas', () => {
      expect(validator).toBeDefined();
      expect(validator.compiledSchemas).toBeDefined();
      expect(validator.compiledSchemas.testFacts).toBeDefined();
      expect(validator.compiledSchemas.llmResponse).toBeDefined();
      expect(validator.compiledSchemas.conversationLog).toBeDefined();
      expect(validator.compiledSchemas.testResults).toBeDefined();
    });

    test('should have custom formats registered', () => {
      const info = validator.getValidatorInfo();
      expect(info.customFormats).toContain('uuid');
      expect(info.customFormats).toContain('model-name');
      expect(info.customFormats).toContain('complexity-level');
      expect(info.customFormats).toContain('score');
      expect(info.customFormats).toContain('percentage');
      expect(info.customFormats).toContain('timestamp');
    });
  });

  describe('Test Facts Validation', () => {
    const validTestFacts = [
      {
        id: 1,
        fact: 'My name is John Smith.',
        question: 'What is my name?',
        answer: 'John Smith',
        complexity: 'simple'
      },
      {
        id: 2,
        fact: 'I have a master\'s degree in Computer Science from MIT.',
        question: 'What is my educational background?',
        answer: 'Master\'s degree in Computer Science from MIT',
        complexity: 'complex'
      }
    ];

    test('should validate correct test facts', () => {
      const result = validator.validateTestFacts(validTestFacts);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.metadata.itemCount).toBe(2);
      expect(result.metadata.validationTime).toBeGreaterThan(0);
    });

    test('should reject empty array', () => {
      const result = validator.validateTestFacts([]);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors[0]).toContain('minItems');
    });

    test('should reject facts with missing required fields', () => {
      const invalidFacts = [
        {
          id: 1,
          fact: 'My name is John.',
          // missing question, answer, complexity
        }
      ];

      const result = validator.validateTestFacts(invalidFacts);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors.some(error => error.includes('required'))).toBe(true);
    });

    test('should reject facts with invalid complexity', () => {
      const invalidFacts = [
        {
          id: 1,
          fact: 'My name is John.',
          question: 'What is my name?',
          answer: 'John',
          complexity: 'invalid'
        }
      ];

      const result = validator.validateTestFacts(invalidFacts);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('enum'))).toBe(true);
    });

    test('should reject facts with invalid ID types', () => {
      const invalidFacts = [
        {
          id: 'not-a-number',
          fact: 'My name is John.',
          question: 'What is my name?',
          answer: 'John',
          complexity: 'simple'
        }
      ];

      const result = validator.validateTestFacts(invalidFacts);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('type'))).toBe(true);
    });

    test('should warn about unbalanced complexity distribution', () => {
      const unbalancedFacts = [
        ...Array(10).fill(null).map((_, i) => ({
          id: i + 1,
          fact: `Simple fact ${i + 1}`,
          question: `Question ${i + 1}?`,
          answer: `Answer ${i + 1}`,
          complexity: 'simple'
        }))
      ];

      const result = validator.validateTestFacts(unbalancedFacts);
      
      expect(result.isValid).toBe(true);
      expect(result.warnings.some(warning => 
        warning.includes('Unbalanced complexity distribution')
      )).toBe(true);
    });

    test('should warn about very short facts', () => {
      const shortFacts = [
        {
          id: 1,
          fact: 'Short',
          question: 'Is this short?',
          answer: 'Yes',
          complexity: 'simple'
        }
      ];

      const result = validator.validateTestFacts(shortFacts);
      
      expect(result.isValid).toBe(true);
      expect(result.warnings.some(warning => 
        warning.includes('very short')
      )).toBe(true);
    });

    test('should warn about very long facts', () => {
      const longFact = 'A'.repeat(600);
      const longFacts = [
        {
          id: 1,
          fact: longFact,
          question: 'Is this long?',
          answer: 'Yes',
          complexity: 'complex'
        }
      ];

      const result = validator.validateTestFacts(longFacts);
      
      expect(result.isValid).toBe(true);
      expect(result.warnings.some(warning => 
        warning.includes('very long')
      )).toBe(true);
    });

    test('should handle optional fields correctly', () => {
      const factsWithOptionalFields = [
        {
          id: 1,
          fact: 'My name is John.',
          question: 'What is my name?',
          answer: 'John',
          complexity: 'simple',
          category: 'personal',
          tags: ['name', 'identity'],
          metadata: {
            source: 'user_profile',
            difficulty: 5
          }
        }
      ];

      const result = validator.validateTestFacts(factsWithOptionalFields);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
  });

  describe('LLM Response Validation', () => {
    const validLLMResponse = {
      content: 'Hello! How can I help you today?',
      metadata: {
        model: 'gpt-3.5-turbo',
        timestamp: Date.now(),
        requestId: '550e8400-e29b-41d4-a716-************',
        provider: 'openai',
        responseTime: 1250,
        tokenUsage: {
          promptTokens: 15,
          completionTokens: 12,
          totalTokens: 27
        }
      },
      status: 'success',
      finishReason: 'stop'
    };

    test('should validate correct LLM response', () => {
      const result = validator.validateLLMResponse(validLLMResponse);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.metadata.validationTime).toBeGreaterThan(0);
    });

    test('should reject response without required fields', () => {
      const invalidResponse = {
        content: 'Hello!'
        // missing metadata
      };

      const result = validator.validateLLMResponse(invalidResponse);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('required'))).toBe(true);
    });

    test('should reject response with invalid model name format', () => {
      const invalidResponse = {
        content: 'Hello!',
        metadata: {
          model: '', // empty model name
          timestamp: Date.now()
        }
      };

      const result = validator.validateLLMResponse(invalidResponse);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('model'))).toBe(true);
    });

    test('should reject response with invalid timestamp', () => {
      const invalidResponse = {
        content: 'Hello!',
        metadata: {
          model: 'gpt-3.5-turbo',
          timestamp: 'not-a-timestamp'
        }
      };

      const result = validator.validateLLMResponse(invalidResponse);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('timestamp'))).toBe(true);
    });

    test('should warn about very short responses', () => {
      const shortResponse = {
        content: 'Hi',
        metadata: {
          model: 'gpt-3.5-turbo',
          timestamp: Date.now()
        }
      };

      const result = validator.validateLLMResponse(shortResponse);
      
      expect(result.isValid).toBe(true);
      expect(result.warnings.some(warning => 
        warning.includes('very short')
      )).toBe(true);
    });

    test('should warn about very long responses', () => {
      const longContent = 'A'.repeat(2500);
      const longResponse = {
        content: longContent,
        metadata: {
          model: 'gpt-3.5-turbo',
          timestamp: Date.now()
        }
      };

      const result = validator.validateLLMResponse(longResponse);
      
      expect(result.isValid).toBe(true);
      expect(result.warnings.some(warning => 
        warning.includes('very long')
      )).toBe(true);
    });

    test('should validate error responses correctly', () => {
      const errorResponse = {
        content: '',
        metadata: {
          model: 'gpt-3.5-turbo',
          timestamp: Date.now()
        },
        error: {
          code: 'rate_limit_exceeded',
          message: 'Rate limit exceeded',
          type: 'rate_limit_error',
          retryable: true,
          retryAfter: 60
        },
        status: 'rate_limited'
      };

      const result = validator.validateLLMResponse(errorResponse);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
  });

  describe('Conversation Log Validation', () => {
    const validConversationLog = [
      {
        role: 'user',
        content: 'Hello, my name is John.',
        timestamp: Date.now(),
        messageId: '550e8400-e29b-41d4-a716-446655440001',
        cycle: 1
      },
      {
        role: 'assistant',
        content: 'Hello John! Nice to meet you.',
        timestamp: Date.now() + 1000,
        messageId: '550e8400-e29b-41d4-a716-446655440002',
        cycle: 2
      }
    ];

    test('should validate correct conversation log', () => {
      const result = validator.validateConversationLog(validConversationLog);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.metadata.messageCount).toBe(2);
    });

    test('should reject empty conversation log', () => {
      const result = validator.validateConversationLog([]);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('minItems'))).toBe(true);
    });

    test('should reject messages with invalid roles', () => {
      const invalidLog = [
        {
          role: 'invalid_role',
          content: 'Hello',
          timestamp: Date.now()
        }
      ];

      const result = validator.validateConversationLog(invalidLog);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('enum'))).toBe(true);
    });

    test('should reject messages without required fields', () => {
      const invalidLog = [
        {
          role: 'user',
          content: 'Hello'
          // missing timestamp
        }
      ];

      const result = validator.validateConversationLog(invalidLog);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('required'))).toBe(true);
    });

    test('should warn about very short messages', () => {
      const shortMessageLog = [
        {
          role: 'user',
          content: 'Hi',
          timestamp: Date.now()
        }
      ];

      const result = validator.validateConversationLog(shortMessageLog);
      
      expect(result.isValid).toBe(true);
      expect(result.warnings.some(warning => 
        warning.includes('very short')
      )).toBe(true);
    });

    test('should handle optional metadata fields', () => {
      const logWithMetadata = [
        {
          role: 'user',
          content: 'Hello',
          timestamp: Date.now(),
          metadata: {
            isFactInjection: true,
            factId: 1,
            tokenCount: 5
          }
        }
      ];

      const result = validator.validateConversationLog(logWithMetadata);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
  });

  describe('Test Results Validation', () => {
    const validTestResults = {
      metadata: {
        sessionId: '550e8400-e29b-41d4-a716-************',
        timestamp: Date.now(),
        memoryType: 'summary',
        testScenario: 'customer',
        testFactsCount: 2,
        messagesBetweenFacts: 3
      },
      scores: {
        overall: 78.5,
        simple: 85.0,
        complex: 72.0
      },
      evaluationResults: [
        {
          factId: 1,
          fact: 'My name is John.',
          question: 'What is my name?',
          expectedAnswer: 'John',
          actualResponse: 'Your name is John.',
          score: 9.5,
          complexity: 'simple'
        }
      ]
    };

    test('should validate correct test results', () => {
      const result = validator.validateTestResults(validTestResults);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('should reject results without required fields', () => {
      const invalidResults = {
        metadata: {
          sessionId: '550e8400-e29b-41d4-a716-************',
          timestamp: Date.now(),
          memoryType: 'summary',
          testScenario: 'customer'
        }
        // missing scores and evaluationResults
      };

      const result = validator.validateTestResults(invalidResults);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('required'))).toBe(true);
    });

    test('should reject invalid session ID format', () => {
      const invalidResults = {
        ...validTestResults,
        metadata: {
          ...validTestResults.metadata,
          sessionId: 'invalid-uuid'
        }
      };

      const result = validator.validateTestResults(invalidResults);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('uuid'))).toBe(true);
    });

    test('should reject invalid memory type', () => {
      const invalidResults = {
        ...validTestResults,
        metadata: {
          ...validTestResults.metadata,
          memoryType: 'invalid_type'
        }
      };

      const result = validator.validateTestResults(invalidResults);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('enum'))).toBe(true);
    });

    test('should reject scores outside valid range', () => {
      const invalidResults = {
        ...validTestResults,
        scores: {
          overall: 150, // invalid: > 100
          simple: -10,  // invalid: < 0
          complex: 50
        }
      };

      const result = validator.validateTestResults(invalidResults);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => 
        error.includes('maximum') || error.includes('minimum')
      )).toBe(true);
    });

    test('should warn about suspicious scores', () => {
      const suspiciousResults = {
        ...validTestResults,
        scores: {
          overall: 0, // suspicious: 0%
          simple: 0,
          complex: 0
        }
      };

      const result = validator.validateTestResults(suspiciousResults);
      
      expect(result.isValid).toBe(true);
      expect(result.warnings.some(warning => 
        warning.includes('Overall score is 0%')
      )).toBe(true);
    });

    test('should warn about large score discrepancies', () => {
      const discrepantResults = {
        ...validTestResults,
        scores: {
          overall: 50,
          simple: 90,  // large difference
          complex: 10  // with complex
        }
      };

      const result = validator.validateTestResults(discrepantResults);
      
      expect(result.isValid).toBe(true);
      expect(result.warnings.some(warning => 
        warning.includes('Large score difference')
      )).toBe(true);
    });
  });

  describe('Multiple Data Validation', () => {
    test('should validate multiple data objects', () => {
      const testData = {
        testFacts: [
          {
            id: 1,
            fact: 'Test fact',
            question: 'Test question?',
            answer: 'Test answer',
            complexity: 'simple'
          }
        ],
        llmResponse: {
          content: 'Test response',
          metadata: {
            model: 'test-model',
            timestamp: Date.now()
          }
        }
      };

      const result = validator.validateMultiple(testData);
      
      expect(result.allValid).toBe(true);
      expect(result.summary.totalValidations).toBe(2);
      expect(result.summary.successfulValidations).toBe(2);
      expect(result.summary.failedValidations).toBe(0);
    });

    test('should handle mixed valid and invalid data', () => {
      const testData = {
        testFacts: [], // invalid: empty array
        llmResponse: {
          content: 'Test response',
          metadata: {
            model: 'test-model',
            timestamp: Date.now()
          }
        } // valid
      };

      const result = validator.validateMultiple(testData);
      
      expect(result.allValid).toBe(false);
      expect(result.summary.totalValidations).toBe(2);
      expect(result.summary.successfulValidations).toBe(1);
      expect(result.summary.failedValidations).toBe(1);
    });
  });

  describe('Custom Format Validation', () => {
    test('should validate UUID format', () => {
      const validUUID = '550e8400-e29b-41d4-a716-************';
      const invalidUUID = 'not-a-uuid';

      // Test through LLM response validation which uses UUID format
      const validResponse = {
        content: 'Test',
        metadata: {
          model: 'test-model',
          timestamp: Date.now(),
          requestId: validUUID
        }
      };

      const invalidResponse = {
        content: 'Test',
        metadata: {
          model: 'test-model',
          timestamp: Date.now(),
          requestId: invalidUUID
        }
      };

      expect(validator.validateLLMResponse(validResponse).isValid).toBe(true);
      expect(validator.validateLLMResponse(invalidResponse).isValid).toBe(false);
    });

    test('should validate model name format', () => {
      const validModelNames = [
        'gpt-3.5-turbo',
        'claude-3-sonnet',
        'microsoft/phi-4:free',
        'local_model_v1.0'
      ];

      const invalidModelNames = [
        '', // empty
        'model with spaces and special chars!@#'
      ];

      for (const modelName of validModelNames) {
        const response = {
          content: 'Test',
          metadata: {
            model: modelName,
            timestamp: Date.now()
          }
        };
        expect(validator.validateLLMResponse(response).isValid).toBe(true);
      }

      for (const modelName of invalidModelNames) {
        const response = {
          content: 'Test',
          metadata: {
            model: modelName,
            timestamp: Date.now()
          }
        };
        expect(validator.validateLLMResponse(response).isValid).toBe(false);
      }
    });

    test('should validate score format', () => {
      const validScores = [0, 5.5, 10];
      const invalidScores = [-1, 11, NaN, 'not-a-number'];

      for (const score of validScores) {
        const results = {
          metadata: {
            sessionId: '550e8400-e29b-41d4-a716-************',
            timestamp: Date.now(),
            memoryType: 'simple',
            testScenario: 'test'
          },
          scores: { overall: 50 },
          evaluationResults: [{
            factId: 1,
            fact: 'Test',
            question: 'Test?',
            expectedAnswer: 'Test',
            actualResponse: 'Test',
            score: score,
            complexity: 'simple'
          }]
        };
        expect(validator.validateTestResults(results).isValid).toBe(true);
      }

      for (const score of invalidScores) {
        const results = {
          metadata: {
            sessionId: '550e8400-e29b-41d4-a716-************',
            timestamp: Date.now(),
            memoryType: 'simple',
            testScenario: 'test'
          },
          scores: { overall: 50 },
          evaluationResults: [{
            factId: 1,
            fact: 'Test',
            question: 'Test?',
            expectedAnswer: 'Test',
            actualResponse: 'Test',
            score: score,
            complexity: 'simple'
          }]
        };
        expect(validator.validateTestResults(results).isValid).toBe(false);
      }
    });
  });

  describe('Error Message Formatting', () => {
    test('should provide helpful error messages', () => {
      const invalidFacts = [
        {
          id: 'not-a-number',
          fact: '',
          question: 'What?',
          answer: 'Answer',
          complexity: 'invalid'
        }
      ];

      const result = validator.validateTestFacts(invalidFacts);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      
      // Check that errors contain helpful guidance
      const hasGuidance = result.errors.some(error => 
        error.includes('Guidance:')
      );
      expect(hasGuidance).toBe(true);
    });

    test('should provide schema-specific guidance', () => {
      const invalidResponse = {
        content: 'Test'
        // missing required metadata
      };

      const result = validator.validateLLMResponse(invalidResponse);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => 
        error.includes('required') && error.includes('Guidance:')
      )).toBe(true);
    });
  });

  describe('Validator Information', () => {
    test('should provide validator information', () => {
      const info = validator.getValidatorInfo();
      
      expect(info).toHaveProperty('schemas');
      expect(info).toHaveProperty('customFormats');
      expect(info).toHaveProperty('ajvVersion');
      
      expect(info.schemas).toHaveProperty('testFacts');
      expect(info.schemas).toHaveProperty('llmResponse');
      expect(info.schemas).toHaveProperty('conversationLog');
      expect(info.schemas).toHaveProperty('testResults');
      
      expect(Array.isArray(info.customFormats)).toBe(true);
      expect(info.customFormats.length).toBeGreaterThan(0);
    });
  });
});