/**
 * Schema Validator - Comprehensive Data Validation System
 *
 * This module provides comprehensive JSON schema validation for all data structures
 * used in the LLM Memory Test Application. It validates test fact files, configuration
 * objects, LLM responses, conversation logs, and result output to ensure data integrity
 * and compliance with expected formats.
 *
 * Key Features:
 * - JSON Schema validation using AJV with format support
 * - Custom validation rules for application-specific requirements
 * - Detailed error reporting with specific guidance
 * - Schema compliance checking for all data types
 * - Extensible schema definitions for future data formats
 * - Performance-optimized validation with compiled schemas
 *
 * Supported Data Types:
 * - Test fact files (JSON arrays with fact objects)
 * - Configuration objects (environment and runtime config)
 * - LLM responses (API responses and generated content)
 * - Conversation logs (message sequences and metadata)
 * - Test results (evaluation results and performance metrics)
 *
 * @fileoverview Comprehensive data validation using JSON Schema
 * @module schemaValidator
 * 
 * @example
 * // Validate test facts file
 * const validator = new SchemaValidator();
 * const result = await validator.validateTestFacts(testFactsData);
 * if (!result.isValid) {
 *   console.error('Validation errors:', result.errors);
 * }
 * 
 * @example
 * // Validate LLM response
 * const result = validator.validateLLMResponse(responseData);
 * console.log('Response is valid:', result.isValid);
 */

const Ajv = require('ajv');
const addFormats = require('ajv-formats');
const { defaultLogger } = require('../utils/logger');

// Import schema definitions
const testFactsSchema = require('./schemas/testFactsSchema');
const llmResponseSchema = require('./schemas/llmResponseSchema');
const conversationLogSchema = require('./schemas/conversationLogSchema');
const testResultsSchema = require('./schemas/testResultsSchema');

/**
 * SchemaValidator class provides comprehensive data validation capabilities
 * using JSON Schema with AJV validator and custom validation rules.
 */
class SchemaValidator {
  /**
   * Initialize the schema validator with AJV instance and compiled schemas
   */
  constructor() {
    // Initialize AJV with all errors and format support
    this.ajv = new Ajv({ 
      allErrors: true,
      verbose: true,
      strict: false, // Allow additional properties for extensibility
      removeAdditional: false // Keep additional properties
    });
    
    // Add format support for common formats
    addFormats(this.ajv);
    
    // Add custom formats for application-specific validation
    this.addCustomFormats();
    
    // Compile schemas for performance
    this.compiledSchemas = {
      testFacts: this.ajv.compile(testFactsSchema),
      llmResponse: this.ajv.compile(llmResponseSchema),
      conversationLog: this.ajv.compile(conversationLogSchema),
      testResults: this.ajv.compile(testResultsSchema)
    };
    
    defaultLogger.debug('SchemaValidator initialized with compiled schemas', {
      schemas: Object.keys(this.compiledSchemas)
    });
  }

  /**
   * Add custom format validators for application-specific data types
   * @private
   */
  addCustomFormats() {
    // UUID format validation
    this.ajv.addFormat('uuid', {
      type: 'string',
      validate: (data) => {
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        return uuidRegex.test(data);
      }
    });

    // Model name format validation (allows various model naming conventions)
    this.ajv.addFormat('model-name', {
      type: 'string',
      validate: (data) => {
        // Allow alphanumeric, hyphens, underscores, slashes, colons, and dots
        const modelNameRegex = /^[a-zA-Z0-9\-_\/:.]+$/;
        return modelNameRegex.test(data) && data.length >= 1 && data.length <= 200;
      }
    });

    // Complexity level validation
    this.ajv.addFormat('complexity-level', {
      type: 'string',
      validate: (data) => {
        return ['simple', 'complex'].includes(data);
      }
    });

    // Score validation (0-10 range)
    this.ajv.addFormat('score', {
      type: 'number',
      validate: (data) => {
        return typeof data === 'number' && data >= 0 && data <= 10 && !isNaN(data);
      }
    });

    // Percentage validation (0-100 range)
    this.ajv.addFormat('percentage', {
      type: 'number',
      validate: (data) => {
        return typeof data === 'number' && data >= 0 && data <= 100 && !isNaN(data);
      }
    });

    // Timestamp validation (Unix timestamp in milliseconds)
    this.ajv.addFormat('timestamp', {
      type: 'number',
      validate: (data) => {
        return typeof data === 'number' && data > 0 && data <= Date.now() + 86400000; // Allow up to 1 day in future
      }
    });
  }

  /**
   * Validate test facts data against the test facts schema
   * 
   * @param {Array|Object} data - Test facts data to validate
   * @returns {Object} Validation result with isValid flag and detailed errors
   * @returns {boolean} returns.isValid - Whether the data is valid
   * @returns {Array<string>} returns.errors - Array of validation error messages
   * @returns {Array<string>} returns.warnings - Array of validation warnings
   * @returns {Object} returns.metadata - Validation metadata and statistics
   * 
   * @example
   * const result = validator.validateTestFacts(testFactsArray);
   * if (!result.isValid) {
   *   console.error('Test facts validation failed:', result.errors);
   * }
   */
  validateTestFacts(data) {
    const startTime = Date.now();
    
    defaultLogger.debug('Validating test facts data', {
      dataType: Array.isArray(data) ? 'array' : typeof data,
      itemCount: Array.isArray(data) ? data.length : 'N/A'
    });

    const isValid = this.compiledSchemas.testFacts(data);
    const result = {
      isValid,
      errors: [],
      warnings: [],
      metadata: {
        validationTime: Date.now() - startTime,
        itemCount: Array.isArray(data) ? data.length : 0,
        schemaVersion: testFactsSchema.$id || 'unknown'
      }
    };

    if (!isValid) {
      result.errors = this.formatValidationErrors(this.compiledSchemas.testFacts.errors, 'testFacts');
      
      defaultLogger.warn('Test facts validation failed', {
        errorCount: result.errors.length,
        errors: result.errors
      });
    } else {
      // Perform additional business logic validation
      this.addTestFactsWarnings(data, result);
      
      defaultLogger.debug('Test facts validation successful', {
        itemCount: result.metadata.itemCount,
        validationTime: result.metadata.validationTime
      });
    }

    return result;
  }

  /**
   * Validate LLM response data against the LLM response schema
   * 
   * @param {Object} data - LLM response data to validate
   * @returns {Object} Validation result with isValid flag and detailed errors
   * 
   * @example
   * const result = validator.validateLLMResponse(llmResponseData);
   * console.log('LLM response valid:', result.isValid);
   */
  validateLLMResponse(data) {
    const startTime = Date.now();
    
    defaultLogger.debug('Validating LLM response data', {
      hasContent: !!data?.content,
      hasMetadata: !!data?.metadata
    });

    const isValid = this.compiledSchemas.llmResponse(data);
    const result = {
      isValid,
      errors: [],
      warnings: [],
      metadata: {
        validationTime: Date.now() - startTime,
        schemaVersion: llmResponseSchema.$id || 'unknown'
      }
    };

    if (!isValid) {
      result.errors = this.formatValidationErrors(this.compiledSchemas.llmResponse.errors, 'llmResponse');
      
      defaultLogger.warn('LLM response validation failed', {
        errorCount: result.errors.length,
        errors: result.errors
      });
    } else {
      // Add warnings for potential issues
      this.addLLMResponseWarnings(data, result);
      
      defaultLogger.debug('LLM response validation successful', {
        validationTime: result.metadata.validationTime
      });
    }

    return result;
  }

  /**
   * Validate conversation log data against the conversation log schema
   * 
   * @param {Array} data - Conversation log data to validate
   * @returns {Object} Validation result with isValid flag and detailed errors
   * 
   * @example
   * const result = validator.validateConversationLog(conversationData);
   * if (result.warnings.length > 0) {
   *   console.warn('Conversation log warnings:', result.warnings);
   * }
   */
  validateConversationLog(data) {
    const startTime = Date.now();
    
    defaultLogger.debug('Validating conversation log data', {
      messageCount: Array.isArray(data) ? data.length : 'N/A'
    });

    const isValid = this.compiledSchemas.conversationLog(data);
    const result = {
      isValid,
      errors: [],
      warnings: [],
      metadata: {
        validationTime: Date.now() - startTime,
        messageCount: Array.isArray(data) ? data.length : 0,
        schemaVersion: conversationLogSchema.$id || 'unknown'
      }
    };

    if (!isValid) {
      result.errors = this.formatValidationErrors(this.compiledSchemas.conversationLog.errors, 'conversationLog');
      
      defaultLogger.warn('Conversation log validation failed', {
        errorCount: result.errors.length,
        errors: result.errors
      });
    } else {
      // Add warnings for conversation flow issues
      this.addConversationLogWarnings(data, result);
      
      defaultLogger.debug('Conversation log validation successful', {
        messageCount: result.metadata.messageCount,
        validationTime: result.metadata.validationTime
      });
    }

    return result;
  }

  /**
   * Validate test results data against the test results schema
   * 
   * @param {Object} data - Test results data to validate
   * @returns {Object} Validation result with isValid flag and detailed errors
   * 
   * @example
   * const result = validator.validateTestResults(resultsData);
   * console.log('Test results valid:', result.isValid);
   */
  validateTestResults(data) {
    const startTime = Date.now();
    
    defaultLogger.debug('Validating test results data', {
      hasScores: !!data?.scores,
      hasEvaluationResults: !!data?.evaluationResults,
      hasMetadata: !!data?.metadata
    });

    const isValid = this.compiledSchemas.testResults(data);
    const result = {
      isValid,
      errors: [],
      warnings: [],
      metadata: {
        validationTime: Date.now() - startTime,
        schemaVersion: testResultsSchema.$id || 'unknown'
      }
    };

    if (!isValid) {
      result.errors = this.formatValidationErrors(this.compiledSchemas.testResults.errors, 'testResults');
      
      defaultLogger.warn('Test results validation failed', {
        errorCount: result.errors.length,
        errors: result.errors
      });
    } else {
      // Add warnings for result quality issues
      this.addTestResultsWarnings(data, result);
      
      defaultLogger.debug('Test results validation successful', {
        validationTime: result.metadata.validationTime
      });
    }

    return result;
  }

  /**
   * Format AJV validation errors into readable error messages with guidance
   * 
   * @param {Array} errors - AJV validation errors
   * @param {string} schemaType - Type of schema being validated
   * @returns {Array<string>} Formatted error messages
   * @private
   */
  formatValidationErrors(errors, schemaType) {
    if (!errors || errors.length === 0) {
      return [];
    }

    return errors.map(error => {
      const path = error.instancePath || 'root';
      const message = error.message;
      const schemaPath = error.schemaPath;
      
      // Create base error message
      let errorMessage = `${path}: ${message}`;
      
      // Add allowed values for enum errors
      if (error.params?.allowedValues) {
        errorMessage += ` (allowed values: ${error.params.allowedValues.join(', ')})`;
      }
      
      // Add limit information for numeric constraints
      if (error.params?.limit !== undefined) {
        errorMessage += ` (limit: ${error.params.limit})`;
      }
      
      // Add helpful guidance based on error type and schema
      const guidance = this.getValidationGuidance(error, schemaType);
      if (guidance) {
        errorMessage += `\n    Guidance: ${guidance}`;
      }
      
      return errorMessage;
    });
  }

  /**
   * Get helpful guidance for specific validation errors
   * 
   * @param {Object} error - AJV validation error
   * @param {string} schemaType - Type of schema being validated
   * @returns {string|null} Guidance message or null if no guidance available
   * @private
   */
  getValidationGuidance(error, schemaType) {
    const path = error.instancePath?.replace(/^\//, '').replace(/\//g, '.');
    const keyword = error.keyword;
    
    // Schema-specific guidance
    const guidanceMap = {
      testFacts: {
        'required': 'Each test fact must have id, fact, question, answer, and complexity fields',
        'type': 'Check that numeric fields (like id) are numbers and text fields are strings',
        'enum': 'Complexity must be either "simple" or "complex"',
        'minLength': 'Fact statements and questions should not be empty',
        'minimum': 'ID values should be positive integers'
      },
      llmResponse: {
        'required': 'LLM responses must include content and metadata fields',
        'type': 'Response content should be a string',
        'format': 'Check timestamp and model name formats'
      },
      conversationLog: {
        'required': 'Each message must have role, content, and timestamp fields',
        'enum': 'Message role must be either "user" or "assistant"',
        'minItems': 'Conversation log should contain at least one message'
      },
      testResults: {
        'required': 'Test results must include scores, evaluationResults, and metadata',
        'format': 'Check that scores are valid percentages (0-100)',
        'minimum': 'Scores should not be negative',
        'maximum': 'Scores should not exceed 100%'
      }
    };
    
    // Get schema-specific guidance
    const schemaGuidance = guidanceMap[schemaType];
    if (schemaGuidance && schemaGuidance[keyword]) {
      return schemaGuidance[keyword];
    }
    
    // General guidance based on error keyword
    const generalGuidance = {
      'required': 'This field is required and cannot be missing',
      'type': 'The data type does not match the expected type',
      'format': 'The data format is invalid',
      'enum': 'The value must be one of the allowed options',
      'minimum': 'The value is below the minimum allowed',
      'maximum': 'The value exceeds the maximum allowed',
      'minLength': 'The text is too short',
      'maxLength': 'The text is too long',
      'minItems': 'The array needs more items',
      'maxItems': 'The array has too many items'
    };
    
    return generalGuidance[keyword] || null;
  }

  /**
   * Add business logic warnings for test facts data
   * 
   * @param {Array} data - Test facts data
   * @param {Object} result - Validation result to add warnings to
   * @private
   */
  addTestFactsWarnings(data, result) {
    if (!Array.isArray(data)) return;

    // Check for duplicate IDs
    const ids = data.map(fact => fact.id);
    const duplicateIds = ids.filter((id, index) => ids.indexOf(id) !== index);
    if (duplicateIds.length > 0) {
      result.warnings.push(`Duplicate fact IDs found: ${[...new Set(duplicateIds)].join(', ')}`);
    }

    // Check complexity distribution
    const complexityCount = data.reduce((acc, fact) => {
      acc[fact.complexity] = (acc[fact.complexity] || 0) + 1;
      return acc;
    }, {});

    const simpleCount = complexityCount.simple || 0;
    const complexCount = complexityCount.complex || 0;
    const total = data.length;

    if (simpleCount === 0) {
      result.warnings.push('No simple facts found - consider adding simple facts for balanced testing');
    } else if (complexCount === 0) {
      result.warnings.push('No complex facts found - consider adding complex facts for comprehensive testing');
    } else {
      const simpleRatio = simpleCount / total;
      if (simpleRatio < 0.3 || simpleRatio > 0.7) {
        result.warnings.push(`Unbalanced complexity distribution: ${simpleCount} simple, ${complexCount} complex (consider 30-70% simple facts)`);
      }
    }

    // Check for very short or very long facts
    const shortFacts = data.filter(fact => fact.fact.length < 10);
    const longFacts = data.filter(fact => fact.fact.length > 500);
    
    if (shortFacts.length > 0) {
      result.warnings.push(`${shortFacts.length} facts are very short (<10 characters) - may not provide meaningful testing`);
    }
    
    if (longFacts.length > 0) {
      result.warnings.push(`${longFacts.length} facts are very long (>500 characters) - may impact memory performance`);
    }
  }

  /**
   * Add business logic warnings for LLM response data
   * 
   * @param {Object} data - LLM response data
   * @param {Object} result - Validation result to add warnings to
   * @private
   */
  addLLMResponseWarnings(data, result) {
    // Check for empty or very short responses
    if (data.content && data.content.length < 5) {
      result.warnings.push('LLM response is very short - may indicate generation issues');
    }

    // Check for very long responses
    if (data.content && data.content.length > 2000) {
      result.warnings.push('LLM response is very long - may impact performance and memory usage');
    }

    // Check for missing metadata
    if (!data.metadata || Object.keys(data.metadata).length === 0) {
      result.warnings.push('LLM response metadata is missing or empty - consider adding model and timing information');
    }

    // Check for suspicious content patterns
    if (data.content && data.content.includes('I cannot') && data.content.includes('I am not able')) {
      result.warnings.push('LLM response contains refusal patterns - may indicate prompt issues');
    }
  }

  /**
   * Add business logic warnings for conversation log data
   * 
   * @param {Array} data - Conversation log data
   * @param {Object} result - Validation result to add warnings to
   * @private
   */
  addConversationLogWarnings(data, result) {
    if (!Array.isArray(data) || data.length === 0) return;

    // Check for conversation flow issues
    let consecutiveUserMessages = 0;
    let consecutiveAssistantMessages = 0;
    let maxConsecutiveUser = 0;
    let maxConsecutiveAssistant = 0;

    for (const message of data) {
      if (message.role === 'user') {
        consecutiveUserMessages++;
        consecutiveAssistantMessages = 0;
        maxConsecutiveUser = Math.max(maxConsecutiveUser, consecutiveUserMessages);
      } else if (message.role === 'assistant') {
        consecutiveAssistantMessages++;
        consecutiveUserMessages = 0;
        maxConsecutiveAssistant = Math.max(maxConsecutiveAssistant, consecutiveAssistantMessages);
      }
    }

    if (maxConsecutiveUser > 2) {
      result.warnings.push(`Found ${maxConsecutiveUser} consecutive user messages - may indicate conversation flow issues`);
    }

    if (maxConsecutiveAssistant > 2) {
      result.warnings.push(`Found ${maxConsecutiveAssistant} consecutive assistant messages - may indicate conversation flow issues`);
    }

    // Check for timestamp ordering
    const timestamps = data.map(msg => msg.timestamp).filter(ts => ts);
    for (let i = 1; i < timestamps.length; i++) {
      if (timestamps[i] < timestamps[i - 1]) {
        result.warnings.push('Conversation messages are not in chronological order');
        break;
      }
    }

    // Check for very short messages
    const shortMessages = data.filter(msg => msg.content && msg.content.length < 3);
    if (shortMessages.length > 0) {
      result.warnings.push(`${shortMessages.length} messages are very short (<3 characters) - may impact conversation quality`);
    }
  }

  /**
   * Add business logic warnings for test results data
   * 
   * @param {Object} data - Test results data
   * @param {Object} result - Validation result to add warnings to
   * @private
   */
  addTestResultsWarnings(data, result) {
    // Check for suspicious scores
    if (data.scores) {
      if (data.scores.overall === 0) {
        result.warnings.push('Overall score is 0% - may indicate evaluation issues');
      } else if (data.scores.overall === 100) {
        result.warnings.push('Overall score is 100% - verify evaluation accuracy');
      }

      // Check for large score discrepancies
      if (data.scores.simple !== undefined && data.scores.complex !== undefined) {
        const scoreDiff = Math.abs(data.scores.simple - data.scores.complex);
        if (scoreDiff > 50) {
          result.warnings.push(`Large score difference between simple (${data.scores.simple}%) and complex (${data.scores.complex}%) facts`);
        }
      }
    }

    // Check evaluation results consistency
    if (data.evaluationResults && Array.isArray(data.evaluationResults)) {
      const scores = data.evaluationResults.map(r => r.score).filter(s => typeof s === 'number');
      if (scores.length > 0) {
        const avgScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
        const calculatedOverall = (avgScore / 10) * 100; // Convert 0-10 to percentage
        
        if (data.scores && data.scores.overall && Math.abs(calculatedOverall - data.scores.overall) > 5) {
          result.warnings.push('Overall score does not match calculated average from evaluation results');
        }
      }
    }

    // Check for missing performance metrics
    if (!data.performanceMetrics) {
      result.warnings.push('Performance metrics are missing - consider including timing and resource usage data');
    }
  }

  /**
   * Validate multiple data objects with different schemas
   * 
   * @param {Object} dataObjects - Object containing different data types to validate
   * @param {Array} dataObjects.testFacts - Test facts data
   * @param {Object} dataObjects.llmResponse - LLM response data
   * @param {Array} dataObjects.conversationLog - Conversation log data
   * @param {Object} dataObjects.testResults - Test results data
   * @returns {Object} Combined validation results
   * 
   * @example
   * const results = validator.validateMultiple({
   *   testFacts: factsData,
   *   conversationLog: logData,
   *   testResults: resultsData
   * });
   * console.log('All valid:', results.allValid);
   */
  validateMultiple(dataObjects) {
    const results = {
      allValid: true,
      validationResults: {},
      summary: {
        totalValidations: 0,
        successfulValidations: 0,
        failedValidations: 0,
        totalErrors: 0,
        totalWarnings: 0
      }
    };

    const validationMethods = {
      testFacts: this.validateTestFacts.bind(this),
      llmResponse: this.validateLLMResponse.bind(this),
      conversationLog: this.validateConversationLog.bind(this),
      testResults: this.validateTestResults.bind(this)
    };

    for (const [dataType, data] of Object.entries(dataObjects)) {
      if (data !== undefined && data !== null && validationMethods[dataType]) {
        const validationResult = validationMethods[dataType](data);
        results.validationResults[dataType] = validationResult;
        results.summary.totalValidations++;

        if (validationResult.isValid) {
          results.summary.successfulValidations++;
        } else {
          results.summary.failedValidations++;
          results.allValid = false;
        }

        results.summary.totalErrors += validationResult.errors.length;
        results.summary.totalWarnings += validationResult.warnings.length;
      }
    }

    defaultLogger.info('Multiple data validation completed', {
      allValid: results.allValid,
      summary: results.summary
    });

    return results;
  }

  /**
   * Get validation statistics and schema information
   * 
   * @returns {Object} Validator statistics and schema information
   */
  getValidatorInfo() {
    return {
      schemas: {
        testFacts: {
          id: testFactsSchema.$id,
          version: testFactsSchema.version || '1.0.0',
          description: testFactsSchema.description || 'Test facts validation schema'
        },
        llmResponse: {
          id: llmResponseSchema.$id,
          version: llmResponseSchema.version || '1.0.0',
          description: llmResponseSchema.description || 'LLM response validation schema'
        },
        conversationLog: {
          id: conversationLogSchema.$id,
          version: conversationLogSchema.version || '1.0.0',
          description: conversationLogSchema.description || 'Conversation log validation schema'
        },
        testResults: {
          id: testResultsSchema.$id,
          version: testResultsSchema.version || '1.0.0',
          description: testResultsSchema.description || 'Test results validation schema'
        }
      },
      customFormats: ['uuid', 'model-name', 'complexity-level', 'score', 'percentage', 'timestamp'],
      ajvVersion: Ajv.version || 'unknown'
    };
  }
}

module.exports = SchemaValidator;