/**
 * Data Migration Utilities for Schema Evolution
 * 
 * This module provides utilities for migrating data between different schema versions
 * to support backward compatibility and schema evolution.
 */

const fs = require('fs').promises;
const path = require('path');
const SchemaValidator = require('./schemaValidator');

/**
 * Migration error class
 */
class MigrationError extends Error {
  constructor(message, details = {}) {
    super(message);
    this.name = 'MigrationError';
    this.details = details;
  }
}

/**
 * Schema version definitions
 */
const SCHEMA_VERSIONS = {
  TEST_FACTS: {
    '1.0.0': 'Initial version with basic fact structure',
    '1.1.0': 'Added optional category and tags fields',
    '1.2.0': 'Added metadata and validation fields'
  },
  TEST_RESULTS: {
    '1.0.0': 'Initial version with basic results',
    '1.1.0': 'Added performance metrics',
    '1.2.0': 'Added memory contexts and analysis',
    '1.3.0': 'Enhanced metadata and configuration tracking'
  },
  CONVERSATION_LOG: {
    '1.0.0': 'Basic conversation entries',
    '1.1.0': 'Added metadata and timing information',
    '1.2.0': 'Enhanced with fact tracking and context'
  }
};

/**
 * Data Migration Manager
 */
class DataMigrationManager {
  constructor() {
    this.validator = new SchemaValidator();
    this.migrations = this.initializeMigrations();
  }

  /**
   * Initialize migration functions
   * @returns {Object} Migration functions organized by data type and version
   */
  initializeMigrations() {
    return {
      testFacts: {
        '1.0.0_to_1.1.0': this.migrateTestFacts_1_0_to_1_1.bind(this),
        '1.1.0_to_1.2.0': this.migrateTestFacts_1_1_to_1_2.bind(this)
      },
      testResults: {
        '1.0.0_to_1.1.0': this.migrateTestResults_1_0_to_1_1.bind(this),
        '1.1.0_to_1.2.0': this.migrateTestResults_1_1_to_1_2.bind(this),
        '1.2.0_to_1.3.0': this.migrateTestResults_1_2_to_1_3.bind(this)
      },
      conversationLog: {
        '1.0.0_to_1.1.0': this.migrateConversationLog_1_0_to_1_1.bind(this),
        '1.1.0_to_1.2.0': this.migrateConversationLog_1_1_to_1_2.bind(this)
      }
    };
  }

  /**
   * Detect schema version of data
   * @param {string} dataType - Type of data (testFacts, testResults, conversationLog)
   * @param {*} data - Data to analyze
   * @returns {string} Detected schema version
   */
  detectSchemaVersion(dataType, data) {
    switch (dataType) {
      case 'testFacts':
        return this.detectTestFactsVersion(data);
      case 'testResults':
        return this.detectTestResultsVersion(data);
      case 'conversationLog':
        return this.detectConversationLogVersion(data);
      default:
        throw new MigrationError(`Unknown data type: ${dataType}`);
    }
  }

  /**
   * Migrate data to the latest schema version
   * @param {string} dataType - Type of data
   * @param {*} data - Data to migrate
   * @param {string} fromVersion - Source version (optional, will be detected)
   * @param {string} toVersion - Target version (optional, defaults to latest)
   * @returns {Object} Migration result with migrated data
   */
  migrateToLatest(dataType, data, fromVersion = null, toVersion = null) {
    const detectedVersion = fromVersion || this.detectSchemaVersion(dataType, data);
    const targetVersion = toVersion || this.getLatestVersion(dataType);
    
    if (detectedVersion === targetVersion) {
      return {
        success: true,
        fromVersion: detectedVersion,
        toVersion: targetVersion,
        data: data,
        migrationsApplied: []
      };
    }

    const migrationPath = this.findMigrationPath(dataType, detectedVersion, targetVersion);
    if (!migrationPath) {
      throw new MigrationError(`No migration path found from ${detectedVersion} to ${targetVersion} for ${dataType}`);
    }

    return this.executeMigrationPath(dataType, data, migrationPath);
  }

  /**
   * Migrate data file and save the result
   * @param {string} filePath - Path to the data file
   * @param {string} dataType - Type of data
   * @param {Object} options - Migration options
   * @returns {Promise<Object>} Migration result
   */
  async migrateFile(filePath, dataType, options = {}) {
    const {
      backupOriginal = true,
      outputPath = null,
      targetVersion = null
    } = options;

    try {
      // Read and parse the file
      const fileContent = await fs.readFile(filePath, 'utf8');
      const data = JSON.parse(fileContent);

      // Create backup if requested
      if (backupOriginal) {
        const backupPath = `${filePath}.backup.${Date.now()}`;
        await fs.writeFile(backupPath, fileContent);
      }

      // Perform migration
      const migrationResult = this.migrateToLatest(dataType, data, null, targetVersion);

      // Save migrated data
      const savePath = outputPath || filePath;
      const migratedContent = JSON.stringify(migrationResult.data, null, 2);
      await fs.writeFile(savePath, migratedContent);

      return {
        ...migrationResult,
        filePath: savePath,
        backupCreated: backupOriginal
      };

    } catch (error) {
      throw new MigrationError(`Failed to migrate file ${filePath}: ${error.message}`, {
        filePath,
        dataType,
        originalError: error
      });
    }
  }

  /**
   * Validate migrated data
   * @param {string} dataType - Type of data
   * @param {*} data - Migrated data
   * @param {string} version - Expected version
   * @returns {Object} Validation result
   */
  validateMigratedData(dataType, data, version) {
    try {
      let validationResult;
      
      // Use appropriate validation method based on data type
      switch (dataType) {
        case 'testFacts':
          validationResult = this.validator.validateTestFacts(data);
          break;
        case 'testResults':
          validationResult = this.validator.validateTestResults(data);
          break;
        case 'conversationLog':
          validationResult = this.validator.validateConversationLog(data);
          break;
        default:
          throw new Error(`Unknown data type for validation: ${dataType}`);
      }
      
      return {
        isValid: validationResult.isValid,
        errors: validationResult.errors || [],
        warnings: validationResult.warnings || [],
        version: version
      };
    } catch (error) {
      return {
        isValid: false,
        errors: [{ message: `Validation failed: ${error.message}` }],
        warnings: [],
        version: version
      };
    }
  }

  /**
   * Get latest version for a data type
   * @param {string} dataType - Type of data
   * @returns {string} Latest version
   */
  getLatestVersion(dataType) {
    const versions = Object.keys(SCHEMA_VERSIONS[this.getSchemaKey(dataType)] || {});
    return versions.sort().pop() || '1.0.0';
  }

  /**
   * Find migration path between versions
   * @param {string} dataType - Type of data
   * @param {string} fromVersion - Source version
   * @param {string} toVersion - Target version
   * @returns {Array} Migration path
   */
  findMigrationPath(dataType, fromVersion, toVersion) {
    const migrations = this.migrations[dataType] || {};
    const path = [];
    
    let currentVersion = fromVersion;
    while (currentVersion !== toVersion) {
      const nextMigration = Object.keys(migrations).find(key => 
        key.startsWith(`${currentVersion}_to_`)
      );
      
      if (!nextMigration) {
        return null; // No path found
      }
      
      path.push(nextMigration);
      currentVersion = nextMigration.split('_to_')[1];
    }
    
    return path;
  }

  /**
   * Execute migration path
   * @param {string} dataType - Type of data
   * @param {*} data - Data to migrate
   * @param {Array} migrationPath - Path of migrations to apply
   * @returns {Object} Migration result
   */
  executeMigrationPath(dataType, data, migrationPath) {
    let currentData = JSON.parse(JSON.stringify(data)); // Deep copy
    const migrationsApplied = [];
    
    for (const migrationKey of migrationPath) {
      const migrationFunction = this.migrations[dataType][migrationKey];
      if (!migrationFunction) {
        throw new MigrationError(`Migration function not found: ${migrationKey}`);
      }
      
      try {
        currentData = migrationFunction(currentData);
        migrationsApplied.push(migrationKey);
      } catch (error) {
        throw new MigrationError(`Migration ${migrationKey} failed: ${error.message}`, {
          migrationKey,
          originalError: error
        });
      }
    }
    
    const fromVersion = migrationPath[0].split('_to_')[0];
    const toVersion = migrationPath[migrationPath.length - 1].split('_to_')[1];
    
    return {
      success: true,
      fromVersion,
      toVersion,
      data: currentData,
      migrationsApplied
    };
  }

  /**
   * Migration functions for test facts
   */

  detectTestFactsVersion(data) {
    if (!Array.isArray(data) || data.length === 0) {
      return '1.0.0';
    }
    
    const sample = data[0];
    
    if (sample.metadata || sample.validation) {
      return '1.2.0';
    } else if (sample.category || sample.tags) {
      return '1.1.0';
    } else {
      return '1.0.0';
    }
  }

  migrateTestFacts_1_0_to_1_1(data) {
    return data.map(fact => ({
      ...fact,
      category: this.inferCategory(fact),
      tags: this.inferTags(fact)
    }));
  }

  migrateTestFacts_1_1_to_1_2(data) {
    return data.map(fact => ({
      ...fact,
      metadata: {
        created: new Date().toISOString(),
        version: '1.2.0',
        source: 'migration'
      },
      validation: {
        reviewed: false,
        lastReview: null
      }
    }));
  }

  /**
   * Migration functions for test results
   */

  detectTestResultsVersion(data) {
    if (!data || typeof data !== 'object') {
      return '1.0.0';
    }
    
    if (data.analysis && data.performanceMetrics && data.memoryContexts) {
      return '1.3.0';
    } else if (data.memoryContexts && data.analysis) {
      return '1.2.0';
    } else if (data.performanceMetrics) {
      return '1.1.0';
    } else {
      return '1.0.0';
    }
  }

  migrateTestResults_1_0_to_1_1(data) {
    return {
      ...data,
      performanceMetrics: {
        testExecution: {
          startTime: new Date().toISOString(),
          endTime: new Date().toISOString(),
          duration: 0,
          factsProcessed: data.evaluationResults?.length || 0,
          messagesGenerated: data.conversationLog?.length || 0
        },
        apiUsage: {
          totalCalls: 0,
          callsByModel: {},
          totalTokens: 0,
          tokensByModel: {},
          averageResponseTime: 0,
          failedCalls: 0
        }
      }
    };
  }

  migrateTestResults_1_1_to_1_2(data) {
    return {
      ...data,
      memoryContexts: data.evaluationResults?.map(result => ({
        factId: result.factId,
        context: result.memoryContext || 'Context not available in legacy format',
        timestamp: new Date().toISOString(),
        memoryType: data.metadata?.memoryType || 'unknown'
      })) || [],
      analysis: {
        trends: {},
        insights: ['Migrated from legacy format'],
        recommendations: ['Review migrated data for accuracy']
      }
    };
  }

  migrateTestResults_1_2_to_1_3(data) {
    return {
      ...data,
      metadata: {
        ...data.metadata,
        migrationHistory: [{
          fromVersion: '1.2.0',
          toVersion: '1.3.0',
          timestamp: new Date().toISOString(),
          reason: 'Schema evolution'
        }],
        dataQuality: {
          completeness: 1.0,
          accuracy: 0.95,
          consistency: 0.98
        }
      }
    };
  }

  /**
   * Migration functions for conversation log
   */

  detectConversationLogVersion(data) {
    if (!Array.isArray(data) || data.length === 0) {
      return '1.0.0';
    }
    
    const sample = data[0];
    
    if (sample.factId !== undefined || sample.context) {
      return '1.2.0';
    } else if (sample.metadata || sample.timestamp) {
      return '1.1.0';
    } else {
      return '1.0.0';
    }
  }

  migrateConversationLog_1_0_to_1_1(data) {
    return data.map((entry, index) => ({
      ...entry,
      timestamp: new Date(Date.now() + index * 1000).toISOString(),
      metadata: {
        migrated: true,
        originalIndex: index
      }
    }));
  }

  migrateConversationLog_1_1_to_1_2(data) {
    return data.map(entry => ({
      ...entry,
      factId: this.inferFactId(entry),
      context: this.inferContext(entry)
    }));
  }

  /**
   * Helper methods for migration
   */

  getSchemaKey(dataType) {
    const keyMap = {
      testFacts: 'TEST_FACTS',
      testResults: 'TEST_RESULTS',
      conversationLog: 'CONVERSATION_LOG'
    };
    return keyMap[dataType] || dataType.toUpperCase();
  }

  getSchemaNameForDataType(dataType) {
    const schemaMap = {
      testFacts: 'testFactsFile',
      testResults: 'testResults',
      conversationLog: 'conversationLog'
    };
    return schemaMap[dataType] || dataType;
  }

  inferCategory(fact) {
    const text = fact.fact.toLowerCase();
    
    if (text.includes('name') || text.includes('called')) {
      return 'personal';
    } else if (text.includes('work') || text.includes('job') || text.includes('company')) {
      return 'professional';
    } else if (text.includes('like') || text.includes('favorite') || text.includes('prefer')) {
      return 'preference';
    } else if (text.includes('live') || text.includes('address') || text.includes('city')) {
      return 'location';
    } else {
      return 'general';
    }
  }

  inferTags(fact) {
    const tags = [];
    const text = fact.fact.toLowerCase();
    
    if (text.includes('age') || text.includes('old')) tags.push('age');
    if (text.includes('name')) tags.push('identity');
    if (text.includes('work') || text.includes('job')) tags.push('career');
    if (text.includes('education') || text.includes('school') || text.includes('university') || text.includes('college') || text.includes('harvard') || text.includes('went to')) tags.push('education');
    if (text.includes('family') || text.includes('parent')) tags.push('family');
    
    return tags;
  }

  inferFactId(entry) {
    // Simple heuristic to infer which fact might be related to this entry
    // In a real implementation, this would be more sophisticated
    return entry.cycle <= 2 ? 1 : Math.floor((entry.cycle - 1) / 10) + 1;
  }

  inferContext(entry) {
    return `Inferred context for cycle ${entry.cycle}`;
  }

  /**
   * Create migration report
   * @param {Object} migrationResult - Result from migration
   * @returns {Object} Formatted migration report
   */
  createMigrationReport(migrationResult) {
    return {
      timestamp: new Date().toISOString(),
      success: migrationResult.success,
      migration: {
        fromVersion: migrationResult.fromVersion,
        toVersion: migrationResult.toVersion,
        migrationsApplied: migrationResult.migrationsApplied
      },
      dataInfo: {
        recordCount: Array.isArray(migrationResult.data) 
          ? migrationResult.data.length 
          : Object.keys(migrationResult.data).length,
        sizeEstimate: JSON.stringify(migrationResult.data).length
      },
      validation: migrationResult.validation || null,
      recommendations: this.generateMigrationRecommendations(migrationResult)
    };
  }

  /**
   * Generate recommendations after migration
   * @param {Object} migrationResult - Migration result
   * @returns {Array} Recommendations
   */
  generateMigrationRecommendations(migrationResult) {
    const recommendations = [];
    
    if (migrationResult.migrationsApplied.length > 1) {
      recommendations.push('Multiple migrations were applied - review data carefully');
    }
    
    if (migrationResult.fromVersion.startsWith('1.0')) {
      recommendations.push('Data migrated from early version - verify inferred fields');
    }
    
    recommendations.push('Run validation tests on migrated data');
    recommendations.push('Update any dependent systems to use new schema');
    
    return recommendations;
  }
}

module.exports = {
  DataMigrationManager,
  MigrationError,
  SCHEMA_VERSIONS
};